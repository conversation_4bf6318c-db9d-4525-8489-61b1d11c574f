from django.http import HttpResponse
from apps.account.models import User

ALLOWED_URLS = [
    "/login", "/admin", "telegram-sentry", 'bot-runner', "auth/google/", "/elalhabib/submit/", '/pay', 'paypal',
    'robots.txt', "/.well-known/", "about", "/download", 'dont-kill/'
]



def language_middleware(get_response):
    def middleware(request):
        request.LANGUAGE_CODE = request.GET.get('language_code') or request.LANGUAGE_CODE

        response = get_response(request)

        return response

    return middleware

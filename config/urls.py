"""
URL configuration for backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.conf.urls.i18n import i18n_patterns
from utils import UploadTmpMedia
# from django.conf.urls import url
from django.http import JsonResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from drf_yasg.utils import swagger_auto_schema

from dynamic_preferences.registries import global_preferences_registry
from utils import absolute_url


from utils.admin import project_admin_site, HomeView

from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions
import requests
from filer import views
from django.contrib.admin.views.decorators import staff_member_required
from apps.api.views import CustomSwaggerView, SwaggerTokenAuthView, clear_swagger_auth, generate_user_token
from apps.api.views.swagger_documentation import SwaggerBasedDocumentationView


# Restricted schema view for admin users only
schema_view = get_schema_view(
    openapi.Info(
        title="Qatreh API",
        default_version='v1',
        description="Qatreh Financial Platform API Documentation",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="Proprietary License"),
    ),
    public=False,
    permission_classes=(permissions.IsAdminUser,),
)

# Token-authenticated schema view for Swagger UI
token_schema_view = get_schema_view(
    openapi.Info(
        title="Qatreh API",
        default_version='v1',
        description="Qatreh Financial Platform API Documentation",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=[permissions.IsAuthenticated],
)

@api_view(http_method_names=['GET'])
def assetlinks(request):
    response_data = [
      {
        "relation": ["delegate_permission/common.handle_all_urls"],
        "target": {
          "namespace": "android_app",
          "package_name": "com.qatreh.app",
          "sha256_cert_fingerprints": ["30:5A:05:06:8A:33:57:D2:5F:C8:62:5D:DD:61:E4:83:2D:64:9E:17:98:AD:49:6F:7F:B9:A1:5B:51:DB:2D:C2", "87:50:69:CF:07:C7:7C:BC:14:8D:FD:5E:F6:7B:25:49:FA:FC:50:B1:67:81:18:6E:38:2F:40:DF:E7:62:D0:AF"]
        }
      }
    ]
    return JsonResponse(response_data, safe=False)

def oneapi_translate(request):
    dist_lang = request.GET.get('dist_lang')
    q = request.GET.get('q')
    url = f"https://one-api.ir/translate/?token=169700:6485a38c34b00&action=google&lang={dist_lang}&q={q}"
    try:
        data = requests.get(url).json()
    except Exception as e:
        data = {}

    return JsonResponse(data)

api_patterns = [
    path('', include('apps.api.urls')),

    path('account/', include('apps.account.urls')),
    path('requests/', include('apps.request.urls')),
    path('deposits/', include('apps.deposit.urls', namespace='deposit')),
    path('region/', include('apps.region.urls', namespace='region')),

    path('', include('apps.lottery.urls')),
    path('issues/', include('apps.issues.urls')),
    path('tickets/', include('apps.ticket.urls')),
    path('voting/', include('apps.voting.urls')),
    path('', include('apps.payment.urls')),
    path('transactions/', include('apps.transaction.urls')),
    path('preferences/', include('apps.api.preference_urls')),

    path('upload-tmp-media/', UploadTmpMedia.as_view()),

]


urlpatterns = [
    path('.well-known/assetlinks.json', assetlinks),
    path("", include('apps.homepage.urls')),  # Homepage as root URL
    path("admin/", HomeView.as_view(), name="home"),
    path("i18n/", include("django.conf.urls.i18n")),

    # path('admin/', admin.site.urls),
    path('api/', include(api_patterns)),
    # path('test/', include('apps.api.urls'))
    path('oneapi-translation/', oneapi_translate),
    path('admin/filer/', include('filer.urls')),

    # Add direct path for payment verification
    path('payments/verify/', include('apps.payment.verify_urls')),
]
# Protected swagger URL patterns
swagger_urlpatterns = [
    path('swagger-auth/', SwaggerTokenAuthView.as_view(), name='swagger-token-auth'),
    path('swagger-auth/clear/', clear_swagger_auth, name='clear-swagger-auth'),
    path('swagger-auth/generate-token/', generate_user_token, name='generate-user-token'),
    # Admin-only schema endpoints (original)
    re_path(r'^swagger(?P<format>\.json|\.yaml)$',
            staff_member_required(schema_view.without_ui(cache_timeout=0)),
            name='schema-json'),

    # Token-authenticated schema endpoints for Swagger UI
    re_path(r'^api/swagger(?P<format>\.json|\.yaml)$',
            token_schema_view.without_ui(cache_timeout=0),
            name='schema-json-api'),

    path('swagger/', CustomSwaggerView.as_view(), name='schema-swagger-ui'),
    re_path(r'^redoc/$',
            staff_member_required(schema_view.with_ui('redoc', cache_timeout=0)),
            name='schema-redoc'),
]

urlpatterns+= i18n_patterns(
    path("admin/", project_admin_site.urls),
    path('docs/', SwaggerBasedDocumentationView.as_view(), name='docs-index'),
    *swagger_urlpatterns,
    path('admin/filer/', include('filer.urls')),
)

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)





# import sentry_sdk

from .base import *
from celery.schedules import crontab
import os

DEBUG = False

# Static files settings for production
# Use container paths for Docker
# STATIC_ROOT = '/usr/src/app/static/staticfiles'
# MEDIA_ROOT = '/usr/src/app/static/media'

# Override STATICFILES_DIRS for production to ensure all static files are collected
# STATICFILES_DIRS = [
#     os.path.join(BASE_DIR, 'static'),
# ]

# Ensure static files are served correctly
# STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

CORS_ALLOW_ALL_ORIGINS = False
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')



CELERY_BROKER_URL = env("REDIS_URL")
CELERY_RESULT_BACKEND = env("REDIS_URL")
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Asia/Tehran'
CELERY_BROKER_TRANSPORT = 'redis'

# زمان‌بندی Celery Beat

# CELERY_BEAT_SCHEDULE = {

# }

CORS_ALLOWED_ORIGINS = [
    'https://hamjeb.nwhco.ir',    
    'http://qatreh.nwhco.ir',
    'https://qatreh.com',
    "https://hammjeeb.ir"
]

CACHES = {
    'default': {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": env("REDIS_URL"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    },
    'memory': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        'TIMEOUT': 5000,
    },
}
import sentry_sdk

sentry_sdk.init(
    dsn="https://<EMAIL>/8225",
    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for performance monitoring.
    traces_sample_rate=1.0,
    # Note: profiles_sample_rate is only supported in Sentry SDK v1.18.0+
    # If you want to use profiling, please upgrade the Sentry SDK
)

REST_FRAMEWORK['DEFAULT_RENDERER_CLASSES'] = [
    'rest_framework.renderers.JSONRenderer',
]


LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "require_debug_false": {
            "()": "django.utils.log.RequireDebugFalse",
        },
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        },
    },
    "formatters": {
        "django.server": {
            "()": "django.utils.log.ServerFormatter",
            "format": "[{server_time}] {message}",
            "style": "{",
        }
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
        },
        "django.server": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "django.server",
        },
        "mail_admins": {
            "level": "ERROR",
            "filters": ["require_debug_false"],
            "class": "django.utils.log.AdminEmailHandler",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console", "mail_admins"],
            "level": "INFO",
        },
        "django.server": {
            "handlers": ["django.server"],
            "level": "INFO",
            "propagate": False,
        },
    },
}
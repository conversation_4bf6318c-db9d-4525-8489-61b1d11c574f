"""
Django settings for backend project.

Generated by 'django-admin startproject' using Django 5.0.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""
import os
from pathlib import Path

import environ
from django.utils.translation import gettext_lazy as _
from django.templatetags.static import static
from django.urls import reverse_lazy


env = environ.Env(
    # set casting, default value
    # DEBUG=(bool, False)
)
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

ALLOWED_HOSTS = env('DJANGO_ALLOWED_HOSTS').split(',')


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-7=3it+m^28^+0c1*9-*c*6g3ej63sz(97rq1^mp=!6e(mhmysq'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

X_FRAME_OPTIONS = 'SAMEORIGIN'

LOCAL_APPS = [
    'apps.account.apps.AccountConfig',
    'apps.api.apps.ApiConfig',
    'apps.region.apps.RegionConfig',
    'apps.deposit.apps.DepositConfig',
    'apps.request.apps.RequestConfig',
    'apps.transaction.apps.TransactionConfig',
    'apps.issues.apps.IssuesConfig',
    'apps.ticket.apps.TicketConfig',
    'apps.lottery.apps.LotteryConfig',
    'apps.voting.apps.VotingConfig',
    'apps.loan.apps.LoanConfig',
    'apps.payment.apps.PaymentConfig',
    'apps.homepage.apps.HomepageConfig',
    'dynamic_preferences',
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'rest_framework.authtoken',
    'drf_yasg',
    'easy_thumbnails',
    'phonenumber_field',
    'dj_language',
    'dj_filer',
    'ajaxdatatable',
    'corsheaders',
    'django_filters',
    
]
INSTALLED_APPS = [
    "unfold",
    "unfold.contrib.filters",
    "unfold.contrib.import_export",
    "unfold.contrib.guardian",
    "unfold.contrib.simple_history",
    "unfold.contrib.forms",
    "unfold.contrib.inlines",
    'django.contrib.admin',
    
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',  # Added for humanize template tags
    *THIRD_PARTY_APPS,
    *LOCAL_APPS,
    
]
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',  # این خط را نگه دارید تا احراز هویت پیش‌فرض کار کند
    'apps.account.custom_user_login.CustomLoginBackend',  # مسیر به کلاس سفارشی خود
]

REDIS_URL = env('REDIS_URL')



# OTP service key mile
OTP_SERIVCE_KEY = "33213d78f1234e99b81f94eefda77e45"

# Kavenegar SMS service settings
KAVENEGAR_API_KEY = env('KAVENEGAR_API_KEY')
KAVENEGAR_OTP_TEMPLATE = env('KAVENEGAR_OTP_TEMPLATE')
KAVENEGAR_SENDER = env('KAVENEGAR_SENDER')

# Zarinpal payment gateway settings
ZARINPAL_MERCHANT_ID = env('ZARINPAL_MERCHANT_ID', default='2e84ce7e-85fc-11ea-9ccd-000c295eb8fc')


PHONENUMBER_DEFAULT_REGION = "IR"
PHONENUMBER_DB_FORMAT = 'INTERNATIONAL'
PHONENUMBER_DEFAULT_FORMAT = 'INTERNATIONAL'

AUTH_USER_MODEL = "account.User"

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'config.language_code_middleware.language_middleware',
    'config.test_auth_middleware.enhanced_auth_middleware',
]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            BASE_DIR / 'templates',
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',

            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'


# django google recaptcha default keys
RECAPTCHA_PUBLIC_KEY = env('captcha_public_key')
RECAPTCHA_PRIVATE_KEY = env('captcha_private_key')

# custom settings
APPS_REORDER = {
    'auth': {
        'icon': 'icon-shield-check',
        'name': 'Authentication'
    },
    'account': {
        # 'icon': 'icon-',
        'name': 'account'

    }
}
# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': env('POSTGRES_DB'),
        'USER': env('POSTGRES_USER'),
        'PASSWORD': env('POSTGRES_PASSWORD'),
        'HOST': env('POSTGRES_HOST'),
        'PORT': env('POSTGRES_PORT'),
        'ATOMIC_REQUESTS': True,
        'TEST': {
            'NAME': 'test_qatreh', 
        },        
    },
    
}


CORS_ALLOW_ALL_ORIGINS = True

THUMBNAIL_ALIASES = {
    '': {
        'icon': {'size': (50, 50), 'crop': True},
        'large': {'size': (1200, 620), 'crop': False},
        'medium': {'size': (545, 545), 'crop': False},
        'small': {'size': (150, 150), 'crop': False},
    },
}

LANGUAGES_MAP = {
    'az': ['az', 'tr', 'fa', 'ar'],
    'tr': ['tr', 'az', 'fa', 'ar'],
    'ru': ['ru', 'az', 'tr', 'fa', 'ar'],
    'ar': ['ar', 'fa'],
    'ur': ['ur', 'en', 'fa', 'ar'],
    'en': ['en', 'ur', 'fa', 'ar'],
    'de': ['de', 'en', 'fr', 'es', 'ar'],
    'fa': ['fa', 'az', 'ar', 'en', 'ur'],

    'fr': ['fr', 'en', 'ar', 'fa'],
    'es': ['es', 'en', 'ar', 'fa'],
    'id': ['id', 'en', 'ar', 'fa'],
    'sw': ['sw', 'en', 'ar', 'fa'],
}
TEST_RUNNER = "config.test_runner.KeepDBDiscoverRunner"

# Invitation Link Domain
INVITATION_LINK_DOMAIN = "https://hammjeeb.ir/link/"

LANGUAGES = [
    ('fa', _('Persian')),
    ('en', _('English')),
]

CELERY_BROKER_URL = env("REDIS_URL")
CELERY_RESULT_BACKEND = env("REDIS_URL")
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TIMEZONE = 'Asia/Tehran'
CELERY_BROKER_TRANSPORT = 'redis'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True

# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators


AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 6,
        }
    },
]


REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 16,
    # 'DEFAULT_AUTHENTICATION_CLASSES': [
    #     'apps.account.auth_back.TokenAuthentication2',
    # ],
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',
        # 'config.custom_token.MultiModelTokenAuthentication',

        # 'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    'EXCEPTION_HANDLER': 'utils.exceptions.exception_handler',
    # 'EXCEPTION_HANDLER': 'django_rest_exception_handler.exception_handlers.exception_handler'
    # 'EXCEPTION_HANDLER': 'drf_pretty_exception_handler.exception_handler',

}
# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'fa'

TIME_ZONE = 'Asia/Tehran'

USE_I18N = True

USE_L10N = True

USE_TZ = False

STATIC_URL = '/static/'
MEDIA_URL = '/media/'

STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]
STATIC_ROOT = os.path.join(BASE_DIR, 'static', 'static')
MEDIA_ROOT = os.path.join(BASE_DIR, 'static', 'media')

# FILER_ADMIN_ICON_SIZES = ('32', '48')

FILER_ENABLE_LOGGING = True
FILER_DEBUG = True
ADMIN_TITLE = 'Hamjib App'
ADMIN_INDEX_TITLE = 'Hamjib Administration'

LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/


# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
FILE_UPLOAD_HANDLERS = [
    'django.core.files.uploadhandler.TemporaryFileUploadHandler',
]

######################################################################
# Sessions
######################################################################
SESSION_ENGINE = "django.contrib.sessions.backends.signed_cookies"
LOGIN_URL = "admin:login"
LOGIN_REDIRECT_URL = reverse_lazy("admin:index")
# STORAGES = {
#     "default": {
#         "BACKEND": "django.core.files.storage.FileSystemStorage",
#     },
#     "staticfiles": {
#         "BACKEND": "whitenoise.storage.CompressedStaticFilesStorage",
#     },
# }
######################################################################
# Unfold
######################################################################
UNFOLD = {
    "SITE_TITLE": _("Hamjib Admin"),
    "SITE_HEADER": _("Hamjib "),
    "SITE_SUBHEADER": _("Hamjib Admin"),
    "SITE_DROPDOWN": [],  # Empty list to remove dropdown items
    "SHOW_VIEW_ON_SITE": False,  # Disable "View on site" button
    "SITE_SYMBOL": "settings",
    "SHOW_HISTORY": True,
    "SHOW_LANGUAGES": True,
    "RTL": True,
    "ENVIRONMENT": "utils.environment_callback",
    "DASHBOARD_CALLBACK": "utils.admin.dashboard_callback",
    "SITE_ICON": {
        "light": lambda request: static("images/img2.svg"),  # light mode
        "dark": lambda request: static("images/img2.svg"),  # dark mode
    },
    "SITE_SYMBOL": "speed",
    "SHOW_BACK_BUTTON": False,
    # "THEME": "light",  
    "LOGIN": {
        "image": lambda request: static("images/img13.jpg"),
    },
    "COLORS": {
        "base": {
            "50": "249 250 251",
            "100": "243 244 246",
            "200": "229 231 235",
            "300": "209 213 219",
            "400": "156 163 175",
            "500": "107 114 128",
            "600": "75 85 99",
            "700": "55 65 81",
            "800": "31 41 55",
            "900": "17 24 39",
            "950": "3 7 18",
        },
        "primary": {
            "50": "232 255 248",     # خیلی روشن و نزدیک به پس‌زمینه‌های روشن
            "100": "204 255 240",
            "200": "153 247 222",
            "300": "102 235 200",
            "400": "51 220 175",
            "500": "0 204 153",       # #00CC99
            "600": "0 184 138",
            "700": "0 153 115",
            "800": "0 122 92",
            "900": "0 92 69",
            "950": "0 61 46",
        },
        "secondary": {
            "50": "240 253 250",
            "100": "204 251 241",
            "200": "153 246 228",
            "300": "94 234 212",
            "400": "45 212 191",
            "500": "1 53 59", 
            "600": "1 43 48",
            "700": "1 36 40",
            "800": "1 30 34",
            "900": "0 26 29",
            "950": "0 13 15",
        },
        # رنگ‌های سفارشی برای حالت دارک
        "dark": {
            "50": "18 26 25",  # پس‌زمینه اصلی با ته‌رنگ سبز
            "100": "22 32 30",  # پس‌زمینه کارت‌ها
            "200": "26 38 36",  # پس‌زمینه هاور
            "300": "30 44 42",  # پس‌زمینه عناصر فعال
            "400": "34 50 48",  # حاشیه‌ها
            "500": "38 56 54",  # جداکننده‌ها
            "600": "42 62 60",  # عناصر برجسته
            "700": "46 68 66",  # دکمه‌های ثانویه
            "800": "50 74 72",  # هایلایت‌ها 
            "900": "54 80 78",  # عناصر برجسته‌تر
        },
        "font": {
            "subtle-light": "var(--color-base-500)",
            "subtle-dark": "var(--color-base-400)",
            "default-light": "var(--color-secondary-500)",  # استفاده از رنگ ثانویه برای متن
            "default-dark": "var(--color-base-300)",
            "important-light": "var(--color-base-900)",
            "important-dark": "255 255 255",  # #FFFFFF - برای متن سفید در دکمه‌ها
        },
    },
    "RADIAL_GRADIENT": {
        "secondary": {
            "colors": ["#7BA3CE", "#165EAB"],
            "stops": ["20%", "80%"],  # تغییر نسبت رنگ‌ها برای کاهش شدت رنگ ثانویه
        },
        "primary": {  # اضافه کردن گرادیان برای رنگ اصلی
            "colors": ["#00CC99", "#008F73"],
            "stops": ["0%", "100%"],
        },
        "dark": {  # گرادیان مدرن برای حالت دارک
            "colors": ["#182625", "#1A2E2C", "#00CC99"],
            "stops": ["0%", "85%", "200%"],
        },
    },
    
    # "STYLES": [
        # lambda request: static("css/styles.css"),
    # ],
    # "SCRIPTS": [
        # lambda request: static("js/chart.min.js"),
    # ],
    "TABS": [
        {
            "page": "accounts",
            "models": ["account.user", 'account.guestadminuser','auth.group'],
            "items": [
                {
                    "title": _("Users"),
                    "icon": "sports_motorsports",
                    "link": reverse_lazy("admin:account_user_changelist"),
                    "active": lambda request: request.path
                    == reverse_lazy("admin:account_user_changelist")
                },
                {
                    "title": _("Guest Users"),
                    "icon": "person_outline",
                    "link": reverse_lazy("admin:account_guestadminuser_changelist"),
                    "active": lambda request: request.path
                    == reverse_lazy("admin:account_guestadminuser_changelist")
                },
            ],
        },
        # {
        #     "page": "deposits",
        #     "models": ["deposit.polldeposit", "deposit.savingdeposit", "deposit.reportingdeposit", "deposit.depositmembership"],
        #     "items": [
        #         {
        #             "title": _("Poll Deposits"),
        #             "icon": "savings",
        #             "link": reverse_lazy("admin:deposit_polldeposit_changelist"),
        #             "active": lambda request: request.path.endswith('/deposit/polldeposit/')
        #         },
        #         {
        #             "title": _("Poll Deposits 2"),
        #             "icon": "savings",
        #             "link": reverse_lazy("admin:deposit_polldeposit_changelist"),
        #             "active": lambda request: request.path.endswith('/deposit/polldeposit/'),
        #             "permission": lambda x: False
        #         },

        #     ]
        # },

    ],
    "SIDEBAR": {
        "show_search": True,
        "show_all_applications": True,
        "navigation": [
            {
                "title": _(""),
                # "separator": True,
                # "collapsible": True,
                "items": [
                    {
                        "title": _("Dashboard"),
                        "icon": "dashboard",
                        "link": reverse_lazy("admin:index"),
                    },
                ],
            },
            {
                "title": _(""),
                "items": [
                    {
                        "title": _("Accounts"),
                        "icon": "person",
                        "link": reverse_lazy("admin:account_user_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                ]
            },
            {
                "title": _("Regions"),
                "icon": "globe",
                "collapsible": False,
                "items": [
                    {
                        "title": _("Regions"),
                        "icon": "public",
                        "link": reverse_lazy("admin:region_region_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    {
                        "title": _("User Regions"),
                        "icon": "group_work",
                        "link": reverse_lazy("admin:region_userregion_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    {
                        "title": _("Invitation Links"),
                        "icon": "link",
                        "link": reverse_lazy("admin:region_invitationlink_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                ]
            },
            {
                "title": _("Deposits"),
                "icon": "savings",
                "collapsible": False,
                "items": [
                    {
                        "title": _("Poll Deposits"),
                        "icon": "orbit",
                        "link": reverse_lazy("admin:deposit_polldeposit_changelist"),
                        # "link": reverse_lazy("admin:deposit"),
                    },
                    {
                        "title": _("Reporting Deposits"),
                        "icon": "poll",
                        "link": reverse_lazy("admin:deposit_reportingdeposit_changelist"),
                        # "link": reverse_lazy("admin:deposit"),
                    },
                    {
                        "title": _("Saving Deposits"),
                        "icon": "account_balance",
                        "link": reverse_lazy("admin:deposit_savingdeposit_changelist"),
                        # "link": reverse_lazy("admin:deposit"),
                    },
                ],
            },
            {
                "title": _("Requests"),
                "icon": "request_page",
                "collapsible": False,
                "items": [
                    {
                        "title": _("Create Deposit Requests"),
                        "icon": "add_circle",
                        "link": reverse_lazy("admin:request_requestcreatedeposit_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    {
                        "title": _("Join Deposit Requests"),
                        "icon": "group_add",
                        "link": reverse_lazy("admin:request_requestjoindeposit_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    {
                        "title": _("Loan Requests"),
                        "icon": "payments",
                        "link": reverse_lazy("admin:request_loanrequest_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    {
                        "title": _("Withdrawal Requests"),
                        "icon": "money_off",
                        "link": reverse_lazy("admin:request_withdrawalrequest_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                ],
            },
            {
                "title": _("Payments"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Payments"),
                        "icon": "wallet",
                        "link": reverse_lazy("admin:payment_payment_changelist"),
                    },
                    {
                        "title": _("Transctions"),
                        "icon": "add_card",
                        "link": reverse_lazy("admin:transaction_transaction_changelist"),
                    },                    
                ],
            },
            {
                "title": _(""),
                "items": [
                    {
                        "title": _("Issues"),
                        "icon": "report_problem",
                        "link": reverse_lazy("admin:issues_issuereport_changelist"),
                    },
                ],
            },
            {
                "title": _(""),
                "items": [
                    {
                        "title": _("Tickets"),
                        "icon": "confirmation_number", 
                        "link": reverse_lazy("admin:ticket_ticket_changelist"),
                    },
                ],
            },
                        {
                "title": "",
                "items": [
                    {
                        "title": _("Global Preferences"),
                        "icon": "settings",
                        "link": reverse_lazy("admin:dynamic_preferences_globalpreferencemodel_changelist"),
                    },
                    # You can add more preference sections here
                ],
            },
            {
                "title": _(""),
                "items": [
                    {
                        "title": _("App Versions"),
                        "icon": "system_update",
                        "link": reverse_lazy("admin:homepage_appversion_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                ],
            },
            {
                "title": _(""),
                "items": [
                    {
                        "title": _("Guides"),
                        "icon": "help_outline",
                        "link": reverse_lazy("admin:api_guide_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                ],
            },
        ],
    },
}
UNFOLD_STUDIO_DEFAULT_FRAGMENT = "color-schemes"
UNFOLD_STUDIO_PERMISSION = lambda request: request.user.is_authenticated

PLAUSIBLE_DOMAIN = env("PLAUSIBLE_DOMAIN")

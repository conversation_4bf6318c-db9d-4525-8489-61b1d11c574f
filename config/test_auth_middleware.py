from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.urls import reverse
from rest_framework.authtoken.models import Token
from apps.account.models import User
from django.contrib.auth import get_user_model

User = get_user_model()

def enhanced_auth_middleware(get_response):
    """
    Enhanced middleware for API authentication with admin restriction
    Handles both session-based and token-based authentication for documentation
    """
    def middleware(request):
        protected_paths = ["/swagger", "/redoc", "/docs"]
        is_protected_path = any(path in request.path for path in protected_paths)

        if is_protected_path:
            # Check if user is authenticated staff member
            if request.user.is_authenticated and request.user.is_staff:
                # Provide API token for authenticated staff users
                if 'swagger_token' in request.session:
                    token = request.session['swagger_token']
                    request.META['HTTP_AUTHORIZATION'] = f"Token {token}"
                elif not request.META.get('HTTP_AUTHORIZATION'):
                    # Try to get or create token for current user
                    try:
                        token, _ = Token.objects.get_or_create(user=request.user)
                        request.META['HTTP_AUTHORIZATION'] = f"Token {token.key}"
                    except Exception:
                        # Fallback to default admin user token
                        admin_user = User.objects.filter(is_staff=True).first()
                        if admin_user:
                            token, _ = Token.objects.get_or_create(user=admin_user)
                            request.META['HTTP_AUTHORIZATION'] = f"Token {token.key}"
            else:
                # For non-authenticated users or non-staff users, redirect to admin login
                if not request.user.is_authenticated:
                    return redirect(f"{reverse('admin:login')}?next={request.path}")
                elif not request.user.is_staff:
                    # User is authenticated but not staff - show permission denied
                    raise PermissionDenied("You must be a staff member to access this resource.")

        return get_response(request)

    return middleware

# Keep the old middleware for backward compatibility
def test_auth_middleware(get_response):
    """
    Legacy middleware - kept for backward compatibility
    Use enhanced_auth_middleware for new implementations
    """
    return enhanced_auth_middleware(get_response)

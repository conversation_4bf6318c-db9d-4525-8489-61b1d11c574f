{% extends 'admin/base_site.html' %}
{% load i18n %}

{% block title %}
    {{ title }} | {{ site_title|default:_('Django site admin') }}
{% endblock %}

{% block extrastyle %}
{{ block.super }}
<style>
    body {
        background-color: #f5f7fa;
        font-family: '<PERSON>azi<PERSON>', 'Tahoma', sans-serif;
    }
    
    .documentation-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 20px;
        background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(46, 204, 113, 0.05) 100%);
        min-height: calc(100vh - 60px);
    }
    
    .documentation-container {
        max-width: 1200px;
        width: 100%;
        margin: 0 auto;
        padding: 40px;
        background-color: #fff;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        border-radius: 20px;
        direction: rtl;
        text-align: right;
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.05);
    }
    
    .documentation-container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 8px;
        background: linear-gradient(90deg, #3498db, #2ecc71);
    }
    
    .documentation-header {
        text-align: center;
        margin-bottom: 50px;
        position: relative;
    }
    
    .documentation-header::after {
        content: "";
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        width: 150px;
        height: 4px;
        background: linear-gradient(90deg, #3498db, #2ecc71);
        border-radius: 2px;
    }
    
    .section {
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    
    .section:last-child {
        border-bottom: none;
    }
    
    h1 {
        color: #2c3e50;
        font-size: 2.8rem;
        margin-bottom: 10px;
        text-align: center;
        font-weight: 800;
        position: relative;
        padding-bottom: 15px;
        letter-spacing: -0.5px;
    }
    
    .subtitle {
        color: #7f8c8d;
        font-size: 1.2rem;
        text-align: center;
        margin-bottom: 40px;
        font-weight: 400;
    }
    
    h2 {
        color: #2c3e50;
        font-size: 1.9rem;
        margin: 45px 0 25px;
        padding-bottom: 12px;
        border-bottom: 2px solid #f0f0f0;
        font-weight: 700;
        position: relative;
    }
    
    h2::before {
        content: "";
        position: absolute;
        bottom: -2px;
        right: 0;
        width: 80px;
        height: 2px;
        background: linear-gradient(90deg, #3498db, #2ecc71);
    }
    
    h3 {
        color: #3498db;
        font-size: 1.5rem;
        margin: 30px 0 15px;
        font-weight: 600;
        position: relative;
        padding-right: 15px;
    }
    
    h3::before {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 20px;
        background: linear-gradient(180deg, #3498db, #2ecc71);
        border-radius: 3px;
    }
    
    h4 {
        color: #34495e;
        font-size: 1.2rem;
        margin: 20px 0 10px;
        font-weight: 600;
    }
    
    p {
        margin-bottom: 15px;
        text-align: justify;
    }
    
    ul, ol {
        margin-bottom: 15px;
        padding-right: 20px;
    }
    
    li {
        margin-bottom: 8px;
    }
    
    .highlight-box {
        background-color: #f8f9fa;
        border-radius: 16px;
        padding: 30px;
        margin: 40px 0;
        position: relative;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        border: 1px solid rgba(52, 152, 219, 0.1);
        overflow: hidden;
    }
    
    .highlight-box::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 6px;
        background: linear-gradient(180deg, #3498db, #2ecc71);
        border-radius: 0 16px 16px 0;
    }
    
    .highlight-box h3 {
        color: #3498db;
        margin-top: 0;
        margin-bottom: 20px;
        padding-right: 0;
    }
    
    .highlight-box h3::before {
        display: none;
    }
    
    .process-steps {
        position: relative;
    }
    
    .process-steps::before {
        content: "";
        position: absolute;
        top: 25px;
        right: 25px;
        bottom: 25px;
        width: 2px;
        background: linear-gradient(180deg, #3498db, #2ecc71);
        z-index: 1;
    }
    
    .process-step {
        display: flex;
        margin-bottom: 30px;
        align-items: flex-start;
        background-color: #fff;
        padding: 25px;
        border-radius: 16px;
        box-shadow: 0 8px 20px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        z-index: 2;
    }
    
    .process-step:hover {
        transform: translateY(-8px) translateX(5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        border-color: rgba(52, 152, 219, 0.2);
    }
    
    .step-number {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        margin-left: 25px;
        flex-shrink: 0;
        box-shadow: 0 8px 15px rgba(52, 152, 219, 0.3);
        font-size: 1.2rem;
    }
    
    .step-content {
        flex-grow: 1;
        padding-top: 5px;
    }
    
    .step-content strong {
        color: #3498db;
        font-weight: 600;
    }
    
    .feature-card {
        background-color: #ffffff;
        border-radius: 16px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        border: 1px solid rgba(52, 152, 219, 0.1);
        position: relative;
        overflow: hidden;
    }
    
    .feature-card::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 6px;
        background: linear-gradient(180deg, #3498db, #2ecc71);
        border-radius: 0 16px 16px 0;
    }
    
    .feature-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        border-color: rgba(52, 152, 219, 0.3);
    }
    
    .feature-card h3 {
        color: #2c3e50;
        margin-top: 0;
        font-weight: 700;
        border-bottom: 1px solid rgba(0,0,0,0.1);
        padding-bottom: 15px;
        margin-bottom: 20px;
        padding-right: 0;
    }
    
    .feature-card h3::before {
        display: none;
    }
    
    .feature-icon {
        display: inline-block;
        width: 60px;
        height: 60px;
        background-color: rgba(52, 152, 219, 0.1);
        border-radius: 50%;
        text-align: center;
        line-height: 60px;
        margin-bottom: 20px;
        color: #3498db;
        font-size: 24px;
    }
    
    .diagram {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
        text-align: center;
    }
    
    .diagram img {
        max-width: 100%;
    }
    
    .table-container {
        overflow-x: auto;
        margin: 30px 0;
        border-radius: 16px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        border: 1px solid rgba(0,0,0,0.05);
    }
    
    table {
        width: 100%;
        border-collapse: collapse;
        background-color: white;
    }
    
    th, td {
        padding: 15px 20px;
        text-align: right;
        border-bottom: 1px solid #eee;
    }
    
    th {
        background: linear-gradient(90deg, rgba(52, 152, 219, 0.1), rgba(46, 204, 113, 0.1));
        color: #2c3e50;
        font-weight: 600;
        position: relative;
    }
    
    th:first-child {
        border-top-right-radius: 16px;
    }
    
    th:last-child {
        border-top-left-radius: 16px;
    }
    
    tr:last-child td:first-child {
        border-bottom-right-radius: 16px;
    }
    
    tr:last-child td:last-child {
        border-bottom-left-radius: 16px;
    }
    
    tr:hover {
        background-color: rgba(52, 152, 219, 0.03);
    }
    
    tr:last-child td {
        border-bottom: none;
    }
    
    .table-highlight {
        background-color: rgba(46, 204, 113, 0.05);
    }
    
    .note, .warning, .success, .info {
        padding: 25px;
        margin: 30px 0;
        border-radius: 16px;
        position: relative;
        box-shadow: 0 8px 20px rgba(0,0,0,0.05);
        border: 1px solid rgba(0,0,0,0.05);
        overflow: hidden;
    }
    
    .note::before, .warning::before, .success::before, .info::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 6px;
        border-radius: 0 16px 16px 0;
    }
    
    .note {
        background-color: rgba(255, 193, 7, 0.05);
    }
    
    .note::before {
        background: linear-gradient(180deg, #ffc107, #ff9800);
    }
    
    .warning {
        background-color: rgba(244, 67, 54, 0.05);
    }
    
    .warning::before {
        background: linear-gradient(180deg, #f44336, #e53935);
    }
    
    .success {
        background-color: rgba(76, 175, 80, 0.05);
    }
    
    .success::before {
        background: linear-gradient(180deg, #4caf50, #43a047);
    }
    
    .info {
        background-color: rgba(33, 150, 243, 0.05);
    }
    
    .info::before {
        background: linear-gradient(180deg, #2196f3, #1e88e5);
    }
    
    .note h4, .warning h4, .success h4, .info h4 {
        margin-top: 0;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .note h4 {
        color: #ff9800;
    }
    
    .warning h4 {
        color: #e53935;
    }
    
    .success h4 {
        color: #43a047;
    }
    
    .info h4 {
        color: #1e88e5;
    }
    
    /* Code blocks */
    code {
        background-color: rgba(0,0,0,0.05);
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        color: #e74c3c;
    }
    
    pre {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 16px;
        overflow-x: auto;
        border: 1px solid rgba(0,0,0,0.05);
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        margin: 20px 0;
    }
    
    pre code {
        background-color: transparent;
        padding: 0;
        color: #2c3e50;
        display: block;
        line-height: 1.6;
    }
    
    /* Model diagram */
    .model-diagram {
        background-color: #fff;
        border-radius: 16px;
        padding: 30px;
        margin: 30px 0;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        border: 1px solid rgba(0,0,0,0.05);
    }
    
    .model-box {
        border: 2px solid #3498db;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        background-color: #f8f9fa;
    }
    
    .model-name {
        background-color: #3498db;
        color: white;
        padding: 8px 15px;
        border-radius: 6px;
        margin: -25px 20px 15px 0;
        display: inline-block;
        font-weight: 600;
    }
    
    .model-field {
        padding: 8px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .model-field:last-child {
        border-bottom: none;
    }
    
    .field-name {
        font-weight: 600;
        color: #2c3e50;
    }
    
    .field-type {
        color: #7f8c8d;
        font-size: 0.9em;
    }
    
    .relationship-line {
        border-left: 2px dashed #3498db;
        height: 30px;
        margin-right: 20px;
    }
    
    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 12px;
        height: 12px;
    }
    
    ::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #3498db, #2ecc71);
        border-radius: 10px;
        border: 3px solid #f5f7fa;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #2980b9, #27ae60);
    }
    
    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(30px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes slideInRight {
        from { opacity: 0; transform: translateX(30px); }
        to { opacity: 1; transform: translateX(0); }
    }
    
    .section {
        animation: fadeIn 0.8s ease-out forwards;
    }
    
    .section:nth-child(2) { animation-delay: 0.2s; }
    .section:nth-child(3) { animation-delay: 0.4s; }
    .section:nth-child(4) { animation-delay: 0.6s; }
    .section:nth-child(5) { animation-delay: 0.8s; }
    
    .feature-card {
        animation: slideInRight 0.6s ease-out forwards;
        animation-delay: calc(0.2s * var(--card-index, 0));
    }
    
    /* Navigation */
    .doc-nav {
        position: sticky;
        top: 20px;
        background-color: white;
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        margin-bottom: 30px;
        border: 1px solid rgba(0,0,0,0.05);
    }
    
    .doc-nav-title {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .doc-nav-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }
    
    .doc-nav-item {
        margin-bottom: 10px;
    }
    
    .doc-nav-link {
        color: #7f8c8d;
        text-decoration: none;
        display: block;
        padding: 8px 10px;
        border-radius: 8px;
        transition: all 0.2s ease;
    }
    
    .doc-nav-link:hover {
        background-color: rgba(52, 152, 219, 0.05);
        color: #3498db;
    }
    
    .doc-nav-link.active {
        background-color: rgba(52, 152, 219, 0.1);
        color: #3498db;
        font-weight: 600;
    }
    
    @media (max-width: 768px) {
        .documentation-wrapper {
            padding: 20px 10px;
        }
        
        .documentation-container {
            padding: 25px 15px;
            border-radius: 12px;
        }
        
        h1 {
            font-size: 2rem;
        }
        
        h2 {
            font-size: 1.5rem;
        }
        
        h3 {
            font-size: 1.2rem;
        }
        
        .process-step {
            flex-direction: column;
        }
        
        .step-number {
            margin-left: 0;
            margin-bottom: 15px;
        }
    }
</style>
{% endblock %}

{% block contentwrap %}
<div class="documentation-wrapper">
    <div class="documentation-container">
        <div class="documentation-header">
            <h1>مستندات جامع پروژه قطره</h1>
            <p class="subtitle">سیستم مدیریت صندوق‌های قرض‌الحسنه و پس‌انداز</p>
        </div>
        
        <div class="doc-nav">
            <div class="doc-nav-title">فهرست مطالب</div>
            <ul class="doc-nav-list">
                <li class="doc-nav-item"><a href="#intro" class="doc-nav-link active">معرفی پروژه</a></li>
                <li class="doc-nav-item"><a href="#deposit-types" class="doc-nav-link">انواع صندوق‌ها</a></li>
                <li class="doc-nav-item"><a href="#lifecycle" class="doc-nav-link">چرخه حیات صندوق</a></li>
                <li class="doc-nav-item"><a href="#user-management" class="doc-nav-link">مدیریت کاربران</a></li>
                <li class="doc-nav-item"><a href="#payment-system" class="doc-nav-link">سیستم پرداخت</a></li>
                <li class="doc-nav-item"><a href="#data-models" class="doc-nav-link">مدل‌های داده</a></li>
                <li class="doc-nav-item"><a href="#api" class="doc-nav-link">API ها</a></li>
                <li class="doc-nav-item"><a href="#benefits" class="doc-nav-link">مزایا</a></li>
                <li class="doc-nav-item"><a href="#roadmap" class="doc-nav-link">نقشه راه آینده</a></li>
            </ul>
        </div>
    
    <div id="intro" class="section">
        <h2>۱. معرفی پروژه</h2>
        <p>
            پروژه «قطره» یک سیستم مدیریت صندوق‌های قرض‌الحسنه و پس‌انداز است که به کاربران امکان می‌دهد به صورت گروهی، منابع مالی خود را مدیریت کنند. این سیستم با هدف تسهیل فرآیندهای مالی گروهی، شفافیت در تراکنش‌ها و ایجاد بستری امن برای پس‌انداز و وام‌دهی طراحی شده است.
        </p>
        
        <div class="highlight-box">
            <h3>چشم‌انداز پروژه</h3>
            <p>
                ایجاد بستری دیجیتال برای مدیریت صندوق‌های مالی گروهی که دسترسی به منابع مالی را برای همه افراد جامعه آسان‌تر کرده و فرهنگ پس‌انداز و کمک متقابل را ترویج دهد.
            </p>
        </div>
    </div>
    
    <div id="deposit-types" class="section">
        <h2>۲. انواع صندوق‌ها</h2>
        <p>
            در سیستم قطره، سه نوع اصلی صندوق وجود دارد که هر کدام برای نیازهای متفاوت طراحی شده‌اند:
        </p>
        
        <div class="feature-card">
            <h3>صندوق قرعه‌کشی (Poll Deposit)</h3>
            <p>
                در این نوع صندوق، اعضا به صورت ماهانه مبلغی مشخص را پرداخت می‌کنند و در هر دوره، قرعه‌کشی انجام می‌شود تا یکی از اعضا کل مبلغ جمع‌آوری شده را دریافت کند. این نوع صندوق برای افرادی مناسب است که می‌خواهند در یک بازه زمانی مشخص به یک مبلغ قابل توجه دست پیدا کنند.
            </p>
            <p>
                <strong>ویژگی‌های کلیدی:</strong>
            </p>
            <ul>
                <li>تعیین مبلغ هر سهم (unit_amount)</li>
                <li>تعیین تعداد ماه‌های قرعه‌کشی (lottery_month_count)</li>
                <li>تعیین حداکثر تعداد اعضا (max_members_count)</li>
                <li>تعیین حداکثر سهم قابل درخواست توسط هر عضو (max_unit_per_request)</li>
                <li>تعیین تاریخ شروع قرعه‌کشی (initial_lottery_date)</li>
            </ul>
        </div>
        
        <div class="feature-card">
            <h3>صندوق پس‌انداز (Saving Deposit)</h3>
            <p>
                صندوق پس‌انداز به اعضا امکان می‌دهد مبالغی را به صورت منظم پس‌انداز کنند و در صورت نیاز، از این صندوق وام دریافت کنند. این نوع صندوق برای افرادی مناسب است که به دنبال ایجاد عادت پس‌انداز و دسترسی به منابع مالی در مواقع ضروری هستند.
            </p>
            <p>
                <strong>ویژگی‌های کلیدی:</strong>
            </p>
            <ul>
                <li>تعیین مبلغ هر سهم (unit_amount)</li>
                <li>تعیین مدت اعتبار صندوق (validity_duration)</li>
                <li>تعیین تاریخ شروع صندوق (start_date)</li>
                <li>تعیین دوره پرداخت به ماه (payment_cycle)</li>
            </ul>
        </div>
        
        <div class="feature-card">
            <h3>صندوق گزارش‌دهی (Reporting Deposit)</h3>
            <p>
                صندوق گزارش‌دهی برای مدیریت و نظارت بر هزینه‌ها و درآمدهای گروهی طراحی شده است. این نوع صندوق امکان ثبت و پیگیری تراکنش‌ها را فراهم می‌کند و برای مدیریت مالی پروژه‌های گروهی یا سازمان‌های کوچک مناسب است.
            </p>
            <p>
                <strong>ویژگی‌های کلیدی:</strong>
            </p>
            <ul>
                <li>ثبت و پیگیری تراکنش‌ها</li>
                <li>گزارش‌گیری از وضعیت مالی</li>
                <li>مدیریت هزینه‌ها و درآمدها</li>
            </ul>
        </div>
    </div>
    
    <div id="lifecycle" class="section">
        <h2>۳. چرخه حیات صندوق</h2>
        
        <h3>۳.۱. ایجاد صندوق</h3>
        <div class="process-step">
            <div class="step-number">۱</div>
            <div class="step-content">
                <p>
                    <strong>درخواست ایجاد صندوق:</strong> کاربر با تکمیل فرم مربوطه، درخواست ایجاد صندوق را ثبت می‌کند. در این فرم، نوع صندوق (قرعه‌کشی، پس‌انداز یا گزارش‌دهی)، عنوان، توضیحات و سایر پارامترهای مورد نیاز مشخص می‌شود.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۲</div>
            <div class="step-content">
                <p>
                    <strong>بررسی و تأیید:</strong> درخواست ایجاد صندوق توسط مدیران سیستم بررسی می‌شود و در صورت تأیید، صندوق ایجاد می‌شود. کاربر درخواست‌دهنده به عنوان مالک صندوق (Owner) تعیین می‌شود.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۳</div>
            <div class="step-content">
                <p>
                    <strong>تنظیم پارامترها:</strong> پس از ایجاد صندوق، پارامترهای آن مانند مبلغ هر سهم، حداکثر تعداد اعضا، دوره پرداخت و سایر موارد تنظیم می‌شود.
                </p>
            </div>
        </div>
        
        <h3>۳.۲. عضویت در صندوق</h3>
        <div class="process-step">
            <div class="step-number">۱</div>
            <div class="step-content">
                <p>
                    <strong>درخواست عضویت:</strong> کاربران می‌توانند با مشاهده صندوق‌های موجود، درخواست عضویت در صندوق مورد نظر خود را ارسال کنند. در این درخواست، تعداد سهم‌های مورد نظر و مبلغ قسط ماهانه مشخص می‌شود.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۲</div>
            <div class="step-content">
                <p>
                    <strong>بررسی ظرفیت:</strong> سیستم بررسی می‌کند که آیا صندوق ظرفیت پذیرش تعداد سهم‌های درخواستی را دارد یا خیر. این بررسی با استفاده از تابع <code>has_capacity</code> انجام می‌شود.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۳</div>
            <div class="step-content">
                <p>
                    <strong>تأیید عضویت:</strong> در صورت وجود ظرفیت و تأیید مالک صندوق، کاربر به عنوان عضو صندوق پذیرفته می‌شود و رکورد مربوط به عضویت او در جدول <code>DepositMembership</code> ایجاد می‌شود.
                </p>
            </div>
        </div>
        
        <h3>۳.۳. مدیریت سررسیدها</h3>
        <div class="process-step">
            <div class="step-number">۱</div>
            <div class="step-content">
                <p>
                    <strong>ایجاد سررسیدها:</strong> برای هر صندوق، سررسیدهای پرداخت (Due Dates) تعریف می‌شود که مشخص می‌کند اعضا در چه تاریخ‌هایی باید پرداخت‌های خود را انجام دهند.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۲</div>
            <div class="step-content">
                <p>
                    <strong>پیگیری پرداخت‌ها:</strong> سیستم وضعیت پرداخت‌های هر سررسید را پیگیری می‌کند و در صورت تکمیل شدن پرداخت‌های یک سررسید، آن را به عنوان تکمیل‌شده (is_completed) علامت‌گذاری می‌کند.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۳</div>
            <div class="step-content">
                <p>
                    <strong>غیرفعال‌سازی سررسیدهای گذشته:</strong> سیستم به صورت خودکار سررسیدهای مربوط به ماه‌های گذشته را که هنوز تکمیل نشده‌اند، غیرفعال می‌کند. این کار با استفاده از تابع <code>disable_previous_month_due_dates</code> انجام می‌شود.
                </p>
            </div>
        </div>
        
        <h3>۳.۴. قرعه‌کشی (برای صندوق‌های قرعه‌کشی)</h3>
        <div class="process-step">
            <div class="step-number">۱</div>
            <div class="step-content">
                <p>
                    <strong>بررسی شرایط قرعه‌کشی:</strong> سیستم بررسی می‌کند که آیا شرایط برگزاری قرعه‌کشی فراهم است یا خیر. این شرایط شامل تکمیل شدن پرداخت‌های سررسید جاری و رسیدن به تاریخ قرعه‌کشی است.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۲</div>
            <div class="step-content">
                <p>
                    <strong>انجام قرعه‌کشی:</strong> در صورت فراهم بودن شرایط، قرعه‌کشی انجام می‌شود و یکی از اعضای صندوق به عنوان برنده انتخاب می‌شود. اطلاعات مربوط به قرعه‌کشی در جدول <code>DepositLottery</code> ثبت می‌شود.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۳</div>
            <div class="step-content">
                <p>
                    <strong>اعلام نتیجه:</strong> نتیجه قرعه‌کشی به اعضای صندوق اعلام می‌شود و برنده می‌تواند مبلغ جمع‌آوری شده را دریافت کند.
                </p>
            </div>
        </div>
        
        <h3>۳.۵. درخواست وام (برای صندوق‌های پس‌انداز)</h3>
        <div class="process-step">
            <div class="step-number">۱</div>
            <div class="step-content">
                <p>
                    <strong>ثبت درخواست وام:</strong> اعضای صندوق می‌توانند با تکمیل فرم مربوطه، درخواست وام خود را ثبت کنند. در این فرم، مبلغ وام، تعداد اقساط، روز پرداخت اقساط و توضیحات مشخص می‌شود.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۲</div>
            <div class="step-content">
                <p>
                    <strong>بررسی و تأیید:</strong> درخواست وام توسط مالک صندوق یا مدیران آن بررسی می‌شود و در صورت تأیید، وام به متقاضی پرداخت می‌شود.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۳</div>
            <div class="step-content">
                <p>
                    <strong>ایجاد اقساط:</strong> پس از تأیید وام، اقساط آن به صورت خودکار ایجاد می‌شود و در جدول <code>LoanInstallment</code> ثبت می‌شود. هر قسط شامل شماره قسط، مبلغ، تاریخ سررسید و وضعیت پرداخت است.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۴</div>
            <div class="step-content">
                <p>
                    <strong>پیگیری پرداخت اقساط:</strong> سیستم وضعیت پرداخت اقساط را پیگیری می‌کند و در صورت عدم پرداخت به موقع، وام را به عنوان معوق (overdue) علامت‌گذاری می‌کند.
                </p>
            </div>
        </div>
        
        <h3>۳.۶. رأی‌گیری</h3>
        <div class="process-step">
            <div class="step-number">۱</div>
            <div class="step-content">
                <p>
                    <strong>ایجاد رأی‌گیری:</strong> مالک صندوق یا مدیران آن می‌توانند برای تصمیم‌گیری در مورد موضوعات مختلف، رأی‌گیری ایجاد کنند. در هنگام ایجاد رأی‌گیری، عنوان، توضیحات، مهلت رأی‌گیری و گزینه‌های رأی مشخص می‌شود.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۲</div>
            <div class="step-content">
                <p>
                    <strong>شرکت در رأی‌گیری:</strong> اعضای صندوق می‌توانند در رأی‌گیری شرکت کنند و گزینه مورد نظر خود را انتخاب کنند. هر عضو فقط یک بار می‌تواند در هر رأی‌گیری شرکت کند.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۳</div>
            <div class="step-content">
                <p>
                    <strong>اعلام نتیجه:</strong> پس از پایان مهلت رأی‌گیری، نتیجه آن اعلام می‌شود و تصمیم‌گیری بر اساس آن انجام می‌شود.
                </p>
            </div>
        </div>
    </div>
    
    <div id="user-management" class="section">
        <h2>۴. مدیریت کاربران و دسترسی‌ها</h2>
        
        <h3>۴.۱. انواع کاربران</h3>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>نوع کاربر</th>
                        <th>توضیحات</th>
                        <th>دسترسی‌ها</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>مهمان (Guest)</td>
                        <td>کاربرانی که هنوز عضو هیچ صندوقی نشده‌اند</td>
                        <td>مشاهده صندوق‌های عمومی، درخواست عضویت</td>
                    </tr>
                    <tr>
                        <td>عضو (Member)</td>
                        <td>کاربرانی که عضو یک یا چند صندوق هستند</td>
                        <td>مشاهده صندوق‌های خود، پرداخت اقساط، درخواست وام، شرکت در رأی‌گیری</td>
                    </tr>
                    <tr>
                        <td>مالک (Owner)</td>
                        <td>کاربرانی که صندوق ایجاد کرده‌اند</td>
                        <td>تمام دسترسی‌های عضو، به علاوه مدیریت صندوق، تأیید درخواست‌ها، برگزاری قرعه‌کشی</td>
                    </tr>
                    <tr>
                        <td>مدیر (Admin)</td>
                        <td>کاربرانی که توسط مالک به عنوان مدیر تعیین شده‌اند</td>
                        <td>تمام دسترسی‌های عضو، به علاوه برخی از دسترسی‌های مدیریتی</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <h3>۴.۲. مناطق (Regions)</h3>
        <p>
            در سیستم قطره، کاربران می‌توانند در مناطق مختلف عضو شوند. هر منطقه می‌تواند شامل چندین صندوق باشد و هر کاربر می‌تواند عضو یک منطقه باشد. این ویژگی امکان مدیریت صندوق‌ها بر اساس موقعیت جغرافیایی یا سازمانی را فراهم می‌کند.
        </p>
        
        <div class="process-step">
            <div class="step-number">۱</div>
            <div class="step-content">
                <p>
                    <strong>ایجاد منطقه:</strong> کاربران می‌توانند منطقه جدید ایجاد کنند و به عنوان مالک آن تعیین شوند.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۲</div>
            <div class="step-content">
                <p>
                    <strong>دعوت کاربران:</strong> مالک منطقه می‌تواند کاربران دیگر را به منطقه دعوت کند. برای هر کاربر، یک کد دعوت منحصر به فرد ایجاد می‌شود.
                </p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">۳</div>
            <div class="step-content">
                <p>
                    <strong>عضویت در منطقه:</strong> کاربران می‌توانند با استفاده از کد دعوت، به منطقه ملحق شوند.
                </p>
            </div>
        </div>
    </div>
    
    <div id="payment-system" class="section">
        <h2>۵. سیستم پرداخت و تراکنش‌ها</h2>
        
        <h3>۵.۱. انواع تراکنش‌ها</h3>
        <p>
            در سیستم قطره، تراکنش‌های مختلفی وجود دارد که هر کدام برای هدف خاصی طراحی شده‌اند:
        </p>
        
        <ul>
            <li><strong>پرداخت قسط:</strong> پرداخت‌های ماهانه اعضا به صندوق</li>
            <li><strong>پرداخت وام:</strong> پرداخت مبلغ وام به عضو درخواست‌کننده</li>
            <li><strong>پرداخت قسط وام:</strong> پرداخت اقساط وام توسط وام‌گیرنده</li>
            <li><strong>برداشت:</strong> برداشت وجه از صندوق توسط اعضا</li>
        </ul>
        
        <h3>۵.۲. درخواست برداشت</h3>
        <p>
            اعضای صندوق می‌توانند در صورت نیاز، درخواست برداشت وجه از صندوق را ارسال کنند. این درخواست شامل مبلغ برداشت، شماره شبا و دلیل برداشت است. درخواست برداشت توسط مالک صندوق یا مدیران آن بررسی می‌شود و در صورت تأیید، مبلغ مورد نظر به حساب متقاضی واریز می‌شود.
        </p>
    </div>
    
    <div class="section">
        <h2>۶. سیستم پشتیبانی و ارتباط با کاربران</h2>
        
        <h3>۶.۱. تیکت‌ها</h3>
        <p>
            کاربران می‌توانند در صورت بروز مشکل یا داشتن سؤال، از طریق سیستم تیکت با پشتیبانی ارتباط برقرار کنند. هر تیکت شامل عنوان، توضیحات و اولویت است. تیکت‌ها توسط تیم پشتیبانی بررسی و پاسخ داده می‌شوند.
        </p>
        
        <h3>۶.۲. اعلان‌ها</h3>
        <p>
            سیستم قطره از طریق اعلان‌ها، اطلاعات مهم را به کاربران اطلاع می‌دهد. این اعلان‌ها می‌توانند شامل موارد زیر باشند:
        </p>
        
        <ul>
            <li>یادآوری پرداخت قسط</li>
            <li>اعلام نتیجه قرعه‌کشی</li>
            <li>تأیید یا رد درخواست‌ها</li>
            <li>اعلام رأی‌گیری جدید</li>
            <li>اعلام پایان مهلت رأی‌گیری</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>۷. مزایای استفاده از سیستم قطره</h2>
        
        <div class="feature-card">
            <h3>مدیریت آسان صندوق‌های مالی</h3>
            <p>
                سیستم قطره با ارائه ابزارهای مدیریتی پیشرفته، فرآیند مدیریت صندوق‌های مالی را بسیار آسان می‌کند. مالکان صندوق می‌توانند به راحتی اعضا، پرداخت‌ها، وام‌ها و سایر موارد را مدیریت کنند.
            </p>
        </div>
        
        <div class="feature-card">
            <h3>شفافیت در تراکنش‌ها</h3>
            <p>
                تمام تراکنش‌ها در سیستم قطره به صورت شفاف ثبت و نگهداری می‌شوند و اعضای صندوق می‌توانند گزارش‌های مربوط به تراکنش‌ها را مشاهده کنند. این شفافیت باعث افزایش اعتماد اعضا به صندوق می‌شود.
            </p>
        </div>
        
        <div class="feature-card">
            <h3>دسترسی آسان</h3>
            <p>
                کاربران می‌توانند از طریق وب یا اپلیکیشن موبایل، در هر زمان و هر مکان به سیستم قطره دسترسی داشته باشند و امور مربوط به صندوق خود را انجام دهند.
            </p>
        </div>
        
        <div class="feature-card">
            <h3>امنیت بالا</h3>
            <p>
                سیستم قطره از استانداردهای امنیتی بالایی برخوردار است و اطلاعات کاربران و تراکنش‌ها به صورت امن نگهداری می‌شوند.
            </p>
        </div>
        
        <div class="feature-card">
            <h3>تصمیم‌گیری دموکراتیک</h3>
            <p>
                سیستم رأی‌گیری قطره امکان تصمیم‌گیری دموکراتیک در مورد موضوعات مختلف را فراهم می‌کند و باعث افزایش مشارکت اعضا در اداره صندوق می‌شود.
            </p>
        </div>
    </div>
    
    <div class="section">
        <h2>۸. نقشه راه آینده</h2>
        
        <div class="highlight-box">
            <h3>برنامه‌های توسعه آینده</h3>
            <p>
                تیم توسعه قطره برنامه‌های متعددی برای بهبود و گسترش سیستم دارد:
            </p>
            <ul>
                <li>افزودن روش‌های پرداخت جدید</li>
                <li>توسعه اپلیکیشن موبایل با امکانات بیشتر</li>
                <li>افزودن ابزارهای تحلیلی پیشرفته برای مدیریت بهتر صندوق‌ها</li>
                <li>پیاده‌سازی سیستم هوش مصنوعی برای پیش‌بینی رفتار مالی اعضا</li>
                <li>گسترش سیستم به سایر کشورها و زبان‌ها</li>
            </ul>
        </div>
    </div>
    
    <div class="section">
        <h2>۹. جمع‌بندی</h2>
        <p>
            پروژه قطره یک راه‌حل جامع برای مدیریت صندوق‌های مالی گروهی است که با استفاده از فناوری‌های نوین، فرآیندهای سنتی را بهبود بخشیده و دسترسی به منابع مالی را برای همه افراد آسان‌تر کرده است. این سیستم با ارائه انواع مختلف صندوق (قرعه‌کشی، پس‌انداز و گزارش‌دهی)، امکان مدیریت شفاف و کارآمد منابع مالی را فراهم می‌کند.
        </p>
        <p>
            با استفاده از سیستم قطره، کاربران می‌توانند به راحتی صندوق ایجاد کنند، عضو صندوق‌های موجود شوند، در قرعه‌کشی‌ها شرکت کنند، وام دریافت کنند و در تصمیم‌گیری‌های مربوط به صندوق مشارکت داشته باشند. تمام این فرآیندها به صورت دیجیتال و با امنیت بالا انجام می‌شوند.
        </p>
        <p>
            قطره با هدف ترویج فرهنگ پس‌انداز و کمک متقابل، گامی مهم در جهت توسعه اقتصادی و اجتماعی جامعه برداشته است و با برنامه‌های توسعه آینده، به دنبال گسترش خدمات خود و بهبود تجربه کاربری است.
        </p>
    </div>
</div>
</div>
{% endblock %}
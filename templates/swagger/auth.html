<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swagger Authentication - Qatreh API</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #28a745;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }

        body {
            background: var(--primary-gradient);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .auth-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            backdrop-filter: blur(10px);
        }

        .auth-header {
            background: var(--primary-gradient);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .auth-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .auth-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1rem;
        }

        .auth-body {
            padding: 30px;
        }

        .current-status {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid var(--info-color);
        }

        .status-authenticated {
            border-left-color: var(--success-color);
        }

        .status-not-authenticated {
            border-left-color: var(--warning-color);
        }

        .status-title {
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-details {
            color: #666;
            font-size: 0.9rem;
        }

        .user-info-card {
            background: #e8f5e8;
            border: 1px solid #d4edda;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .user-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .user-info-item:last-child {
            margin-bottom: 0;
        }

        .user-info-label {
            font-weight: 600;
            color: #495057;
        }

        .user-info-value {
            color: #28a745;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        .form-section {
            margin-top: 25px;
        }

        .form-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #495057;
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-danger {
            background: var(--danger-color);
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .help-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 25px;
        }

        .help-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #495057;
        }

        .help-text {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .help-steps {
            list-style: none;
            padding: 0;
            margin: 15px 0 0 0;
        }

        .help-steps li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }

        .help-steps li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 5px;
            background: var(--primary-gradient);
            color: white;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .help-steps {
            counter-reset: step-counter;
        }

        .navigation-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .nav-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: #764ba2;
            text-decoration: none;
        }

        @media (max-width: 576px) {
            .auth-container {
                margin: 10px;
            }

            .auth-header {
                padding: 20px;
            }

            .auth-body {
                padding: 20px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .action-buttons .btn {
                width: 100%;
            }

            .navigation-links {
                flex-direction: column;
                text-align: center;
            }
        }

        /* Alert styling */
        .alert {
            border-radius: 8px;
            border: none;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h1><i class="fas fa-key"></i> API Authentication</h1>
            <p>Manage your Swagger UI authentication</p>
        </div>

        <div class="auth-body">
            <!-- Display Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Current Status -->
            <div class="current-status {% if user_info %}status-authenticated{% else %}status-not-authenticated{% endif %}">
                <div class="status-title">
                    {% if user_info %}
                        <i class="fas fa-check-circle text-success"></i>
                        Authentication Status: Active
                    {% else %}
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        Authentication Status: Not Authenticated
                    {% endif %}
                </div>
                
                <div class="status-details">
                    {% if user_info %}
                        You are currently authenticated and can test API endpoints in Swagger UI.
                        
                        <div class="user-info-card">
                            <div class="user-info-item">
                                <span class="user-info-label">Name:</span>
                                <span class="user-info-value">{{ user_info.fullname }}</span>
                            </div>
                            <div class="user-info-item">
                                <span class="user-info-label">Phone:</span>
                                <span class="user-info-value">{{ user_info.phone_number }}</span>
                            </div>
                            {% if user_info.email %}
                            <div class="user-info-item">
                                <span class="user-info-label">Email:</span>
                                <span class="user-info-value">{{ user_info.email }}</span>
                            </div>
                            {% endif %}
                            <div class="user-info-item">
                                <span class="user-info-label">Role:</span>
                                <span class="user-info-value">{{ user_info.user_type }}</span>
                            </div>
                            <div class="user-info-item">
                                <span class="user-info-label">Token:</span>
                                <span class="user-info-value">{{ current_token|slice:":8" }}...{{ current_token|slice:"-8:" }}</span>
                            </div>
                        </div>
                    {% else %}
                        Please enter your API token to authenticate and test API endpoints.
                    {% endif %}
                </div>
            </div>

            {% if not user_info %}
            <!-- Token Authentication Form -->
            <div class="form-section">
                <h3 class="form-title">Enter API Token</h3>
                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="token" class="form-label">API Token (40 characters)</label>
                        <input type="text" class="form-control" id="token" name="token" 
                               placeholder="Enter your 40-character API token" 
                               maxlength="40" required>
                        <div class="form-text">
                            Your API token should be exactly 40 characters long.
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Authenticate
                    </button>
                </form>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="action-buttons">
                {% if user_info %}
                    <a href="{% url 'schema-swagger-ui' %}" class="btn btn-primary">
                        <i class="fas fa-code"></i>
                        Go to Swagger UI
                    </a>
                    <a href="{% url 'clear-swagger-auth' %}" class="btn btn-danger">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                {% endif %}
                
                <form method="post" action="{% url 'generate-user-token' %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i>
                        Generate New Token
                    </button>
                </form>
            </div>

            <!-- Help Section -->
            <div class="help-section">
                <h4 class="help-title">How to get your API Token</h4>
                <p class="help-text">
                    Follow these steps to obtain your API authentication token:
                </p>
                <ol class="help-steps">
                    <li>Make sure you have an active user account in the system</li>
                    <li>Click "Generate New Token" button above to create a new token</li>
                    <li>Copy the generated 40-character token</li>
                    <li>Paste it in the token field and click "Authenticate"</li>
                    <li>You can now test API endpoints in Swagger UI</li>
                </ol>
            </div>

            <!-- Navigation Links -->
            <div class="navigation-links">
                <a href="{% url 'docs-index' %}" class="nav-link">
                    <i class="fas fa-book"></i>
                    Documentation
                </a>
                <a href="{% url 'schema-redoc' %}" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    ReDoc
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Token input validation
        const tokenInput = document.getElementById('token');
        if (tokenInput) {
            tokenInput.addEventListener('input', function() {
                const value = this.value;
                const isValid = value.length === 40;
                
                if (value.length > 0 && value.length !== 40) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                } else if (isValid) {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                } else {
                    this.classList.remove('is-valid', 'is-invalid');
                }
            });
        }
    </script>
</body>
</html>

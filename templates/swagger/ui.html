<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qatreh API - Swagger UI</title>
    
    <!-- Swagger UI CSS -->
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #28a745;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }

        /* Fixed header styles */
        .auth-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 9999;
            background: var(--primary-gradient);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            padding: 15px 20px;
            color: white;
        }

        .auth-banner-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .auth-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .auth-status {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 25px;
            backdrop-filter: blur(5px);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .user-info {
            font-size: 0.9rem;
        }

        .user-name {
            font-weight: 600;
            margin-right: 5px;
        }

        .user-role {
            opacity: 0.8;
            font-size: 0.8rem;
        }

        .auth-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }

        .auth-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .auth-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }

        .auth-btn.danger {
            background: rgba(231, 76, 60, 0.8);
            border-color: rgba(231, 76, 60, 0.9);
        }

        .auth-btn.danger:hover {
            background: rgba(231, 76, 60, 0.9);
        }

        /* Main content adjustment */
        body {
            padding-top: 80px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Swagger UI customizations */
        .swagger-ui .topbar {
            display: none;
        }

        .swagger-ui .info {
            margin: 20px 0;
        }

        .swagger-ui .info .title {
            color: #667eea;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding-top: 120px;
            }

            .auth-banner {
                padding: 10px 15px;
            }

            .auth-banner-content {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .auth-info {
                justify-content: center;
            }

            .auth-actions {
                justify-content: center;
            }

            .nav-links {
                justify-content: center;
                flex-wrap: wrap;
            }

            .nav-link,
            .auth-btn {
                font-size: 0.8rem;
                padding: 6px 12px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding-top: 140px;
            }

            .auth-status {
                flex-direction: column;
                text-align: center;
                gap: 5px;
            }

            .nav-links {
                gap: 8px;
            }

            .nav-link,
            .auth-btn {
                font-size: 0.75rem;
                padding: 5px 10px;
            }
        }

        /* Loading animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #764ba2;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Fixed Authentication Banner -->
    <div class="auth-banner">
        <div class="auth-banner-content">
            <div class="auth-info">
                <div class="auth-status">
                    <div class="status-indicator"></div>
                    {% if user_info %}
                        <div class="user-info">
                            <span class="user-name">{{ user_info.fullname }}</span>
                            <div class="user-role">{{ user_info.user_type }} • {{ user_info.phone_number }}</div>
                        </div>
                    {% else %}
                        <div class="user-info">
                            <span class="user-name">Not Authenticated</span>
                            <div class="user-role">Please authenticate to test APIs</div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="auth-actions">
                <div class="nav-links">
                    <a href="{% url 'docs-index' %}" class="nav-link">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="{% url 'schema-redoc' %}" class="nav-link">
                        <i class="fas fa-file-alt"></i>
                        ReDoc
                    </a>
                </div>

                {% if current_token %}
                    <a href="{% url 'clear-swagger-auth' %}" class="auth-btn danger">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                {% else %}
                    <a href="{% url 'swagger-token-auth' %}" class="auth-btn">
                        <i class="fas fa-key"></i>
                        Authenticate
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Swagger UI Container -->
    <div id="swagger-ui"></div>

    <!-- Swagger UI JavaScript -->
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>

    <script>
        // Hide loading overlay after page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.display = 'none';
            }, 500);
        });

        // Initialize Swagger UI
        const ui = SwaggerUIBundle({
            url: '{{ swagger_spec_url }}',
            dom_id: '#swagger-ui',
            deepLinking: true,
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIStandalonePreset
            ],
            plugins: [
                SwaggerUIBundle.plugins.DownloadUrl
            ],
            layout: "StandaloneLayout",
            requestInterceptor: function(request) {
                // Add authorization header if token exists
                {% if current_token %}
                request.headers['Authorization'] = 'Token {{ current_token }}';
                {% endif %}
                return request;
            },
            responseInterceptor: function(response) {
                // Handle authentication errors
                if (response.status === 401) {
                    console.warn('Authentication required. Please authenticate first.');
                }
                return response;
            },
            onComplete: function() {
                console.log('Swagger UI loaded successfully');
                // Hide loading overlay
                document.getElementById('loadingOverlay').style.display = 'none';
            },
            onFailure: function(error) {
                console.error('Failed to load Swagger UI:', error);
                document.getElementById('loadingOverlay').style.display = 'none';
            }
        });

        // Add custom styling after Swagger UI loads
        setTimeout(() => {
            // Customize Swagger UI appearance
            const style = document.createElement('style');
            style.textContent = `
                .swagger-ui .scheme-container {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 15px;
                    border-radius: 8px;
                    margin: 20px 0;
                }
                
                .swagger-ui .scheme-container .schemes-title {
                    color: white;
                }
                
                .swagger-ui .btn.authorize {
                    background: #667eea;
                    border-color: #667eea;
                }
                
                .swagger-ui .btn.authorize:hover {
                    background: #764ba2;
                    border-color: #764ba2;
                }
            `;
            document.head.appendChild(style);
        }, 1000);
    </script>
</body>
</html>

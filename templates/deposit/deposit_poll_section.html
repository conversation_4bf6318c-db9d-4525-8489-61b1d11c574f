{% load i18n %}
{% load unfold %}
{% load humanize %}

{% component "unfold/components/card.html" with class="relative overflow-hidden rounded-lg shadow-md" %}
    <div class="relative z-10 p-6">
        {% component "unfold/components/title.html" with class="text-2xl font-bold mb-6 text-primary" %}
            {{ title|default:"اطلاعات صندوق" }}
        {% endcomponent %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {% component "unfold/components/card.html" with class="bg-white/80 shadow-sm hover:shadow-md transition-shadow duration-300 rounded-lg p-4" %}
                {% component "unfold/components/text.html" with class="text-gray-500 mb-2 text-sm" %}
                    {% trans "مبلغ کل وام" %}
                {% endcomponent %}
                {% component "unfold/components/title.html" with class="text-xl font-bold text-indigo-700" %}
                    {{ total_debt_amount|default:"0"|floatformat:"0"|intcomma }} {% trans "ریال" %}
                {% endcomponent %}
            {% endcomponent %}
            
            {% component "unfold/components/card.html" with class="bg-white/80 shadow-sm hover:shadow-md transition-shadow duration-300 rounded-lg p-4" %}
                {% component "unfold/components/text.html" with class="text-gray-500 mb-2 text-sm" %}
                    {% trans "مبلغ هر سهم" %}
                {% endcomponent %}
                {% component "unfold/components/title.html" with class="text-xl font-bold text-indigo-700" %}
                    {{ unit_amount|default:"0"|floatformat:"0"|intcomma }} {% trans "ریال" %}
                {% endcomponent %}
            {% endcomponent %}
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {% component "unfold/components/card.html" with class="bg-white/80 shadow-sm hover:shadow-md transition-shadow duration-300 rounded-lg p-4" %}
                {% component "unfold/components/text.html" with class="text-gray-500 mb-2 text-sm" %}
                    {% trans "دوره پرداخت" %}
                {% endcomponent %}
                {% component "unfold/components/title.html" with class="text-xl font-bold text-indigo-700 flex items-center" %}
                    <span>{{ payment_cycle|default:"0" }}</span>
                    <span class="mr-1 text-sm text-gray-600">{% trans "ماه" %}</span>
                {% endcomponent %}
            {% endcomponent %}
            
            {% component "unfold/components/card.html" with class="bg-white/80 shadow-sm hover:shadow-md transition-shadow duration-300 rounded-lg p-4" %}
                {% component "unfold/components/text.html" with class="text-gray-500 mb-2 text-sm" %}
                    {% trans "تعداد ماه‌های قرعه‌کشی" %}
                {% endcomponent %}
                {% component "unfold/components/title.html" with class="text-xl font-bold text-indigo-700 flex items-center" %}
                    <span>{{ lottery_month_count|default:"0" }}</span>
                    <span class="mr-1 text-sm text-gray-600">{% trans "ماه" %}</span>
                {% endcomponent %}
            {% endcomponent %}
        </div>
        
        {% if members_count %}
        <div class="mt-8">
            {% component "unfold/components/progress.html" with value=completion_percentage title="پیشرفت تکمیل صندوق" class="h-2 bg-gray-200 rounded-full" description="تعداد اعضا: "|add:members_count|stringformat:"s" %}
            {% endcomponent %}
            
            <div class="mt-2 flex justify-between text-sm text-gray-500">
                <span>{{ completion_percentage }}% {% trans "تکمیل شده" %}</span>
                <span>{% trans "سهم‌های تکمیل شده:" %} {{ total_shares|default:"0" }}</span>
            </div>
        </div>
        {% endif %}
        
        {% if initial_lottery_date %}
        <div class="mt-6 text-sm text-gray-600">
            <strong>{% trans "تاریخ قرعه‌کشی اولیه:" %}</strong> {{ initial_lottery_date|date:"Y/m/d" }}
        </div>
        {% endif %}
    </div>
    
    <!-- تصویر پس‌زمینه -->
    <div class="absolute inset-0 z-0 opacity-5">
        <img src="/static/images/section-deposit-poll.svg" alt="پس‌زمینه صندوق" class="w-full h-full object-cover">
    </div>
{% endcomponent %}

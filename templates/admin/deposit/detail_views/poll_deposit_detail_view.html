{% extends "admin/base_site.html" %}
{% load i18n static admin_urls unfold humanize %}

{% block branding %}
    {% include "unfold/helpers/site_branding.html" %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <script src="{% url 'admin:jsi18n' %}"></script>
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css" rel="stylesheet" type="text/css" />
    <!-- Material Icons - keeping original but with adjusted settings -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
      :root {
        font-family: 'Vazirmatn', system-ui, sans-serif;
      }
      
      /* Custom styles for tab system */
      .tab-content {
        display: none;
      }
      
      .tab-content.active {
        display: block;
      }
      
      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 4px;
      }
      
      .dot-success {
        background-color: var(--color-success-500);
      }
      
      .dot-warning {
        background-color: var(--color-warning-500);
      }
      
      /* Material Icons sizing fix */
      .material-icons {
        font-size: 20px !important;
        vertical-align: middle;
        margin-right: 8px;
      }
      
      /* Custom style for owner row in table */
      .owner-row {
        background-color: rgba(34, 197, 94, 0.1) !important; /* Light green background */
        border: 1px solid rgba(34, 197, 94, 0.3) !important; /* Green border */
      }
      
      /* Make sure the owner row stands out in both light and dark modes */
      .dark .owner-row {
        background-color: rgba(34, 197, 94, 0.2) !important;
        border: 1px solid rgba(34, 197, 94, 0.4) !important;
      }
      
      /* Add a subtle left border to make it even more distinct */
      .owner-row td:first-child {
        border-left: 3px solid rgb(34, 197, 94) !important;
      }
      
      /* Styles for due date rows */
      .completed-row {
        background-color: rgba(34, 197, 94, 0.1) !important; /* Light green background */
      }
      
      .pending-row {
        background-color: rgba(245, 158, 11, 0.1) !important; /* Light amber background */
      }
      
      /* Make sure the rows stand out in dark mode */
      .dark .completed-row {
        background-color: rgba(34, 197, 94, 0.2) !important;
      }
      
      .dark .pending-row {
        background-color: rgba(245, 158, 11, 0.2) !important;
      }
      
      /* Add a subtle left border to make it even more distinct */
      .completed-row td:first-child {
        border-left: 3px solid rgb(34, 197, 94) !important; /* Green border */
      }
      
      .pending-row td:first-child {
        border-left: 3px solid rgb(245, 158, 11) !important; /* Amber border */
      }
    </style>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Tab functionality
        const tabButtons = document.querySelectorAll('[data-tab]');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
          button.addEventListener('click', () => {
            // Remove active class from all buttons
            tabButtons.forEach(btn => {
              btn.classList.remove('border-primary-500', 'font-semibold', 'text-primary-500');
              btn.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Hide all tab contents
            tabContents.forEach(content => {
              content.classList.remove('active');
            });
            
            // Add active class to clicked button
            button.classList.remove('border-transparent', 'text-gray-500');
            button.classList.add('border-primary-500', 'font-semibold', 'text-primary-500');
            
            // Show corresponding tab content
            const tabId = button.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
          });
        });
      });
    </script>
{% endblock %}

{% block breadcrumbs %}{% if not is_popup %}
    <div class="px-4 lg:px-8">
        <div class="container mb-6 mx-auto -my-3 lg:mb-12">
            <ul class="flex flex-wrap">
                {% url 'admin:index' as link %}
                {% trans 'Home' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% url 'admin:deposit_polldeposit_changelist' as link %}
                {% trans 'Poll Deposit' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% url 'admin:deposit_polldeposit_detail' object_id=deposit_id as link %}
                {% trans 'Deposit Detail' as name %} 
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}
            </ul>
        </div>
    </div>
{% endif %}{% endblock %}

{% block content %}
    {% component "unfold/components/container.html" %}
        <!-- Main Deposit Information Card -->

        {% component "unfold/components/card.html" with class="mb-6" %}
        <!-- Header with Edit button on the right -->
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center">
                <img src="{% static 'images/deposit-poll.svg' %}" alt="Deposit Poll" class="w-16 h-18 mr-3">
                {% component "unfold/components/title.html" %}
                    {{ deposit_title }}
                {% endcomponent %}
            </div>
            <div class="flex space-x-2">
                {% component "unfold/components/button.html" with href=change_form_url %}
                    <span class="material-icons">edit</span>
                    {% trans "View/Edit" %}
                {% endcomponent %}
            </div>
        </div>
        
            <div class="p-6">                
                <!-- Grid layout using Tailwind instead of unfold/components/grid.html -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
                    <!-- Total Loan Amount -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-icons text-primary-500">credit_card</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Total Loan Amount:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}
                           &nbsp; {{ total_debt_amount }} &nbsp;
                        {% endcomponent %}
                    {% endcomponent %}
                    
                    <!-- Payment Date -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-icons text-primary-500">calendar_month</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Payment Due:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}
                            {{ payment_cycle }} &nbsp; 
                        {% endcomponent %}
                    {% endcomponent %}
                    
                    <!-- Number of Lottery -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-icons text-primary-500">casino</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Number of Completed Lotteries:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}{{ number_of_lottery|intcomma }} {% endcomponent %}
                    {% endcomponent %}
                    <!-- Amount per Share -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-icons text-primary-500">attach_money</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Amount per Share:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}&nbsp; {{ amount_per_share|intcomma }} {% endcomponent %}
                    {% endcomponent %}
                    <!-- Due Date -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-icons text-primary-500">event</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Due Date:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}{{ due_date }} {% endcomponent %}
                    {% endcomponent %}

                    <!-- Region Name -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-icons text-primary-500">location_on</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Region Name:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}{{ regin_name }} {% endcomponent %}
                    {% endcomponent %}
                </div>
            </div>
        {% endcomponent %}
        
        <!-- Action Buttons -->
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            {% component "unfold/components/button.html" with href=payment_url class="justify-center" %}
                <span class="material-icons">payments</span>
                {% trans "Payments" %}
            {% endcomponent %}
            
            {% component "unfold/components/button.html" with href=transaction_url variant="danger" class="justify-center" %}
                <span class="material-icons">credit_card</span>
                {% trans "Transactions" %}
            {% endcomponent %}
            
            {% component "unfold/components/button.html" with href=request_join_url variant="secondary" class="justify-center" %}
                <span class="material-icons">person_add</span>
                {% trans "Membership Requests" %}
            {% endcomponent %}
            
            {% component "unfold/components/button.html" with href=lottery_url variant="secondary" class="justify-center" %}
                <span class="material-icons">stars</span>
                {% trans "Winners" %}
            {% endcomponent %}
            

            {% component "unfold/components/button.html" with href=tickets_url variant="secondary" class="justify-center" %}
                <span class="material-icons">confirmation_number</span>
                {% trans "Tickets" %}
            {% endcomponent %}
            
            {% component "unfold/components/button.html" with href=medias_url variant="secondary" class="justify-center" %}
                <span class="material-icons">photo_library</span>
                {% trans "Media" %}
            {% endcomponent %}

        </div>
        
        <!-- Tab System -->
        {% component "unfold/components/card.html" %}
            <!-- Custom tab navigation -->
            <div class="border-b border-gray-200">
                <div class="flex space-x-4">
                    <button class="py-3 px-4 border-b-2 border-primary-500 font-semibold text-primary-500" data-tab="members-tab">{% trans "Members" %}</button>
                    <button class="py-3 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="duedate-tab">{% trans "DueDates" %}</button>
                </div>
            </div>
            
            <!-- Tab contents -->
            <div class="p-4 mt-6">
                <!-- Members Tab -->
                <div id="members-tab" class="tab-content active">
                    {% if deposit_members %}
                        {% component "unfold/components/table.html" with table=members_table_data card_included=1 striped=4 %}{% endcomponent %}
                    {% else %}
                        <div class="text-center py-8">
                            <img src="{% static 'images/image_null_deposit.svg' %}" alt="No data" class="mx-auto h-32">
                            <p class="bg-white border border-base-200 flex grow items-center justify-center py-2 rounded shadow-sm dark:bg-base-900 lg:border-0 lg:rounded-none lg:shadow-none">
                                {% trans "No data" %}
                            </p>
                        </div>
                    {% endif %}
                </div>
                <div id="duedate-tab" class="tab-content">
                    {% if due_dates_table_data.rows %}
                        {% component "unfold/components/table.html" with table=due_dates_table_data card_included=1 striped=1 %}{% endcomponent %}
                    {% else %}
                        <div class="text-center py-8">
                            <img src="{% static 'images/image_null_deposit.svg' %}" alt="No data" class="mx-auto h-32">
                            <p class="bg-white border border-base-200 flex grow items-center justify-center py-2 rounded shadow-sm dark:bg-base-900 lg:border-0 lg:rounded-none lg:shadow-none">
                                {% trans "No due dates available" %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endcomponent %}
    {% endcomponent %}
{% endblock %}
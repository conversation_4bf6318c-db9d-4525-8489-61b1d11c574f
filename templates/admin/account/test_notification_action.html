{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify unfold %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
    &rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
    &rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-4xl">
    <div class="flex flex-col gap-6">

        <!-- Header Card -->
        <div
            class="bg-white block border border-base-200 flex flex-col grow overflow-hidden p-6 relative rounded-default shadow-xs dark:bg-base-900 dark:border-base-800 mb-6">
            <div
                class="font-semibold text-2xl text-font-important-light tracking-tight dark:text-font-important-dark mb-4">
                <span class="flex items-center gap-3">
                    <span class="material-symbols-outlined text-primary-600">notifications</span>
                    {{ title }}
                </span>
            </div>

            <p class="leading-relaxed mb-0 text-sm text-font-muted-light dark:text-font-muted-dark">
                {% trans "Use this form to send test notifications to users. Select a user region and notification
                service type to test the notification system." %}
            </p>
        </div>

        <!-- Main Form Card -->
        <div
            class="bg-white block border border-base-200 flex flex-col grow overflow-hidden p-6 relative rounded-default shadow-xs dark:bg-base-900 dark:border-base-800">
            <h2
                class="border-b border-base-200 font-semibold mb-6 -mt-6 -mx-6 py-4 px-6 text-[15px] text-font-important-light dark:text-font-important-dark dark:border-base-800">
                تنظیمات تست نوتیفیکیشن
            </h2>

            <form method="post" novalidate class="space-y-6">
                {% csrf_token %}

                <!-- User Region Field -->
                <div class="space-y-2">
                    <label for="{{ form.user_region.id_for_label }}"
                        class="block text-sm font-medium text-font-important-light dark:text-font-important-dark">
                        {{ form.user_region.label }}
                        <span class="text-red-500">*</span>
                    </label>

                    <div class="relative">
                        {{ form.user_region }}
                    </div>

                    {% if form.user_region.help_text %}
                    <p class="leading-relaxed mb-0 text-sm text-font-muted-light dark:text-font-muted-dark mt-1">
                        {{ form.user_region.help_text }}
                    </p>
                    {% endif %}

                    {% if form.user_region.errors %}
                    <div class="mt-2">
                        {% for error in form.user_region.errors %}
                        <p class="leading-relaxed mb-0 text-sm text-red-600 dark:text-red-400">
                            {{ error }}
                        </p>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- Notification Service Field -->
                <div class="space-y-2">
                    <label for="{{ form.notification_service.id_for_label }}"
                        class="block text-sm font-medium text-font-important-light dark:text-font-important-dark">
                        {{ form.notification_service.label }}
                        <span class="text-red-500">*</span>
                    </label>

                    <div class="relative">
                        {{ form.notification_service }}
                    </div>

                    {% if form.notification_service.help_text %}
                    <p class="leading-relaxed mb-0 text-sm text-font-muted-light dark:text-font-muted-dark mt-1">
                        {{ form.notification_service.help_text }}
                    </p>
                    {% endif %}

                    {% if form.notification_service.errors %}
                    <div class="mt-2">
                        {% for error in form.notification_service.errors %}
                        <p class="leading-relaxed mb-0 text-sm text-red-600 dark:text-red-400">
                            {{ error }}
                        </p>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-base-200 dark:border-base-800">
                    <div class="flex gap-3">
                        <button type="submit" name="_save"
                            class="font-medium flex group items-center gap-2 px-3 py-2 rounded-default justify-center whitespace-nowrap cursor-pointer border border-base-200 bg-primary-600 border-transparent text-white">
                            <span class="material-symbols-outlined text-sm">send</span>
                            {% trans 'Send Test Notification' %}
                        </button>

                        {% url opts|admin_urlname:'changelist' as cancel_url %}
                        <a href="{{ cancel_url }}"
                            class="font-medium flex group items-center gap-2 px-3 py-2 rounded-default justify-center whitespace-nowrap cursor-pointer bg-gray-100 border border-transparent dark:bg-base-800">
                            <span class="material-symbols-outlined text-sm">arrow_back</span>
                            {% trans 'Cancel' %}
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Help Section -->
        <div
            class="bg-white block border border-base-200 flex flex-col grow overflow-hidden p-6 relative rounded-default shadow-xs dark:bg-base-900 dark:border-base-800">
            <h2
                class="border-b border-base-200 font-semibold mb-6 -mt-6 -mx-6 py-4 px-6 text-[15px] text-font-important-light dark:text-font-important-dark dark:border-base-800">
                <span class="flex items-center gap-2">
                    <span class="material-symbols-outlined">help</span>
                    نحوه عملکرد
                </span>
            </h2>

            <div class="space-y-4">
                <p
                    class="leading-relaxed mb-0 text-sm font-medium text-font-important-light dark:text-font-important-dark mb-3">
                    نحوه عملکرد:
                </p>

                <div class="space-y-3">
                    <div class="flex items-start gap-3">
                        <span class="material-symbols-outlined text-primary-600 text-sm mt-0.5">check_circle</span>
                        <p class="leading-relaxed mb-0 text-sm">
                            انتخاب منطقه کاربری - این گزینه مشخص می‌کند که اعلان برای کدام کاربر و در چه منطقه‌ای ارسال
                            شود.
                        </p>
                    </div>

                    <div class="flex items-start gap-3">
                        <span class="material-symbols-outlined text-primary-600 text-sm mt-0.5">check_circle</span>
                        <p class="leading-relaxed mb-0 text-sm">
                            انتخاب نوع سرویس اعلان - این گزینه نوع اعلان ارسالی را تعیین می‌کند.
                        </p>
                    </div>

                    <div class="flex items-start gap-3">
                        <span class="material-symbols-outlined text-primary-600 text-sm mt-0.5">check_circle</span>
                        <p class="leading-relaxed mb-0 text-sm">
                            سیستم به صورت خودکار در صورت نیاز، یک سپرده در منطقه انتخاب‌شده پیدا می‌کند.
                        </p>
                    </div>

                    <div class="flex items-start gap-3">
                        <span class="material-symbols-outlined text-primary-600 text-sm mt-0.5">check_circle</span>
                        <p class="leading-relaxed mb-0 text-sm">
                            اگر سپرده‌ای در منطقه یافت نشود، یک اعلان تست عمومی ارسال خواهد شد.
                        </p>
                    </div>

                    <div class="flex items-start gap-3">
                        <span class="material-symbols-outlined text-primary-600 text-sm mt-0.5">check_circle</span>
                        <p class="leading-relaxed mb-0 text-sm">
                            اعلان در پایگاه داده ذخیره شده و در صورت داشتن توکن FCM معتبر، از طریق FCM ارسال می‌شود.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Information -->
        <div
            class="bg-white block border border-base-200 flex flex-col grow overflow-hidden p-6 relative rounded-default shadow-xs dark:bg-base-900 dark:border-base-800">
            <h2
                class="border-b border-base-200 font-semibold mb-6 -mt-6 -mx-6 py-4 px-6 text-[15px] text-font-important-light dark:text-font-important-dark dark:border-base-800">
                <span class="flex items-center gap-2">
                    <span class="material-symbols-outlined">info</span>
                    اطلاعات سیستم
                </span>
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ active_regions_count|default:"0" }}
                    </div>
                    <p class="leading-relaxed mb-0 text-sm text-green-700 dark:text-green-300">
                        {% trans "Active Regions" %}
                    </p>
                </div>

                <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ available_services_count|default:"11" }}
                    </div>
                    <p class="leading-relaxed mb-0 text-sm text-blue-700 dark:text-blue-300">
                        {% trans "Available Services" %}
                    </p>
                </div>

                <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {{ total_notifications_count|default:"0" }}
                    </div>
                    <p class="leading-relaxed mb-0 text-sm text-purple-700 dark:text-purple-300">
                        {% trans "Total Notifications" %}
                    </p>
                </div>
            </div>
        </div>

    </div>
</div>

{{ form.media }}
{% endblock %}
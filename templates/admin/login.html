{% extends 'unfold/layouts/skeleton.html' %}

{% load i18n static %}

{% block extrastyle %}
    {{ block.super }}
    {{ form.media }}
{% endblock %}

{% block bodyclass %}{{ block.super }}bg-base-50 login dark:bg-base-900{% endblock %}

{% block usertools %}{% endblock %}

{% block nav-global %}{% endblock %}

{% block nav-sidebar %}{% endblock %}

{% block content_title %}{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block title %}
    {{ title }} | {{ site_title }}
{% endblock %}

{% block base %}
    <div id="page" class="flex min-h-screen">
        <div class="flex flex-grow items-center justify-center mx-auto px-4 relative">
            <div class="w-full sm:w-96">
                <h1 class="font-semibold mb-10">
                    <span class="block text-font-important-light dark:text-font-important-dark">{% trans 'Welcome back to' %}</span>
                    <span class="block text-primary-600 text-xl dark:text-primary-500">{{ site_title|default:_('Django site admin') }}</span>
                </h1>

                {% include "unfold/helpers/messages.html" %}

                {% if form.errors or form.non_field_errors %}
                    <div class="flex flex-col gap-4 mb-8 *:mb-0">
                        {% include "unfold/helpers/messages/errornote.html" with errors=form.errors %}

                        {% include "unfold/helpers/messages/error.html" with errors=form.non_field_errors %}

                        {% if user.is_authenticated %}
                            {% blocktranslate trimmed asvar message %}
                                You are authenticated as {{ username }}, but are not authorized to
                                access this page. Would you like to login to a different account?
                            {% endblocktranslate %}

                            {% include "unfold/helpers/messages/error.html" with error=message %}
                        {% endif %}
                    </div>
                {% endif %}

                {% block login_before %}{% endblock %}

                <form action="{{ app_path }}" method="post" id="login-form">
                    {% csrf_token %}

                    {% include "unfold/helpers/field.html" with field=form.username %}

                    {% include "unfold/helpers/field.html" with field=form.password %}

                    {% url 'admin_password_reset' as password_reset_url %}

                    <div class="submit-row">
                        <button type="submit" class="bg-primary-600 border border-transparent flex flex-row font-semibold group items-center justify-center py-2 rounded text-sm text-white w-full">
                            {% translate 'Log in' %}

                            <i class="material-symbols-outlined ml-2 relative right-0 text-lg transition-all group-hover:-right-1">arrow_forward</i>
                        </button>
                    </div>

                    {% if password_reset_url %}
                        <div class="password-reset-link">
                            <a href="{{ password_reset_url }}" class="border border-base-200 font-medium hidden mt-4 px-3 py-2 rounded text-center text-sm text-base-500 transition-all w-full hover:bg-base-50 lg:block lg:w-auto dark:border-base-700 dark:text-font-default-dark dark:hover:text-base-200 dark:hover:bg-base-900">
                                {% translate 'Forgotten your password or username?' %}
                            </a>
                        </div>
                    {% endif %}
                </form>

                {% block login_after %}{% endblock %}
            </div>

            <div class="absolute flex flex-row items-center justify-between left-0 m-4 right-0 top-0">
                {% comment %} {% if site_url %}
                    <a href="{{ site_url }}" class="flex font-medium items-center text-sm text-primary-600 dark:text-primary-500">
                        <span class="material-symbols-outlined mr-2">arrow_back</span> {% trans 'Return to site' %}
                    </a>
                {% endif %} {% endcomment %}

                {% if not theme %}
                    {% include "unfold/helpers/theme_switch.html" %}
                {% endif %}
            </div>
        </div>

        {% if image %}
            <div class="bg-cover flex-grow hidden max-w-3xl xl:max-w-4xl xl:block" style="background-image: url('{{ image }}')">
            </div>
        {% endif %}
    </div>
{% endblock %}

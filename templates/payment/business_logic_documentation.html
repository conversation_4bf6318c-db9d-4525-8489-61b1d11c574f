<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستندات بیزینس لاجیک سیستم پرداخت</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: bold;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .section-header {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .section-header h2 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.8rem;
        }
        
        .section-header .icon {
            margin-left: 10px;
            color: #3498db;
        }
        
        .section-content {
            padding: 25px;
        }
        
        .deposit-type {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .deposit-type h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }
        
        .deposit-type .badge {
            font-size: 0.9rem;
            padding: 8px 12px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .formula {
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .formula h4 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .formula .equation {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            background: white;
            padding: 10px 20px;
            border-radius: 5px;
            display: inline-block;
        }
        
        .scenario {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .scenario h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .flow-step {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            position: relative;
        }
        
        .flow-step::before {
            content: attr(data-step);
            position: absolute;
            top: -10px;
            right: 15px;
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .api-endpoint {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .api-endpoint .method {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .api-endpoint .url {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .model-relationship {
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .model-relationship h4 {
            color: #0066cc;
            margin-bottom: 15px;
        }
        
        .field-list {
            list-style: none;
            padding: 0;
        }
        
        .field-list li {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
        }
        
        .field-list .field-name {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .field-list .field-desc {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .nav-tabs .nav-link {
            color: #495057;
            border: 1px solid transparent;
        }
        
        .nav-tabs .nav-link.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .tab-content {
            padding: 20px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 8px 8px;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-credit-card"></i> مستندات بیزینس لاجیک سیستم پرداخت</h1>
            <p>تحلیل کامل سیستم صندوق‌های جمعی و فرایند پرداخت</p>
        </div>

        <div class="content">
            <!-- System Overview -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-info-circle icon"></i>خلاصه سیستم</h2>
                </div>
                <div class="section-content">
                    <p class="lead">
                        سیستم پرداخت یک <strong>سیستم صندوق‌های جمعی</strong> است که در آن کاربران می‌توانند در انواع مختلف صندوق‌ها عضویت داشته باشند و پرداخت‌های دوره‌ای انجام دهند.
                    </p>
                    
                    <div class="formula">
                        <h4>فرمول اصلی محاسبه</h4>
                        <div class="equation">
                            مبلغ کل = مبلغ صندوق + قسط وام
                        </div>
                        <div class="equation mt-2">
                            total_amount = payment_amount + loan_installment_amount
                        </div>
                    </div>
                </div>
            </div>

            <!-- Theoretical Business Logic -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-brain icon"></i>منطق تئوری صندوق‌ها</h2>
                </div>
                <div class="section-content">

                    <div class="alert alert-info" role="alert">
                        <h4 class="alert-heading"><i class="fas fa-university"></i> فلسفه صندوق‌های جمعی</h4>
                        <p>صندوق‌های جمعی بر اساس اصل <strong>"همکاری و تعاون مالی"</strong> طراحی شده‌اند. هر نوع صندوق نیاز خاصی را برطرف می‌کند.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-primary h-100">
                                <div class="card-header bg-primary text-white text-center">
                                    <i class="fas fa-dice fa-2x"></i>
                                    <h4 class="mt-2">صندوق قرعه‌کشی</h4>
                                    <small>نظریه: دسترسی سریع به سرمایه</small>
                                </div>
                                <div class="card-body">
                                    <h5><i class="fas fa-lightbulb text-warning"></i> منطق کسب‌وکار</h5>
                                    <ul>
                                        <li><strong>مشکل:</strong> نیاز فوری به مبلغ کلان</li>
                                        <li><strong>راه‌حل:</strong> تجمیع پول اعضا</li>
                                        <li><strong>مزیت:</strong> بدون سود و ضامن</li>
                                        <li><strong>ریسک:</strong> وابستگی به سایر اعضا</li>
                                    </ul>

                                    <h5><i class="fas fa-calculator text-info"></i> فرمول محاسبه جدید</h5>
                                    <div class="formula">
                                        <div class="equation">
                                            total_shares = lottery_month_count × lottery_per_month_count
                                        </div>
                                        <div class="equation">
                                            unit_amount = total_debt_amount ÷ total_shares
                                        </div>
                                    </div>

                                    <h5><i class="fas fa-users text-success"></i> مثال عملی جدید</h5>
                                    <div class="code-block">
# صندوق 12 ماهه با 2 قرعه در ماه (دوهفته‌ای)
total_debt_amount = 12,000,000  # 12 میلیون
lottery_month_count = 12        # 12 ماه
lottery_per_month_count = 2     # 2 قرعه در ماه
total_shares = 12 × 2 = 24      # 24 سهم کل
unit_amount = 12,000,000 ÷ 24 = 500,000  # 500 هزار تومان هر سهم

# عضو با 2 سهم: 2 × 500,000 = 1,000,000 تومان ماهانه
# عضو با 1 سهم: 1 × 500,000 = 500,000 تومان ماهانه
# مجموع 24 قرعه‌کشی در 12 ماه
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-success h-100">
                                <div class="card-header bg-success text-white text-center">
                                    <i class="fas fa-coins fa-2x"></i>
                                    <h4 class="mt-2">صندوق پس‌انداز</h4>
                                    <small>نظریه: انضباط مالی و سود</small>
                                </div>
                                <div class="card-body">
                                    <h5><i class="fas fa-lightbulb text-warning"></i> منطق کسب‌وکار</h5>
                                    <ul>
                                        <li><strong>مشکل:</strong> عدم انضباط در پس‌انداز</li>
                                        <li><strong>راه‌حل:</strong> پرداخت اجباری ماهانه</li>
                                        <li><strong>مزیت:</strong> سود + انضباط</li>
                                        <li><strong>ریسک:</strong> تورم و نقدینگی</li>
                                    </ul>

                                    <h5><i class="fas fa-calculator text-info"></i> فرمول محاسبه</h5>
                                    <div class="formula">
                                        <div class="equation">
                                            total_savings = unit_amount × validity_duration
                                        </div>
                                    </div>

                                    <h5><i class="fas fa-chart-line text-success"></i> مثال عملی</h5>
                                    <div class="code-block">
# صندوق پس‌انداز 2 ساله
unit_amount = 500,000          # ماهانه 500 هزار
validity_duration = 24         # 24 ماه
total_savings = 12,000,000     # 12 میلیون پس‌انداز

# + سود سالانه (مثلاً 15%)
# + مدیریت ریسک تورم
# = بازدهی بهتر از بانک
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-warning h-100">
                                <div class="card-header bg-warning text-white text-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                    <h4 class="mt-2">صندوق گزارشی</h4>
                                    <small>نظریه: شفافیت و کنترل</small>
                                </div>
                                <div class="card-body">
                                    <h5><i class="fas fa-lightbulb text-warning"></i> منطق کسب‌وکار</h5>
                                    <ul>
                                        <li><strong>مشکل:</strong> عدم شفافیت مالی</li>
                                        <li><strong>راه‌حل:</strong> ثبت دقیق تراکنش‌ها</li>
                                        <li><strong>مزیت:</strong> کنترل و گزارش‌گیری</li>
                                        <li><strong>کاربرد:</strong> سازمان‌ها و گروه‌ها</li>
                                    </ul>

                                    <h5><i class="fas fa-calculator text-info"></i> فرمول محاسبه</h5>
                                    <div class="formula">
                                        <div class="equation">
                                            unit_amount = مبلغ ثابت گزارشی
                                        </div>
                                    </div>

                                    <h5><i class="fas fa-building text-success"></i> مثال عملی</h5>
                                    <div class="code-block">
# صندوق گزارشی مسجد
unit_amount = 100,000          # مبلغ ثابت گزارشی
purpose = "ردیابی هزینه‌ها"    # هدف

# استفاده:
# - ثبت دقیق درآمد/هزینه
# - گزارش‌گیری دوره‌ای
# - شفافیت مالی
# - کنترل بودجه
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h4><i class="fas fa-balance-scale text-primary"></i> مقایسه انواع صندوق‌ها</h4>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ویژگی</th>
                                        <th><i class="fas fa-dice"></i> قرعه‌کشی</th>
                                        <th><i class="fas fa-coins"></i> پس‌انداز</th>
                                        <th><i class="fas fa-chart-line"></i> گزارشی</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>هدف اصلی</strong></td>
                                        <td>دسترسی سریع به سرمایه</td>
                                        <td>پس‌انداز بلندمدت</td>
                                        <td>شفافیت و کنترل</td>
                                    </tr>
                                    <tr>
                                        <td><strong>مدت زمان</strong></td>
                                        <td>کوتاه‌مدت (6-12 ماه)</td>
                                        <td>بلندمدت (1-5 سال)</td>
                                        <td>نامحدود</td>
                                    </tr>
                                    <tr>
                                        <td><strong>ریسک</strong></td>
                                        <td>متوسط (وابستگی به اعضا)</td>
                                        <td>کم (تضمین شده)</td>
                                        <td>خیلی کم (فقط ثبت)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>بازدهی</strong></td>
                                        <td>صفر (بدون سود)</td>
                                        <td>مثبت (سود سالانه)</td>
                                        <td>غیرقابل اعمال</td>
                                    </tr>
                                    <tr>
                                        <td><strong>انعطاف‌پذیری</strong></td>
                                        <td>کم (تعهد ماهانه)</td>
                                        <td>کم (تعهد بلندمدت)</td>
                                        <td>بالا (بر اساس نیاز)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>کاربرد</strong></td>
                                        <td>خرید کالای گران</td>
                                        <td>پس‌انداز خانوادگی</td>
                                        <td>مدیریت مالی سازمان</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Deposit Types -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-piggy-bank icon"></i>انواع صندوق‌ها و دلایل پرداخت</h2>
                </div>
                <div class="section-content">

                    <!-- Poll Deposit -->
                    <div class="deposit-type">
                        <h3>
                            <i class="fas fa-dice"></i> صندوق قرعه‌کشی (Poll Deposit)
                            <span class="badge bg-primary">محبوب‌ترین</span>
                        </h3>
                        <p><strong>توضیح:</strong> صندوقی که در آن اعضا هر ماه مبلغ مشخصی پرداخت می‌کنند و هر ماه یک نفر برنده قرعه‌کشی می‌شود.</p>

                        <div class="code-block">
# مثال: صندوق 12 ماهه با قرعه‌کشی دوهفته‌ای
deposit_type = "Poll"
total_debt_amount = 12,000,000  # کل مبلغ صندوق
lottery_month_count = 12        # تعداد ماه‌های قرعه‌کشی
lottery_per_month_count = 2     # تعداد قرعه‌کشی در ماه
total_shares = 12 × 2 = 24      # تعداد کل سهم‌ها
unit_amount = 500,000          # مبلغ هر سهم (محاسبه خودکار)
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-question-circle text-info"></i> چرا پرداخت می‌شود؟</h5>
                                <ul>
                                    <li>هر ماه یک نفر برنده قرعه‌کشی می‌شود</li>
                                    <li>برنده کل مبلغ صندوق را دریافت می‌کند</li>
                                    <li>بقیه اعضا باید مبلغ ماهانه خود را پرداخت کنند</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-target text-success"></i> هدف</h5>
                                <p class="text-success">دریافت مبلغ کلان در یکی از ماه‌ها بدون نیاز به وام بانکی</p>
                            </div>
                        </div>
                    </div>

                    <!-- Saving Deposit -->
                    <div class="deposit-type">
                        <h3>
                            <i class="fas fa-coins"></i> صندوق پس‌انداز (Saving Deposit)
                            <span class="badge bg-success">بلندمدت</span>
                        </h3>
                        <p><strong>توضیح:</strong> صندوقی برای پس‌انداز دوره‌ای که در پایان دوره، پول به همراه سود به اعضا برمی‌گردد.</p>

                        <div class="code-block">
# مثال: صندوق پس‌انداز 2 ساله
deposit_type = "Saving"
validity_duration = 2          # 2 سال
unit_amount = 500,000         # مبلغ ماهانه
payment_cycle = 5             # روز 5 هر ماه
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-question-circle text-info"></i> چرا پرداخت می‌شود؟</h5>
                                <ul>
                                    <li>پس‌انداز دوره‌ای برای جمع‌آوری پول</li>
                                    <li>در پایان دوره، پول + سود برمی‌گردد</li>
                                    <li>انضباط مالی و پس‌انداز منظم</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-target text-success"></i> هدف</h5>
                                <p class="text-success">پس‌انداز بلندمدت با سود و انضباط مالی</p>
                            </div>
                        </div>
                    </div>

                    <!-- Reporting Deposit -->
                    <div class="deposit-type">
                        <h3>
                            <i class="fas fa-chart-line"></i> صندوق گزارشی (Reporting Deposit)
                            <span class="badge bg-warning">تخصصی</span>
                        </h3>
                        <p><strong>توضیح:</strong> صندوقی برای گزارش‌دهی و شفافیت مالی در سازمان‌ها و گروه‌ها.</p>

                        <div class="code-block">
# مثال: صندوق گزارش‌دهی
deposit_type = "Reporting"
unit_amount = 100,000         # مبلغ گزارشی
validity_duration = 1         # 1 سال
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-question-circle text-info"></i> چرا پرداخت می‌شود؟</h5>
                                <ul>
                                    <li>برای گزارش‌دهی و شفافیت مالی</li>
                                    <li>ردیابی و کنترل هزینه‌ها</li>
                                    <li>مدیریت بودجه گروهی</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-target text-success"></i> هدف</h5>
                                <p class="text-success">ردیابی و گزارش‌گیری مالی دقیق</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Calculation -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-calculator icon"></i>محاسبه مبلغ پرداخت</h2>
                </div>
                <div class="section-content">

                    <div class="row">
                        <div class="col-md-6">
                            <h4><i class="fas fa-money-bill-wave text-primary"></i> مبلغ اصلی صندوق</h4>
                            <div class="code-block">
@staticmethod
def calculate_payment_amount(deposit, membership):
    if deposit.deposit_type in [POLL, SAVING]:
        # مبلغ بر اساس unit_amount صندوق
        amount = deposit.unit_amount
        return max(amount, Decimal('0.01'))

    elif deposit.deposit_type == REPORTING:
        amount = deposit.unit_amount
        return max(amount, Decimal('0.01'))

    return Decimal('0.01')
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h4><i class="fas fa-hand-holding-usd text-warning"></i> قسط وام</h4>
                            <div class="code-block">
@staticmethod
def get_loan_installment_amount(deposit, membership):
    # یافتن وام‌های فعال کاربر
    loans = Loan.objects.filter(
        deposit=deposit,
        deposit_membership=membership,
        status=Loan.LoanStatus.ACTIVE
    )

    # یافتن اولین قسط پرداخت نشده
    for loan in loans:
        next_installment = LoanInstallment.objects.filter(
            loan=loan,
            is_paid=False
        ).order_by('due_date').first()

        if next_installment:
            return next_installment.amount, True

    return Decimal('0.00'), False
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Process -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-cogs icon"></i>فرایند کامل پرداخت</h2>
                </div>
                <div class="section-content">

                    <div class="flow-step" data-step="1">
                        <h4><i class="fas fa-info-circle text-primary"></i> درخواست اطلاعات پرداخت</h4>
                        <div class="api-endpoint">
                            <span class="method">GET</span>
                            <div class="url">/api/payments/info/{deposit_id}/</div>
                        </div>
                        <p><strong>فرایند:</strong></p>
                        <ol>
                            <li>بررسی عضویت کاربر در صندوق</li>
                            <li>محاسبه مبلغ پرداخت اصلی</li>
                            <li>بررسی وجود وام و محاسبه قسط</li>
                            <li>ایجاد درخواست پرداخت در Zarinpal</li>
                            <li>ذخیره اطلاعات پرداخت در دیتابیس</li>
                        </ol>

                        <div class="code-block">
# بررسی عضویت
membership = DepositMembership.objects.filter(
    user=request.user,
    deposit=deposit,
    is_active=True
).first()

# محاسبه اطلاعات پرداخت
payment_info = PaymentCalculationService.get_payment_info(deposit, membership)

# ایجاد رکورد پرداخت
payment_data = {
    'deposit': deposit.id,
    'gateway_name': 'zarinpal',
    'amount': payment_info['total_amount'],
    'includes_loan_installment': payment_info['has_loan_installment'],
    'loan_installment_amount': payment_info['loan_installment_amount'],
}
                        </div>
                    </div>

                    <div class="flow-step" data-step="2">
                        <h4><i class="fas fa-check-circle text-success"></i> تأیید پرداخت</h4>
                        <div class="api-endpoint">
                            <span class="method">GET</span>
                            <div class="url">/payments/verify/?Authority=xxx&Status=OK</div>
                        </div>
                        <p><strong>فرایند:</strong></p>
                        <ol>
                            <li>دریافت کد Authority از Zarinpal</li>
                            <li>تأیید پرداخت با API Zarinpal</li>
                            <li>به‌روزرسانی وضعیت پرداخت</li>
                            <li>ایجاد/به‌روزرسانی Transaction</li>
                            <li>پردازش قسط وام (در صورت وجود)</li>
                        </ol>

                        <div class="code-block">
# تأیید پرداخت با Zarinpal
verify_data = {
    "merchant_id": settings.ZARINPAL_MERCHANT_ID,
    "amount": amount_int,
    "authority": authority
}

# در صورت موفقیت
if verify_result.get('data', {}).get('code') == 100:
    ref_id = verify_result.get('data', {}).get('ref_id', '')
    payment.mark_as_verified(ref_id)
                        </div>
                    </div>

                    <div class="flow-step" data-step="3">
                        <h4><i class="fas fa-receipt text-info"></i> ایجاد Transaction</h4>
                        <p><strong>فرایند:</strong></p>
                        <ol>
                            <li>ایجاد رکورد تراکنش با وضعیت SUCCESS</li>
                            <li>اتصال تراکنش به پرداخت</li>
                            <li>اتصال تراکنش به سررسید فعلی</li>
                            <li>ارسال نوتیفیکیشن به مالک صندوق</li>
                        </ol>

                        <div class="code-block">
# ایجاد تراکنش جدید
transaction = Transaction.objects.create(
    deposit=payment.deposit,
    user=payment.user,
    due_date=current_due_date,
    status=Transaction.TransactionStatus.SUCCESS,
    amount=payment.total_amount,
    transaction_type=Transaction.TransactionType.INCOME,
    description=f"پرداخت آنلاین - شماره پیگیری: {payment.reference_number}"
)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scenarios -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-play-circle icon"></i>سناریوهای مختلف پرداخت</h2>
                </div>
                <div class="section-content">

                    <div class="scenario">
                        <h4><i class="fas fa-dice text-primary"></i> سناریو 1: پرداخت عادی صندوق قرعه‌کشی</h4>
                        <p><strong>شرایط:</strong> کاربر در صندوق 10 نفره عضو است و وام ندارد</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="code-block">
# اطلاعات صندوق
deposit.unit_amount = 1,000,000  # 1 میلیون تومان
loan_installment = 0             # بدون وام

# محاسبه
total_amount = 1,000,000 + 0 = 1,000,000 تومان
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>نتیجه:</h5>
                                <ul>
                                    <li><strong>مبلغ پرداختی:</strong> 1,000,000 تومان</li>
                                    <li><strong>شامل وام:</strong> خیر</li>
                                    <li><strong>هدف:</strong> شرکت در قرعه‌کشی ماهانه</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="scenario">
                        <h4><i class="fas fa-hand-holding-usd text-warning"></i> سناریو 2: پرداخت همراه با قسط وام</h4>
                        <p><strong>شرایط:</strong> کاربر وام 5 میلیون تومان گرفته (10 قسط 500 هزار تومانی)</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="code-block">
# اطلاعات صندوق و وام
deposit.unit_amount = 1,000,000    # 1 میلیون تومان
loan_installment = 500,000         # 500 هزار تومان قسط

# محاسبه
total_amount = 1,000,000 + 500,000 = 1,500,000 تومان
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>نتیجه:</h5>
                                <ul>
                                    <li><strong>مبلغ پرداختی:</strong> 1,500,000 تومان</li>
                                    <li><strong>شامل وام:</strong> بله (500,000 تومان)</li>
                                    <li><strong>هدف:</strong> شرکت در قرعه‌کشی + بازپرداخت وام</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="scenario">
                        <h4><i class="fas fa-coins text-success"></i> سناریو 3: پرداخت صندوق پس‌انداز</h4>
                        <p><strong>شرایط:</strong> کاربر در صندوق پس‌انداز 2 ساله عضو است</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="code-block">
# اطلاعات صندوق پس‌انداز
deposit.unit_amount = 500,000      # 500 هزار تومان ماهانه
loan_installment = 0               # بدون وام

# محاسبه
total_amount = 500,000 + 0 = 500,000 تومان
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>نتیجه:</h5>
                                <ul>
                                    <li><strong>مبلغ پرداختی:</strong> 500,000 تومان</li>
                                    <li><strong>شامل وام:</strong> خیر</li>
                                    <li><strong>هدف:</strong> پس‌انداز بلندمدت با سود</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Problem Analysis -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-bug icon"></i>تحلیل مشکلات واقعی سیستم</h2>
                </div>
                <div class="section-content">

                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> مشکل اصلی: `unit_amount = None`</h4>
                        <p>خطای <code>TypeError: '<=' not supported between instances of 'NoneType' and 'int'</code> زمانی رخ می‌دهد که <code>unit_amount</code> برابر <code>None</code> باشد.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <i class="fas fa-dice"></i> صندوق‌های Poll مشکل‌دار
                                </div>
                                <div class="card-body">
                                    <h5>مشکل: بدون `lottery_month_count`</h5>
                                    <p>8 صندوق Poll وجود دارد که <code>lottery_month_count = None</code> دارند:</p>

                                    <div class="code-block">
# مثال صندوق مشکل‌دار
deposit = PollDeposit.objects.create(
    title="صندوق تستی",
    total_debt_amount=None,      # خالی!
    lottery_month_count=None,    # خالی!
    # unit_amount محاسبه نمی‌شه
)

# هنگام پرداخت
unit_amount = total_debt_amount / lottery_month_count
# None / None = خطا!
                                    </div>

                                    <div class="alert alert-warning mt-2">
                                        <small><i class="fas fa-info-circle"></i>
                                        این صندوق‌ها توسط Admin ایجاد شده‌اند ولی اطلاعات کامل ندارند</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <i class="fas fa-coins"></i> صندوق‌های Saving مشکل‌دار
                                </div>
                                <div class="card-body">
                                    <h5>مشکل: بدون `validity_duration`</h5>
                                    <p>5 صندوق Saving وجود دارد که <code>validity_duration = None</code> دارند:</p>

                                    <div class="code-block">
# مثال صندوق مشکل‌دار
deposit = SavingDeposit.objects.create(
    title="صندوق قرض‌الحسنه تست",
    unit_amount=100000.00,       # دستی تنظیم شده
    validity_duration=None,      # خالی!
    # منطق محاسبه مشخص نیست
)

# این صندوق‌ها فعلاً کار می‌کنند چون unit_amount دستی تنظیم شده
                                    </div>

                                    <div class="alert alert-info mt-2">
                                        <small><i class="fas fa-check-circle"></i>
                                        این صندوق‌ها فعلاً کار می‌کنند چون unit_amount دستی وارد شده</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h4><i class="fas fa-search text-primary"></i> علت اصلی مشکل</h4>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <i class="fas fa-code"></i> محاسبه خودکار غیرفعال
                                    </div>
                                    <div class="card-body">
                                        <div class="code-block">
def save(self, *args, **kwargs):
    # این خط کامنت شده!
    # self.unit_amount = self.calculate_unit_amount()
    super().save(*args, **kwargs)
                                        </div>
                                        <p><small>محاسبه خودکار <code>unit_amount</code> در همه صندوق‌ها غیرفعال است</small></p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-white">
                                        <i class="fas fa-user-cog"></i> ورودی دستی Admin
                                    </div>
                                    <div class="card-body">
                                        <p>Admin باید دستی فیلدهای زیر را پر کند:</p>
                                        <ul>
                                            <li><code>total_debt_amount</code></li>
                                            <li><code>lottery_month_count</code></li>
                                            <li><code>unit_amount</code></li>
                                            <li><code>validity_duration</code></li>
                                        </ul>
                                        <p><small>اگر فراموش کند، خطا رخ می‌دهد</small></p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <i class="fas fa-shield-alt"></i> عدم Validation
                                    </div>
                                    <div class="card-body">
                                        <p>هیچ بررسی وجود ندارد برای:</p>
                                        <ul>
                                            <li>فیلدهای الزامی</li>
                                            <li>محاسبه صحیح</li>
                                            <li>مقادیر منطقی</li>
                                            <li>وابستگی‌های فیلدها</li>
                                        </ul>
                                        <p><small>صندوق ناقص ذخیره می‌شود</small></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h4><i class="fas fa-route text-success"></i> مسیر خطا در سیستم</h4>

                        <div class="flow-step" data-step="1">
                            <h5><i class="fas fa-user-plus text-primary"></i> Admin صندوق ناقص ایجاد می‌کند</h5>
                            <div class="code-block">
# Admin در پنل مدیریت
PollDeposit.objects.create(
    title="صندوق تستی",
    # total_debt_amount و lottery_month_count فراموش می‌شه
)
# unit_amount = None (محاسبه نمی‌شه)
                            </div>
                        </div>

                        <div class="flow-step" data-step="2">
                            <h5><i class="fas fa-user-check text-info"></i> کاربر عضو صندوق می‌شود</h5>
                            <div class="code-block">
# کاربر درخواست عضویت می‌فرستد
POST /api/deposits/1/join/
# عضویت موفق (هیچ بررسی unit_amount نمی‌شه)
                            </div>
                        </div>

                        <div class="flow-step" data-step="3">
                            <h5><i class="fas fa-credit-card text-warning"></i> کاربر سعی به پرداخت می‌کند</h5>
                            <div class="code-block">
# کاربر درخواست پرداخت می‌فرستد
GET /api/payments/info/1/
# PaymentCalculationService.calculate_payment_amount()
# amount = deposit.unit_amount  # None!
# return max(amount, Decimal('0.01'))  # خطا!
                            </div>
                        </div>

                        <div class="flow-step" data-step="4">
                            <h5><i class="fas fa-exclamation-triangle text-danger"></i> خطا در Transaction.clean()</h5>
                            <div class="code-block">
# Transaction.objects.create(amount=None)
# transaction.clean()
# if self.amount <= 0:  # None <= 0 ❌
# TypeError: '<=' not supported between instances of 'NoneType' and 'int'
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Solutions -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-tools icon"></i>راه‌حل‌های عملی</h2>
                </div>
                <div class="section-content">

                    <div class="alert alert-success" role="alert">
                        <h4 class="alert-heading"><i class="fas fa-lightbulb"></i> استراتژی حل مشکل</h4>
                        <p>برای حل کامل مشکل، باید در چندین لایه اقدام کنیم:</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success mb-3">
                                <div class="card-header bg-success text-white">
                                    <i class="fas fa-shield-alt"></i> راه‌حل فوری (Quick Fix)
                                </div>
                                <div class="card-body">
                                    <h5>1. Fix در Transaction.clean()</h5>
                                    <div class="code-block">
def clean(self):
    # بررسی None قبل از مقایسه
    if self.amount is None or self.amount <= 0:
        raise ValidationError(
            _("Transaction amount must be greater than zero.")
        )

    if self.due_date and self.due_date.deposit != self.deposit:
        raise ValidationError(
            _("The due date must belong to the same deposit.")
        )
                                    </div>
                                    <p><small><i class="fas fa-check"></i> این راه‌حل خطا را فوراً برطرف می‌کند</small></p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-primary mb-3">
                                <div class="card-header bg-primary text-white">
                                    <i class="fas fa-cogs"></i> راه‌حل بلندمدت (Long-term)
                                </div>
                                <div class="card-body">
                                    <h5>2. فعال‌سازی محاسبه خودکار</h5>
                                    <div class="code-block">
def save(self, *args, **kwargs):
    # محاسبه خودکار unit_amount با فرمول جدید
    if not self.unit_amount:
        if self.deposit_type == 'Poll':
            if self.total_debt_amount and self.lottery_month_count and self.lottery_per_month_count:
                total_shares = self.lottery_month_count * self.lottery_per_month_count
                self.unit_amount = self.total_debt_amount / total_shares
        elif self.deposit_type == 'Saving':
            # منطق محاسبه برای پس‌انداز
            if not self.unit_amount:
                raise ValidationError("مبلغ واحد سهم الزامی است")

    super().save(*args, **kwargs)
                                    </div>
                                    <p><small><i class="fas fa-robot"></i> محاسبه خودکار مشکل را از ریشه حل می‌کند</small></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-warning mb-3">
                                <div class="card-header bg-warning text-white">
                                    <i class="fas fa-user-shield"></i> بهبود Admin Panel
                                </div>
                                <div class="card-body">
                                    <h5>3. Validation در Admin</h5>
                                    <div class="code-block">
class DepositAdminForm(forms.ModelForm):
    def clean(self):
        cleaned_data = super().clean()
        deposit_type = cleaned_data.get('deposit_type')

        if deposit_type == 'Poll':
            total_debt = cleaned_data.get('total_debt_amount')
            lottery_months = cleaned_data.get('lottery_month_count')

            if not total_debt or not lottery_months:
                raise forms.ValidationError(
                    "برای صندوق قرعه‌کشی، مبلغ کل و تعداد ماه الزامی است"
                )

            # محاسبه خودکار
            cleaned_data['unit_amount'] = total_debt / lottery_months

        return cleaned_data
                                    </div>
                                    <p><small><i class="fas fa-user-check"></i> Admin نمی‌تواند صندوق ناقص ایجاد کند</small></p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-info mb-3">
                                <div class="card-header bg-info text-white">
                                    <i class="fas fa-code"></i> بهبود Services
                                </div>
                                <div class="card-body">
                                    <h5>4. Validation در PaymentCalculationService</h5>
                                    <div class="code-block">
@staticmethod
def calculate_payment_amount(deposit, membership):
    # بررسی unit_amount
    if deposit.unit_amount is None:
        raise ValueError(
            f"صندوق {deposit.title} مبلغ واحد سهم ندارد"
        )

    if deposit.unit_amount <= 0:
        raise ValueError(
            f"مبلغ واحد سهم صندوق {deposit.title} نامعتبر است"
        )

    amount = deposit.unit_amount
    return max(amount, Decimal('0.01'))
                                    </div>
                                    <p><small><i class="fas fa-shield-alt"></i> پیش از محاسبه، اعتبارسنجی انجام می‌شود</small></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h4><i class="fas fa-database text-primary"></i> اصلاح داده‌های موجود</h4>

                        <div class="alert alert-warning" role="alert">
                            <h5><i class="fas fa-exclamation-triangle"></i> داده‌های فعلی مشکل‌دار</h5>
                            <p>در حال حاضر 8 صندوق Poll و 5 صندوق Saving مشکل دارند که باید اصلاح شوند:</p>
                        </div>

                        <div class="code-block">
# اسکریپت اصلاح داده‌های موجود
from apps.deposit.models import PollDeposit, SavingDeposit

# اصلاح صندوق‌های Poll
poll_deposits = PollDeposit.objects.filter(
    lottery_month_count__isnull=True
).exclude(total_debt_amount__isnull=True)

for deposit in poll_deposits:
    if deposit.total_debt_amount and not deposit.lottery_month_count:
        # تخمین تعداد ماه بر اساس منطق کسب‌وکار
        deposit.lottery_month_count = 12  # پیش‌فرض 12 ماه
        deposit.unit_amount = deposit.total_debt_amount / deposit.lottery_month_count
        deposit.save()
        print(f"اصلاح شد: {deposit.title}")

# اصلاح صندوق‌های Saving
saving_deposits = SavingDeposit.objects.filter(
    validity_duration__isnull=True
).exclude(unit_amount__isnull=True)

for deposit in saving_deposits:
    if not deposit.validity_duration:
        deposit.validity_duration = 24  # پیش‌فرض 2 سال
        deposit.save()
        print(f"اصلاح شد: {deposit.title}")
                        </div>

                        <div class="alert alert-info mt-3" role="alert">
                            <p><i class="fas fa-info-circle"></i> این اسکریپت باید توسط Admin اجرا شود تا داده‌های موجود اصلاح شوند.</p>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h4><i class="fas fa-check-double text-success"></i> تست راه‌حل‌ها</h4>

                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-vial text-primary"></i> تست سناریو مشکل‌دار</h5>
                                <div class="code-block">
# ایجاد صندوق ناقص (قبل از اصلاح)
deposit = PollDeposit.objects.create(
    title="تست",
    total_debt_amount=1000000,
    # lottery_month_count=None
)

# تست پرداخت
try:
    payment_info = PaymentCalculationService.get_payment_info(
        deposit, membership
    )
except ValueError as e:
    print(f"خطا: {e}")
    # خطا: صندوق تست مبلغ واحد سهم ندارد
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5><i class="fas fa-check-circle text-success"></i> تست سناریو اصلاح شده</h5>
                                <div class="code-block">
# ایجاد صندوق کامل (بعد از اصلاح)
deposit = PollDeposit.objects.create(
    title="تست اصلاح شده",
    total_debt_amount=1000000,
    lottery_month_count=10
    # unit_amount خودکار محاسبه می‌شه: 100000
)

# تست پرداخت
payment_info = PaymentCalculationService.get_payment_info(
    deposit, membership
)
print(f"مبلغ پرداخت: {payment_info['total_amount']}")
# مبلغ پرداخت: 100000.00
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Models -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-database icon"></i>مدل‌های کلیدی و روابط</h2>
                </div>
                <div class="section-content">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="model-relationship">
                                <h4><i class="fas fa-piggy-bank"></i> Deposit (صندوق)</h4>
                                <ul class="field-list">
                                    <li>
                                        <span class="field-name">unit_amount:</span>
                                        <span class="field-desc">مبلغ هر واحد سهم</span>
                                    </li>
                                    <li>
                                        <span class="field-name">deposit_type:</span>
                                        <span class="field-desc">نوع صندوق (Poll/Saving/Reporting)</span>
                                    </li>
                                    <li>
                                        <span class="field-name">payment_cycle:</span>
                                        <span class="field-desc">چرخه پرداخت (روز ماه)</span>
                                    </li>
                                    <li>
                                        <span class="field-name">total_debt_amount:</span>
                                        <span class="field-desc">کل مبلغ صندوق</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="model-relationship">
                                <h4><i class="fas fa-users"></i> DepositMembership (عضویت)</h4>
                                <ul class="field-list">
                                    <li>
                                        <span class="field-name">user:</span>
                                        <span class="field-desc">کاربر عضو</span>
                                    </li>
                                    <li>
                                        <span class="field-name">deposit:</span>
                                        <span class="field-desc">صندوق مربوطه</span>
                                    </li>
                                    <li>
                                        <span class="field-name">requested_unit_count:</span>
                                        <span class="field-desc">تعداد سهم درخواستی</span>
                                    </li>
                                    <li>
                                        <span class="field-name">monthly_installment_amount:</span>
                                        <span class="field-desc">مبلغ قسط ماهانه</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="model-relationship">
                                <h4><i class="fas fa-credit-card"></i> Payment (پرداخت)</h4>
                                <ul class="field-list">
                                    <li>
                                        <span class="field-name">amount:</span>
                                        <span class="field-desc">مبلغ اصلی پرداخت</span>
                                    </li>
                                    <li>
                                        <span class="field-name">loan_installment_amount:</span>
                                        <span class="field-desc">مبلغ قسط وام</span>
                                    </li>
                                    <li>
                                        <span class="field-name">total_amount:</span>
                                        <span class="field-desc">مبلغ کل (amount + loan_installment_amount)</span>
                                    </li>
                                    <li>
                                        <span class="field-name">status:</span>
                                        <span class="field-desc">وضعیت پرداخت</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="model-relationship">
                                <h4><i class="fas fa-receipt"></i> Transaction (تراکنش)</h4>
                                <ul class="field-list">
                                    <li>
                                        <span class="field-name">amount:</span>
                                        <span class="field-desc">مبلغ تراکنش</span>
                                    </li>
                                    <li>
                                        <span class="field-name">transaction_type:</span>
                                        <span class="field-desc">نوع تراکنش (INCOME/OUTCOME)</span>
                                    </li>
                                    <li>
                                        <span class="field-name">due_date:</span>
                                        <span class="field-desc">سررسید مربوطه</span>
                                    </li>
                                    <li>
                                        <span class="field-name">status:</span>
                                        <span class="field-desc">وضعیت تراکنش</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="model-relationship">
                                <h4><i class="fas fa-hand-holding-usd"></i> Loan & LoanInstallment</h4>
                                <ul class="field-list">
                                    <li>
                                        <span class="field-name">Loan:</span>
                                        <span class="field-desc">اطلاعات کلی وام</span>
                                    </li>
                                    <li>
                                        <span class="field-name">LoanInstallment:</span>
                                        <span class="field-desc">اقساط وام با تاریخ سررسید</span>
                                    </li>
                                    <li>
                                        <span class="field-name">amount:</span>
                                        <span class="field-desc">مبلغ هر قسط</span>
                                    </li>
                                    <li>
                                        <span class="field-name">is_paid:</span>
                                        <span class="field-desc">وضعیت پرداخت قسط</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="model-relationship">
                                <h4><i class="fas fa-calendar-alt"></i> DepositDueDate (سررسیدها)</h4>
                                <ul class="field-list">
                                    <li>
                                        <span class="field-name">due_date_number:</span>
                                        <span class="field-desc">شماره سررسید</span>
                                    </li>
                                    <li>
                                        <span class="field-name">due_date:</span>
                                        <span class="field-desc">تاریخ سررسید</span>
                                    </li>
                                    <li>
                                        <span class="field-name">is_completed:</span>
                                        <span class="field-desc">آیا تکمیل شده؟</span>
                                    </li>
                                    <li>
                                        <span class="field-name">is_lottery_completed:</span>
                                        <span class="field-desc">آیا قرعه‌کشی انجام شده؟</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Endpoints -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-plug icon"></i>API Endpoints</h2>
                </div>
                <div class="section-content">

                    <ul class="nav nav-tabs" id="apiTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="payment-info-tab" data-bs-toggle="tab" data-bs-target="#payment-info" type="button" role="tab">اطلاعات پرداخت</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="payment-verify-tab" data-bs-toggle="tab" data-bs-target="#payment-verify" type="button" role="tab">تأیید پرداخت</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="payment-status-tab" data-bs-toggle="tab" data-bs-target="#payment-status" type="button" role="tab">وضعیت پرداخت</button>
                        </li>
                    </ul>

                    <div class="tab-content" id="apiTabsContent">
                        <div class="tab-pane fade show active" id="payment-info" role="tabpanel">
                            <div class="api-endpoint">
                                <span class="method">GET</span>
                                <div class="url">/api/payments/info/{deposit_id}/</div>
                            </div>
                            <p><strong>توضیح:</strong> دریافت اطلاعات پرداخت برای یک صندوق خاص</p>

                            <h5>Response:</h5>
                            <div class="code-block">
{
    "payment_id": 123,
    "deposit_id": 1,
    "deposit_name": "صندوق خانوادگی",
    "payment_amount": 1000000.00,
    "has_loan_installment": true,
    "loan_installment_amount": 500000.00,
    "total_amount": 1500000.00,
    "current_due_date": "2024-02-15",
    "payment_url": "https://www.zarinpal.com/pg/StartPay/xxx",
    "authority": "A00000000000000000000000000123456789"
}
                            </div>
                        </div>

                        <div class="tab-pane fade" id="payment-verify" role="tabpanel">
                            <div class="api-endpoint">
                                <span class="method">GET</span>
                                <div class="url">/payments/verify/?Authority=xxx&Status=OK</div>
                            </div>
                            <p><strong>توضیح:</strong> تأیید پرداخت پس از بازگشت از درگاه</p>

                            <h5>Parameters:</h5>
                            <ul>
                                <li><strong>Authority:</strong> کد اختیار دریافتی از زرین‌پال</li>
                                <li><strong>Status:</strong> وضعیت پرداخت (OK/NOK)</li>
                            </ul>

                            <h5>Response:</h5>
                            <p>صفحه HTML موفقیت یا خطا</p>
                        </div>

                        <div class="tab-pane fade" id="payment-status" role="tabpanel">
                            <div class="api-endpoint">
                                <span class="method">GET</span>
                                <div class="url">/api/payments/status/{payment_id}/</div>
                            </div>
                            <p><strong>توضیح:</strong> بررسی وضعیت یک پرداخت خاص</p>

                            <h5>Response:</h5>
                            <div class="code-block">
{
    "payment_id": 123,
    "status": "VERIFIED",
    "amount": 1500000.00,
    "reference_number": "12345678",
    "payment_date": "2024-01-15T10:30:00Z",
    "deposit_title": "صندوق خانوادگی"
}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-clipboard-check icon"></i>خلاصه دلایل پرداخت</h2>
                </div>
                <div class="section-content">

                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-primary mb-3">
                                <div class="card-header bg-primary text-white">
                                    <i class="fas fa-dice"></i> صندوق قرعه‌کشی
                                </div>
                                <div class="card-body">
                                    <p class="card-text">پرداخت ماهانه برای شرکت در قرعه‌کشی و دریافت مبلغ کلان</p>
                                    <small class="text-muted">هدف: دریافت مبلغ کلان بدون وام</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-success mb-3">
                                <div class="card-header bg-success text-white">
                                    <i class="fas fa-coins"></i> صندوق پس‌انداز
                                </div>
                                <div class="card-body">
                                    <p class="card-text">پرداخت ماهانه برای پس‌انداز بلندمدت با سود</p>
                                    <small class="text-muted">هدف: پس‌انداز منظم با سود</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-warning mb-3">
                                <div class="card-header bg-warning text-white">
                                    <i class="fas fa-chart-line"></i> صندوق گزارشی
                                </div>
                                <div class="card-body">
                                    <p class="card-text">پرداخت برای گزارش‌دهی و شفافیت مالی</p>
                                    <small class="text-muted">هدف: ردیابی و کنترل مالی</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <i class="fas fa-hand-holding-usd"></i> قسط وام
                                </div>
                                <div class="card-body">
                                    <p class="card-text">بازپرداخت وام دریافتی از صندوق به صورت اقساط ماهانه</p>
                                    <small class="text-muted">هدف: بازپرداخت وام دریافتی</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <i class="fas fa-calendar-alt"></i> سررسید
                                </div>
                                <div class="card-body">
                                    <p class="card-text">پرداخت در تاریخ‌های مشخص شده برای هر صندوق</p>
                                    <small class="text-muted">هدف: انضباط مالی و پرداخت منظم</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4" role="alert">
                        <h4 class="alert-heading"><i class="fas fa-lightbulb"></i> نکته مهم</h4>
                        <p>این سیستم به کاربران امکان مدیریت مالی منظم و مشارکت در صندوق‌های جمعی را می‌دهد. هر پرداخت ممکن است شامل مبلغ اصلی صندوق و قسط وام باشد.</p>
                        <hr>
                        <p class="mb-0">فرمول نهایی: <strong>مبلغ کل = مبلغ صندوق + قسط وام</strong></p>
                    </div>
                </div>
            </div>

            <!-- Technical Details -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-code icon"></i>جزئیات فنی</h2>
                </div>
                <div class="section-content">

                    <div class="row">
                        <div class="col-md-6">
                            <h4><i class="fas fa-shield-alt text-success"></i> امنیت</h4>
                            <ul>
                                <li>استفاده از HTTPS برای تمام درخواست‌ها</li>
                                <li>احراز هویت کاربر برای دسترسی به API</li>
                                <li>بررسی عضویت فعال در صندوق</li>
                                <li>تأیید پرداخت با درگاه زرین‌پال</li>
                                <li>ذخیره امن اطلاعات پرداخت</li>
                            </ul>
                        </div>

                        <div class="col-md-6">
                            <h4><i class="fas fa-cogs text-primary"></i> عملکرد</h4>
                            <ul>
                                <li>محاسبه سریع مبلغ پرداخت</li>
                                <li>پردازش همزمان چندین پرداخت</li>
                                <li>ذخیره موثر در دیتابیس</li>
                                <li>نوتیفیکیشن فوری به کاربران</li>
                                <li>گزارش‌گیری لحظه‌ای</li>
                            </ul>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4><i class="fas fa-flow-chart text-info"></i> معماری سیستم</h4>
                            <div class="code-block">
Frontend (React/Vue)
    ↓ API Call
Django REST API
    ↓ Business Logic
PaymentCalculationService
    ↓ Database Query
Models (Deposit, Payment, Transaction, Loan)
    ↓ Payment Gateway
Zarinpal API
    ↓ Verification
PaymentTransactionService
    ↓ Notification
NotificationService
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center py-4 bg-light">
            <p class="mb-0 text-muted">
                <i class="fas fa-code"></i>
                مستندات بیزینس لاجیک سیستم پرداخت - نسخه 1.0
                <i class="fas fa-heart text-danger"></i>
            </p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Smooth scrolling for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add animation to sections on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all sections
        document.querySelectorAll('.section').forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(20px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });

        // Copy code functionality
        document.querySelectorAll('.code-block').forEach(block => {
            block.addEventListener('click', function() {
                navigator.clipboard.writeText(this.textContent).then(() => {
                    // Show temporary success message
                    const originalBg = this.style.backgroundColor;
                    this.style.backgroundColor = '#28a745';
                    setTimeout(() => {
                        this.style.backgroundColor = originalBg;
                    }, 200);
                });
            });

            // Add cursor pointer
            block.style.cursor = 'pointer';
            block.title = 'کلیک کنید تا کپی شود';
        });
    </script>
</body>
</html>

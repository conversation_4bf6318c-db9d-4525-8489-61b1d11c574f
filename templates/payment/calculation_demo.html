<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ماشین حساب پرداخت</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .calculator-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 800px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .calculator-form {
            padding: 30px;
        }
        
        .result-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }
        
        .result-card.show {
            display: block;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .amount-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .amount-breakdown {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        .error-alert {
            display: none;
        }
        
        .error-alert.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="calculator-container">
        <div class="header">
            <h1><i class="fas fa-calculator"></i> ماشین حساب پرداخت</h1>
            <p>محاسبه دقیق مبلغ پرداخت برای صندوق‌های شما</p>
        </div>

        <div class="calculator-form">
            <form id="calculatorForm">
                {% csrf_token %}
                
                <div class="mb-3">
                    <label for="depositSelect" class="form-label">
                        <i class="fas fa-piggy-bank text-primary"></i>
                        انتخاب صندوق
                    </label>
                    <select class="form-select" id="depositSelect" name="deposit_id" required>
                        <option value="">صندوق مورد نظر را انتخاب کنید</option>
                        {% for deposit in user_deposits %}
                        <option value="{{ deposit.id }}" 
                                data-type="{{ deposit.deposit_type }}"
                                data-unit-amount="{{ deposit.unit_amount }}">
                            {{ deposit.title }} ({{ deposit.get_deposit_type_display }})
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <button type="submit" class="btn btn-primary btn-lg w-100">
                    <span class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        در حال محاسبه...
                    </span>
                    <span class="btn-text">
                        <i class="fas fa-calculator"></i>
                        محاسبه مبلغ پرداخت
                    </span>
                </button>
            </form>

            <div class="alert alert-danger error-alert" id="errorAlert">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="errorMessage"></span>
            </div>

            <div class="result-card" id="resultCard">
                <h3 class="mb-3">
                    <i class="fas fa-receipt text-success"></i>
                    نتیجه محاسبه
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="amount-breakdown">
                            <h5><i class="fas fa-info-circle text-info"></i> اطلاعات صندوق</h5>
                            <p><strong>نام صندوق:</strong> <span id="depositName">-</span></p>
                            <p><strong>نوع صندوق:</strong> <span id="depositType">-</span></p>
                            <p><strong>تاریخ سررسید:</strong> <span id="dueDate">-</span></p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="amount-breakdown">
                            <h5><i class="fas fa-money-bill-wave text-success"></i> جزئیات مبلغ</h5>
                            <p><strong>مبلغ اصلی:</strong> <span id="paymentAmount">-</span> تومان</p>
                            <p><strong>قسط وام:</strong> <span id="loanAmount">-</span> تومان</p>
                            <div class="border-top pt-2 mt-2">
                                <p class="amount-display">
                                    <strong>مبلغ کل:</strong> <span id="totalAmount">-</span> تومان
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3" id="loanInfo" style="display: none;">
                    <i class="fas fa-hand-holding-usd"></i>
                    این پرداخت شامل قسط وام شما نیز می‌باشد.
                </div>

                <div class="text-center mt-3">
                    <button class="btn btn-success btn-lg" onclick="proceedToPayment()">
                        <i class="fas fa-credit-card"></i>
                        ادامه پرداخت
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('calculatorForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const form = this;
            const formData = new FormData(form);
            const loadingSpan = document.querySelector('.loading');
            const btnText = document.querySelector('.btn-text');
            const errorAlert = document.getElementById('errorAlert');
            const resultCard = document.getElementById('resultCard');
            
            // Show loading state
            loadingSpan.classList.add('show');
            btnText.style.display = 'none';
            errorAlert.classList.remove('show');
            resultCard.classList.remove('show');
            
            // Make AJAX request
            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                // Update result display
                document.getElementById('depositName').textContent = data.deposit_name;
                document.getElementById('depositType').textContent = data.deposit_type;
                document.getElementById('dueDate').textContent = data.current_due_date || 'تعریف نشده';
                document.getElementById('paymentAmount').textContent = data.payment_amount_formatted;
                document.getElementById('loanAmount').textContent = data.loan_installment_amount_formatted;
                document.getElementById('totalAmount').textContent = data.total_amount_formatted;
                
                // Show/hide loan info
                const loanInfo = document.getElementById('loanInfo');
                if (data.has_loan_installment) {
                    loanInfo.style.display = 'block';
                } else {
                    loanInfo.style.display = 'none';
                }
                
                // Show result card
                resultCard.classList.add('show');
                
                // Store data for payment
                window.paymentData = data;
            })
            .catch(error => {
                document.getElementById('errorMessage').textContent = error.message;
                errorAlert.classList.add('show');
            })
            .finally(() => {
                // Hide loading state
                loadingSpan.classList.remove('show');
                btnText.style.display = 'inline';
            });
        });
        
        function proceedToPayment() {
            if (window.paymentData) {
                const depositId = document.getElementById('depositSelect').value;
                // Redirect to actual payment page
                window.location.href = `/api/payments/info/${depositId}/`;
            }
        }
        
        // Add some visual feedback for select change
        document.getElementById('depositSelect').addEventListener('change', function() {
            const resultCard = document.getElementById('resultCard');
            const errorAlert = document.getElementById('errorAlert');
            
            resultCard.classList.remove('show');
            errorAlert.classList.remove('show');
        });
    </script>
</body>
</html>

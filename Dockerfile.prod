# pull official base image
FROM python:3.9-alpine

# set work directory
WORKDIR /usr/src/app

# set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# install psycopg2 dependencies
RUN apk update && apk add --no-cache \
    git \
    wget \
    unzip \
    curl \
    postgresql-dev \
    gcc \
    python3-dev \
    musl-dev \
    jpeg-dev \
    zlib-dev \
    freetype-dev \
    gnupg \
    chromium \
    chromium-chromedriver \
    harfbuzz \
    nss \
    freetype \
    ttf-freefont \
    mesa-gl \
    alsa-lib


# Set environment variables for Chrome
ENV CHROME_BIN=/usr/bin/chromium-browser
ENV CHROME_DRIVER=/usr/bin/chromedriver

# install dependencies
RUN pip install --upgrade pip
#RUN python -m pip install Pillow

COPY ./requirements.txt .
COPY .env.prod .env

RUN --mount=type=cache,target=/root/.cache  pip install -r requirements.txt

# copy entrypoint.sh
COPY ./entrypoint.sh .
RUN sed -i 's/\r$//g' /usr/src/app/entrypoint.sh
RUN chmod +x /usr/src/app/entrypoint.sh

# copy project
COPY . .

# # Create static files directory with proper permissions
# RUN mkdir -p /usr/src/app/static/staticfiles
# RUN mkdir -p /usr/src/app/static/media
# RUN chmod -R 755 /usr/src/app/static

# Set display port to avoid crash
ENV DISPLAY=:99

# Set Django settings for production
ENV DJANGO_SETTINGS_MODULE=config.settings.production

# run entrypoint.sh
ENTRYPOINT ["/usr/src/app/entrypoint.sh"]
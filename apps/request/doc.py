from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status

# ============================================================================
# STRUCTURED SWAGGER DOCUMENTATION FOR REQUEST APP
# ============================================================================

# Create Poll Deposit Request Swagger Documentation
create_poll_deposit_swagger = swagger_auto_schema(
    operation_description="""Create a new poll deposit request. Poll deposits are lottery-based deposits where members contribute monthly and receive funds through lottery draws.

**New Calculation Formula:**
- Total Shares = lottery_month_count × lottery_per_month_count
- Unit Amount = total_debt_amount ÷ Total Shares
- Monthly Installment = unit_amount × requested_unit_count

**Example:**
- 12 months × 2 lotteries/month = 24 total shares
- 12,000,000 IRR ÷ 24 shares = 500,000 IRR per share
- Member with 2 shares pays: 500,000 × 2 = 1,000,000 IRR monthly""",
    operation_summary="Create Poll Deposit Request",
    tags=['Request'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['title', 'description', 'total_debt_amount', 'lottery_month_count', 'unit_amount', 'payment_cycle', 'max_unit_per_request', 'max_members_count', 'initial_lottery_date'],
        properties={
            'title': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Deposit title',
                example='صندوق قرض‌الحسنه خانوادگی'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Detailed description of the deposit',
                example='این صندوق برای کمک به خانواده‌ها ایجاد شده است'
            ),
            'total_debt_amount': openapi.Schema(
                type=openapi.TYPE_NUMBER,
                description='Total loan amount in Rials (will be divided among all shares)',
                example=12000000.0
            ),
            'lottery_month_count': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Number of months for lottery draws',
                example=12
            ),
            'lottery_per_month_count': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Number of lotteries per month (total shares = lottery_month_count × lottery_per_month_count)',
                example=2
            ),
            'unit_amount': openapi.Schema(
                type=openapi.TYPE_NUMBER,
                description='Amount per unit in Rials (auto-calculated: total_debt_amount ÷ total_shares)',
                example=500000.0
            ),
            'payment_cycle': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Payment cycle in days',
                example=30
            ),
            'max_unit_per_request': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Maximum units per request',
                example=5
            ),
            'max_members_count': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Maximum number of members',
                example=100
            ),
            'initial_lottery_date': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                description='Initial lottery date',
                example='2023-12-15'
            ),
            'rules': openapi.Schema(
                type=openapi.TYPE_ARRAY,
                description='List of deposit rules',
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'subject': openapi.Schema(type=openapi.TYPE_STRING, example='قانون اول'),
                        'description': openapi.Schema(type=openapi.TYPE_STRING, example='توضیحات قانون اول')
                    }
                )
            )
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Poll deposit request created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'title': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق قرض‌الحسنه خانوادگی'),
                    'description': openapi.Schema(type=openapi.TYPE_STRING, example='این صندوق برای کمک به خانواده‌ها ایجاد شده است'),
                    'deposit_type': openapi.Schema(type=openapi.TYPE_STRING, example='Poll'),
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='pending'),
                    'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z')
                }
            ),
            examples={
                'application/json': {
                    "id": 1,
                    "title": "صندوق قرعه‌کشی دوهفته‌ای",
                    "description": "صندوق 12 ماهه با قرعه‌کشی هر دو هفته یکبار",
                    "deposit_type": "Poll",
                    "total_debt_amount": 12000000.0,
                    "lottery_month_count": 12,
                    "lottery_per_month_count": 2,
                    "unit_amount": 500000.0,
                    "total_shares": 24,
                    "status": "pending",
                    "created_at": "2024-01-01T10:00:00Z"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'field_name': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['This field is required.']
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Create Saving Deposit Request Swagger Documentation
create_saving_deposit_swagger = swagger_auto_schema(
    operation_description="Create a new saving deposit request. Saving deposits are long-term savings accounts where members contribute regularly to build savings over time.",
    operation_summary="Create Saving Deposit Request",
    tags=['Request'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['title', 'description', 'unit_amount', 'payment_cycle', 'max_unit_per_request', 'max_members_count', 'validity_duration'],
        properties={
            'title': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Deposit title',
                example='صندوق پس‌انداز خانوادگی'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Detailed description of the deposit',
                example='این صندوق برای پس‌انداز خانواده‌ها ایجاد شده است'
            ),
            'unit_amount': openapi.Schema(
                type=openapi.TYPE_NUMBER,
                description='Amount per unit in Rials',
                example=500000.0
            ),
            'payment_cycle': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Payment cycle in days',
                example=30
            ),
            'max_unit_per_request': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Maximum units per request',
                example=10
            ),
            'max_members_count': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Maximum number of members',
                example=50
            ),
            'validity_duration': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Validity duration in months',
                example=24
            ),
            'rules': openapi.Schema(
                type=openapi.TYPE_ARRAY,
                description='List of deposit rules',
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'subject': openapi.Schema(type=openapi.TYPE_STRING, example='قانون پس‌انداز'),
                        'description': openapi.Schema(type=openapi.TYPE_STRING, example='توضیحات قانون پس‌انداز')
                    }
                )
            )
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Saving deposit request created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=2),
                    'title': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق پس‌انداز خانوادگی'),
                    'description': openapi.Schema(type=openapi.TYPE_STRING, example='این صندوق برای پس‌انداز خانواده‌ها ایجاد شده است'),
                    'deposit_type': openapi.Schema(type=openapi.TYPE_STRING, example='Saving'),
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='pending'),
                    'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z')
                }
            ),
            examples={
                'application/json': {
                    "id": 2,
                    "title": "صندوق پس‌انداز خانوادگی",
                    "description": "این صندوق برای پس‌انداز خانواده‌ها ایجاد شده است",
                    "deposit_type": "Saving",
                    "status": "pending",
                    "created_at": "2023-01-01T10:00:00Z"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'field_name': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['This field is required.']
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Create Reporting Deposit Request Swagger Documentation
create_reporting_deposit_swagger = swagger_auto_schema(
    operation_description="Create a new reporting deposit request. Reporting deposits are used for tracking and reporting financial activities with specific debt amounts.",
    operation_summary="Create Reporting Deposit Request",
    tags=['Request'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['title', 'description', 'total_debt_amount', 'unit_amount', 'payment_cycle', 'max_unit_per_request', 'max_members_count'],
        properties={
            'title': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Deposit title',
                example='صندوق گزارش‌دهی مالی'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Detailed description of the deposit',
                example='این صندوق برای گزارش‌دهی فعالیت‌های مالی ایجاد شده است'
            ),
            'total_debt_amount': openapi.Schema(
                type=openapi.TYPE_NUMBER,
                description='Total debt amount in Rials',
                example=50000000.0
            ),
            'unit_amount': openapi.Schema(
                type=openapi.TYPE_NUMBER,
                description='Amount per unit in Rials',
                example=250000.0
            ),
            'payment_cycle': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Payment cycle in days',
                example=30
            ),
            'max_unit_per_request': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Maximum units per request',
                example=8
            ),
            'max_members_count': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Maximum number of members',
                example=80
            ),
            'rules': openapi.Schema(
                type=openapi.TYPE_ARRAY,
                description='List of deposit rules',
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'subject': openapi.Schema(type=openapi.TYPE_STRING, example='قانون گزارش‌دهی'),
                        'description': openapi.Schema(type=openapi.TYPE_STRING, example='توضیحات قانون گزارش‌دهی')
                    }
                )
            )
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Reporting deposit request created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=3),
                    'title': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق گزارش‌دهی مالی'),
                    'description': openapi.Schema(type=openapi.TYPE_STRING, example='این صندوق برای گزارش‌دهی فعالیت‌های مالی ایجاد شده است'),
                    'deposit_type': openapi.Schema(type=openapi.TYPE_STRING, example='Reporting'),
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='pending'),
                    'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z')
                }
            ),
            examples={
                'application/json': {
                    "id": 3,
                    "title": "صندوق گزارش‌دهی مالی",
                    "description": "این صندوق برای گزارش‌دهی فعالیت‌های مالی ایجاد شده است",
                    "deposit_type": "Reporting",
                    "status": "pending",
                    "created_at": "2023-01-01T10:00:00Z"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'field_name': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['This field is required.']
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Request Join Deposit Swagger Documentation
request_join_deposit_swagger = swagger_auto_schema(
    operation_description="Create a request to join an existing deposit. Users can request to become members of deposits created by others.",
    operation_summary="Request to Join Deposit",
    tags=['Request'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['requested_unit_count'],
        properties={
            'requested_unit_count': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Number of units requested to join',
                example=3
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Optional description for the join request',
                example='I would like to join this deposit for savings'
            )
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Join request created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'user': openapi.Schema(type=openapi.TYPE_INTEGER, example=123),
                    'deposit': openapi.Schema(type=openapi.TYPE_INTEGER, example=456),
                    'requested_unit_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=3),
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='pending'),
                    'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z')
                }
            ),
            examples={
                'application/json': {
                    "id": 1,
                    "user": 123,
                    "deposit": 456,
                    "requested_unit_count": 3,
                    "status": "pending",
                    "created_at": "2023-01-01T10:00:00Z"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error or user already has a request",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='You already have a request for this deposit.'
                    )
                }
            )
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Deposit not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Deposit not found.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Deposit Join Requests List Swagger Documentation
deposit_join_requests_list_swagger = swagger_auto_schema(
    operation_description="Get list of join requests for a specific deposit with filtering and ordering options",
    operation_summary="List Deposit Join Requests",
    tags=['Request'],
    manual_parameters=[
        openapi.Parameter(
            'status',
            openapi.IN_QUERY,
            description="Filter by status (pending, approved, rejected)",
            type=openapi.TYPE_STRING,
            enum=['pending', 'approved', 'rejected'],
            required=False
        ),
        openapi.Parameter(
            'ordering',
            openapi.IN_QUERY,
            description="Order by field. Prefix with '-' for descending order. e.g. '-created_at' or use 'status_priority' for default status ordering",
            type=openapi.TYPE_STRING,
            required=False,
            example='-created_at'
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of join requests retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                        'user': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=123),
                                'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='John Doe'),
                                'phone_number': openapi.Schema(type=openapi.TYPE_STRING, example='+989012345678')
                            }
                        ),
                        'deposit': openapi.Schema(type=openapi.TYPE_INTEGER, example=456),
                        'requested_unit_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=3),
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example='pending'),
                        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z'),
                        'updated_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z')
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "id": 1,
                        "user": {
                            "id": 123,
                            "fullname": "John Doe",
                            "phone_number": "+989012345678"
                        },
                        "deposit": 456,
                        "requested_unit_count": 3,
                        "status": "pending",
                        "created_at": "2023-01-01T10:00:00Z",
                        "updated_at": "2023-01-01T10:00:00Z"
                    }
                ]
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Request Join Deposit Swagger Documentation
request_join_deposit_swagger = swagger_auto_schema(
    operation_description="Create a request to join an existing deposit. Users can request to become members of deposits created by others.",
    operation_summary="Request to Join Deposit",
    tags=['Request'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['requested_unit_count'],
        properties={
            'requested_unit_count': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Number of units requested to join',
                example=3
            )
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Join request created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'user': openapi.Schema(type=openapi.TYPE_INTEGER, example=123),
                    'deposit': openapi.Schema(type=openapi.TYPE_INTEGER, example=456),
                    'requested_unit_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=3),
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='pending'),
                    'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z')
                }
            )
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error or user already has a request",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='You already have a request for this deposit.'
                    )
                }
            )
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Deposit not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Deposit not found.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Deposit Join Requests List Swagger Documentation
deposit_join_requests_list_swagger = swagger_auto_schema(
    operation_description="Get list of join requests for a specific deposit with filtering and ordering options",
    operation_summary="List Deposit Join Requests",
    tags=['Request'],
    manual_parameters=[
        openapi.Parameter(
            'status',
            openapi.IN_QUERY,
            description="Filter by status (pending, approved, rejected)",
            type=openapi.TYPE_STRING,
            enum=['pending', 'approved', 'rejected'],
            required=False
        ),
        openapi.Parameter(
            'ordering',
            openapi.IN_QUERY,
            description="Order by field. Prefix with '-' for descending order",
            type=openapi.TYPE_STRING,
            required=False,
            example='-created_at'
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of join requests retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                        'user': openapi.Schema(type=openapi.TYPE_INTEGER, example=123),
                        'deposit': openapi.Schema(type=openapi.TYPE_INTEGER, example=456),
                        'requested_unit_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=3),
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example='pending'),
                        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z')
                    }
                )
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

def doc_request_poll_deposit_create():
    return """
# 🐈 Scenario
🛠️ ایجاد سپرده جدید

کاربر می‌تواند با ارسال درخواست، یک سپرده جدید ایجاد کند. این سپرده می‌تواند از انواع مختلفی مانند سپرده قرض‌الحسنه، سپرده سرمایه‌گذاری و غیره باشد.

---
## توضیحات سناریو

### توضیحات فیلدها به همراه مثال:

2. **region**: منطقه‌ای که سپرده به آن تعلق دارد. مثلاً: `1` (منطقه ۱).
3. **title**: عنوان سپرده. مثلاً: `سپرده قرض‌الحسنه شرکت X`.
4. **description**: توضیحات کامل در مورد سپرده. مثلاً: `این سپرده برای کمک به کارمندان شرکت X ایجاد شده است.`
5. **total_debt_amount**: مبلغ کل وام. مثلاً: `100,000,000 ریال`.
6. **lottery_month_count**: تعداد ماه‌های قرعه‌کشی. مثلاً: `12 ماه`.
7. **unit_amount**: مبلغ هر سهم. مثلاً: `1,000,000 ریال`.
8. **payment_cycle**: دوره پرداخت به ماه. مثلاً: `6 ماه`.
9. **max_unit_per_request**: حداکثر سهم درخواستی. مثلاً: `10 سهم`.
10. **max_members_count**: حداکثر تعداد اعضا. مثلاً: `100 عضو`.
12. **initial_lottery_date**: تاریخ قرعه‌کشی اولیه. مثلاً: `۱۴۰۲/۰۱/۱۵`.
13. **rules**: لیستی از قوانین سپرده. هر قانون شامل دو فیلد `subject` و `description` است. مثلاً:
    ```json
    [
        {
            "subject": "قانون اول",
            "description": "توضیحات قانون اول"
        },
        {
            "subject": "قانون دوم",
            "description": "توضیحات قانون دوم"
        }
    ]
    ```

## 🚀 درخواست API


### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Bearer <توکن احراز هویت>              |

### Body:
```json
{
    "deposit_type": "Poll",
    "region": 1,
    "title": "سپرده قرض‌الحسنه شرکت X",
    "description": "این سپرده برای کمک به کارمندان شرکت X ایجاد شده است.",
    "total_debt_amount": "100000000.00",
    "lottery_month_count": 12,
    "unit_amount": "1000000.00",
    "payment_cycle": 6,
    "max_unit_per_request": 10,
    "max_members_count": 100,
    "validity_duration": 24,
    "initial_lottery_date": "2023-04-15",
    "rules": [
        {
            "subject": "قانون اول",
            "description": "توضیحات قانون اول"
        },
        {
            "subject": "قانون دوم",
            "description": "توضیحات قانون دوم"
        }
    ]
}
"""
def doc_request_saving_deposit_create():
    return """
# 🐈 Scenario
🛠️ ایجاد سپرده پس‌انداز جدید

کاربر می‌تواند با ارسال درخواست، یک سپرده پس‌انداز جدید ایجاد کند. این سپرده برای اهداف پس‌انداز و سرمایه‌گذاری طراحی شده است.

---
## توضیحات سناریو

### توضیحات فیلدها به همراه مثال:

2. **region**: منطقه‌ای که سپرده به آن تعلق دارد. مثلاً: `1` (منطقه ۱).
3. **title**: عنوان سپرده. مثلاً: `سپرده پس‌انداز شرکت X`.
4. **description**: توضیحات کامل در مورد سپرده. مثلاً: `این سپرده برای پس‌انداز کارمندان شرکت X ایجاد شده است.`
5. **total_debt_amount**: مبلغ کل وام. مثلاً: `100,000,000 ریال`.
6. **unit_amount**: مبلغ هر سهم. مثلاً: `1,000,000 ریال`.
7. **payment_cycle**: دوره پرداخت به ماه. مثلاً: `6 ماه`.
8. **max_unit_per_request**: حداکثر سهم درخواستی. مثلاً: `10 سهم`.
9. **max_members_count**: حداکثر تعداد اعضا. مثلاً: `100 عضو`.
10. **validity_duration**: مدت اعتبار سپرده به ماه. مثلاً: `24 ماه`.
11. **start_date**: تاریخ شروع سپرده. مثلاً: `۱۴۰۲/۰۱/۱۵`.
12. **rules**: لیستی از قوانین سپرده. هر قانون شامل دو فیلد `subject` و `description` است. مثلاً:
    ```json
    [
        {
            "subject": "قانون اول",
            "description": "توضیحات قانون اول"
        },
        {
            "subject": "قانون دوم",
            "description": "توضیحات قانون دوم"
        }
    ]
    ```

## 🚀 درخواست API

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Bearer <توکن احراز هویت>              |

### Body:
```json
{
    "deposit_type": "Saving",
    "region": 1,
    "title": "سپرده پس‌انداز شرکت X",
    "description": "این سپرده برای پس‌انداز کارمندان شرکت X ایجاد شده است.",
    "total_debt_amount": "100000000.00",
    "unit_amount": "1000000.00",
    "payment_cycle": 6,
    "max_unit_per_request": 10,
    "max_members_count": 100,
    "validity_duration": 24,
    "start_date": "2023-04-15",
    "rules": [
        {
            "subject": "قانون اول",
            "description": "توضیحات قانون اول"
        },
        {
            "subject": "قانون دوم",
            "description": "توضیحات قانون دوم"
        }
    ]
}
"""

def doc_request_reporting_deposit_create():
    return """
# 🐈 Scenario
🛠️ ایجاد سپرده گزارش‌گیری جدید

کاربر می‌تواند با ارسال درخواست، یک سپرده گزارش‌گیری جدید ایجاد کند. این سپرده برای اهداف گزارش‌گیری و تحلیل داده‌ها طراحی شده است.

---
## توضیحات سناریو

### توضیحات فیلدها به همراه مثال:

2. **region**: منطقه‌ای که سپرده به آن تعلق دارد. مثلاً: `1` (منطقه ۱).
3. **title**: عنوان سپرده. مثلاً: `سپرده گزارش‌گیری شرکت X`.
4. **description**: توضیحات کامل در مورد سپرده. مثلاً: `این سپرده برای تحلیل داده‌های مالی شرکت X ایجاد شده است.`
5. **total_debt_amount**: مبلغ کل وام. مثلاً: `100,000,000 ریال`.
6. **unit_amount**: مبلغ هر سهم. مثلاً: `1,000,000 ریال`.
7. **payment_cycle**: دوره پرداخت به ماه. مثلاً: `6 ماه`.
8. **max_unit_per_request**: حداکثر سهم درخواستی. مثلاً: `10 سهم`.
9. **max_members_count**: حداکثر تعداد اعضا. مثلاً: `100 عضو`.
10. **validity_duration**: مدت اعتبار سپرده به ماه. مثلاً: `24 ماه`.
11. **rules**: لیستی از قوانین سپرده. هر قانون شامل دو فیلد `subject` و `description` است. مثلاً:
    ```json
    [
        {
            "subject": "قانون اول",
            "description": "توضیحات قانون اول"
        },
        {
            "subject": "قانون دوم",
            "description": "توضیحات قانون دوم"
        }
    ]
    ```

## 🚀 درخواست API

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Bearer <توکن احراز هویت>              |

### Body:
```json
{
    "deposit_type": "Reporting",
    "region": 1,
    "title": "سپرده گزارش‌گیری شرکت X",
    "description": "این سپرده برای تحلیل داده‌های مالی شرکت X ایجاد شده است.",
    "total_debt_amount": "100000000.00",
    "unit_amount": "1000000.00",
    "payment_cycle": 6,
    "max_unit_per_request": 10,
    "max_members_count": 100,
    "validity_duration": 24,
    "rules": [
        {
            "subject": "قانون اول",
            "description": "توضیحات قانون اول"
        },
        {
            "subject": "قانون دوم",
            "description": "توضیحات قانون دوم"
        }
    ]
}
"""

# Cancel Request Join Deposit Swagger Documentation
cancel_request_join_deposit_swagger = swagger_auto_schema(
    operation_description="Cancel a pending request to join a deposit. Only the user who created the request can cancel it, and only pending requests can be cancelled.",
    operation_summary="Cancel Request to Join Deposit",
    tags=['Request'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['request_id'],
        properties={
            'request_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='ID of the RequestJoinDeposit to cancel',
                example=123
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Request cancelled successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='success'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Request cancelled successfully'
                    )
                }
            ),
            examples={
                'application/json': {
                    "status": "success",
                    "message": "Request cancelled successfully"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Bad request - request cannot be cancelled",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example="Cannot cancel request with status 'approved'. Only pending requests can be cancelled."
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Request not found or permission denied",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example="Request not found or you don't have permission to cancel it."
                    )
                }
            )
        )
    }
)
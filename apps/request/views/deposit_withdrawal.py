from rest_framework.generics import ListAP<PERSON><PERSON>iew, CreateAPIView, UpdateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.db.models import Case, When, Value, IntegerField
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from apps.request.models import WithdrawalRequest
from apps.request.serializers import (
    WithdrawalRequestSerializer,
    CreateWithdrawalRequestSerializer,
)
from apps.deposit.models import Deposit, DepositMembership
from utils.exceptions import AppAPIException


class WithdrawalRequestListView(ListAPIView):
    serializer_class = WithdrawalRequestSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status']
    ordering_fields = ['created_at']

    @swagger_auto_schema(
        operation_description="Get list of withdrawal requests for a deposit",
        manual_parameters=[
            openapi.Parameter(
                'status', 
                openapi.IN_QUERY, 
                description="Filter by status (pending, approved, rejected)", 
                type=openapi.TYPE_STRING,
                enum=[WithdrawalRequest.RequestStatus.PENDING, WithdrawalRequest.RequestStatus.APPROVED, WithdrawalRequest.RequestStatus.REJECTED]
            ),
            openapi.Parameter(
                'ordering', 
                openapi.IN_QUERY, 
                description="Order by field. Prefix with '-' for descending order. e.g. '-created_at'", 
                type=openapi.TYPE_STRING
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        deposit_id = self.kwargs['deposit_id']
        
        # Check if the deposit exists
        try:
            deposit = Deposit.objects.get(id=deposit_id)
        except Deposit.DoesNotExist:
            return WithdrawalRequest.objects.none()
        
        # Get the queryset filtered by deposit
        queryset = WithdrawalRequest.objects.filter(deposit_id=deposit_id).order_by('-created_at')
                    
        return queryset


class CreateWithdrawalRequestView(CreateAPIView):
    serializer_class = CreateWithdrawalRequestSerializer
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Create a new withdrawal request",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['deposit', 'account_holder_name', 'amount', 'iban'],
            properties={
                'deposit': openapi.Schema(type=openapi.TYPE_INTEGER, description='Deposit ID'),
                'account_holder_name': openapi.Schema(type=openapi.TYPE_STRING, description='Full name of the account holder'),
                'amount': openapi.Schema(type=openapi.TYPE_NUMBER, description='Withdrawal amount'),
                'iban': openapi.Schema(type=openapi.TYPE_STRING, description='26-digit IBAN number starting with IR'),
                'reason': openapi.Schema(type=openapi.TYPE_STRING, description='Reason for the withdrawal request'),
            }
        )
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class UserWithdrawalRequestListView(ListAPIView):
    serializer_class = WithdrawalRequestSerializer
    permission_cla1sses = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status', 'deposit']
    ordering_fields = ['created_at', 'amount']

    @swagger_auto_schema(
        operation_description="Get list of all withdrawal requests for the authenticated user",
        manual_parameters=[
            openapi.Parameter(
                'status',
                openapi.IN_QUERY,
                description="Filter by status (pending, approved, rejected)",
                type=openapi.TYPE_STRING,
                enum=[WithdrawalRequest.RequestStatus.PENDING, WithdrawalRequest.RequestStatus.APPROVED, WithdrawalRequest.RequestStatus.REJECTED]
            ),
            openapi.Parameter(
                'ordering',
                openapi.IN_QUERY,
                description="Order by field. Prefix with '-' for descending order. e.g. '-created_at', 'amount'",
                type=openapi.TYPE_STRING
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        user = self.request.user

        # Get all active deposit memberships for this user
        memberships = DepositMembership.objects.filter(
            user=user,
            is_active=True
        )

        # Get all withdrawal requests associated with these memberships
        queryset = WithdrawalRequest.objects.filter(
            deposit_membership__in=memberships
        ).order_by('-created_at')

        return queryset



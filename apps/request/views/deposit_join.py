from rest_framework import generics, permissions, status, serializers
from rest_framework.views import APIView
from rest_framework.response import Response
from apps.deposit.models import DepositMembership, Deposit
from apps.request.models import RequestJoinDeposit
from utils.exceptions import AppAPIException

from apps.request.serializers import RequestJoinDepositCreateSerializer, CancelRequestJoinDepositSerializer
from apps.request.doc import request_join_deposit_swagger, cancel_request_join_deposit_swagger


class RequestJoinDepositView(generics.CreateAPIView):
    serializer_class = RequestJoinDepositCreateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_deposit(self):
        deposit_id = self.kwargs['deposit_id']
        try:
            return Deposit.objects.get(id=deposit_id)
        except Deposit.DoesNotExist:
            raise AppAPIException(
                {"message": "Deposit not found."},
                status_code=status.HTTP_404_NOT_FOUND
            )

    @request_join_deposit_swagger
    def create(self, request, *args, **kwargs):
        user = request.user
        deposit = self.get_deposit()

        # Check if user already has a pending or approved request for this deposit
        existing_request = RequestJoinDeposit.objects.filter(
            user=user,
            deposit=deposit,
            status__in=[RequestJoinDeposit.StatusChoices.PENDING, RequestJoinDeposit.StatusChoices.APPROVED]
        ).first()

        if existing_request:
            status_text = "pending" if existing_request.status == RequestJoinDeposit.StatusChoices.PENDING else "approved"
            raise AppAPIException(
                {"message": f"You already have  request for this deposit."},
                status_code=status.HTTP_400_BAD_REQUEST
            )

        return super().create(request, *args, **kwargs)

    def perform_create(self, serializer):
        user = self.request.user
        deposit = self.get_deposit()
        serializer.save(user=user, deposit=deposit, status='pending')


class CancelRequestJoinDepositView(generics.GenericAPIView):
    """
    API view to cancel a RequestJoinDeposit by the user who created it
    """
    serializer_class = CancelRequestJoinDepositSerializer
    permission_classes = [permissions.IsAuthenticated]

    @cancel_request_join_deposit_swagger
    def post(self, request, *args, **kwargs):
        """Cancel the RequestJoinDeposit"""
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'status': 'error',
                'message': 'Validation failed',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        request_id = serializer.validated_data['request_id']
        user = request.user

        # Check if request exists and belongs to the user
        try:
            request_obj = RequestJoinDeposit.objects.get(
                id=request_id,
            )
        except RequestJoinDeposit.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Request not found or you don\'t have permission to cancel it.'
            }, status=status.HTTP_404_NOT_FOUND)


        # Cancel the request
        request_obj.cancel()

        return Response({
            'status': 'success',
            'message': 'Request cancelled successfully'
        }, status=status.HTTP_200_OK)



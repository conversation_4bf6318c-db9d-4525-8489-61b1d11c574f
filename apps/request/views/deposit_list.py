from rest_framework.generics import ListAPIView, UpdateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import status, filters
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.db.models import Case, When, Value, IntegerField

from apps.request.models import RequestJoinDeposit
from apps.deposit.models import DepositMembership, Deposit
from apps.request.serializers import RequestJoinDepositSerializer, UpdateRequestStatusSerializer
from apps.request.doc import deposit_join_requests_list_swagger



class RequestJoinDepositListView(ListAPIView):
    serializer_class = RequestJoinDepositSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status']
    ordering_fields = ['created_at']

    @deposit_join_requests_list_swagger
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        deposit_id = self.kwargs['deposit_id']
        queryset = RequestJoinDeposit.objects.filter(deposit_id=deposit_id)
        
        # Custom ordering by status priority using ORM annotations
        ordering = self.request.query_params.get('ordering', None)
        if ordering == 'status_priority' or ordering is None:
            # Annotate queryset with a status_priority field
            # PENDING=0, REJECTED=1, APPROVED=2, CANCELLED=3
            queryset = queryset.annotate(
                status_priority=Case(
                    When(status=RequestJoinDeposit.StatusChoices.PENDING, then=Value(0)),
                    When(status=RequestJoinDeposit.StatusChoices.REJECTED, then=Value(1)),
                    When(status=RequestJoinDeposit.StatusChoices.APPROVED, then=Value(2)),
                    When(status=RequestJoinDeposit.StatusChoices.CANCELLED, then=Value(3)),
                    default=Value(4),
                    output_field=IntegerField()
                )
            ).order_by('status_priority')
            
        return queryset
    

class UpdateRequestStatusView(UpdateAPIView):
    queryset = RequestJoinDeposit.objects.all()
    serializer_class = UpdateRequestStatusSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'  

    @swagger_auto_schema(auto_schema=None)
    def put(self, request, *args, **kwargs):
        return Response(
            {"detail": "Method 'PUT' not allowed."},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )
        
    def perform_update(self, serializer):
        instance = serializer.save()
        if instance.status == RequestJoinDeposit.StatusChoices.APPROVED:
            instance.approve()  
        elif instance.status == RequestJoinDeposit.StatusChoices.REJECTED:
            instance.reject()  
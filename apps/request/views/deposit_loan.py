from rest_framework.generics import ListAP<PERSON><PERSON>iew, UpdateAPIView, CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import status, filters
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.db.models import Case, When, Value, IntegerField

from apps.request.models import LoanRequest
from apps.deposit.models import Deposit
from apps.request.serializers import (
    LoanRequestSerializer, 
    UpdateLoanRequestStatusSerializer,
    CreateLoanRequestSerializer
)


class LoanRequestListView(ListAPIView):
    serializer_class = LoanRequestSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status']
    ordering_fields = ['created_at']

    @swagger_auto_schema(
        operation_description="Get list of loan requests for a deposit",
        manual_parameters=[
            openapi.Parameter(
                'status', 
                openapi.IN_QUERY, 
                description="Filter by status (pending, approved, rejected)", 
                type=openapi.TYPE_STRING,
                enum=[LoanRequest.LoanStatus.PENDING, LoanRequest.LoanStatus.APPROVED, LoanRequest.LoanStatus.REJECTED]
            ),
            openapi.Parameter(
                'ordering', 
                openapi.IN_QUERY, 
                description="Order by field. Prefix with '-' for descending order. e.g. '-created_at' or use 'status_priority' for status-based ordering", 
                type=openapi.TYPE_STRING
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        deposit_id = self.kwargs['deposit_id']
        queryset = LoanRequest.objects.filter(deposit_id=deposit_id)
        
        # Get ordering parameter from query params
        ordering = self.request.query_params.get('ordering', None)
        
        if ordering == 'status_priority':
            # Order by status priority if explicitly requested
            queryset = queryset.annotate(
                status_priority=Case(
                    When(status=LoanRequest.LoanStatus.PENDING, then=Value(0)),
                    When(status=LoanRequest.LoanStatus.REJECTED, then=Value(1)),
                    When(status=LoanRequest.LoanStatus.APPROVED, then=Value(2)),
                    default=Value(3),
                    output_field=IntegerField()
                )
            ).order_by('status_priority')
        elif ordering is None:
            # Default ordering by newest to oldest if no ordering specified
            queryset = queryset.order_by('-created_at')
            
        return queryset


class CreateLoanRequestView(CreateAPIView):
    serializer_class = CreateLoanRequestSerializer
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Create a new loan request",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['deposit', 'amount', 'installment_count'],
            properties={
                'deposit': openapi.Schema(type=openapi.TYPE_INTEGER, description='Deposit ID'),
                'amount': openapi.Schema(type=openapi.TYPE_NUMBER, description='Loan amount'),
                'installment_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='Number of installments'),
                'installment_date': openapi.Schema(type=openapi.TYPE_INTEGER, description='Day of month for installments (1-31). Default is the payment cycle of the deposit'),
                'description': openapi.Schema(type=openapi.TYPE_STRING, description='Description of the loan request'),
            }
        )
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)
    
    def perform_create(self, serializer):
        # Set the user to the current logged-in user
        serializer.save(user=self.request.user)


class UpdateLoanRequestStatusView(UpdateAPIView):
    queryset = LoanRequest.objects.all()
    serializer_class = UpdateLoanRequestStatusSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'  

    @swagger_auto_schema(auto_schema=None)
    def put(self, request, *args, **kwargs):
        return Response(
            {"detail": "Method 'PUT' not allowed."},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )
    
    @swagger_auto_schema(
        operation_description="Update the status of a loan request (approve or reject)",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['status'],
            properties={
                'status': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='New status (approved or rejected)',
                    enum=[LoanRequest.LoanStatus.APPROVED, LoanRequest.LoanStatus.REJECTED]
                ),
                'rejection_reason': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='Reason for rejection (required if status is rejected)'
                ),
            }
        )
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)
        
    def perform_update(self, serializer):
        instance = serializer.save()
        if instance.status == LoanRequest.LoanStatus.APPROVED:
            instance.approve()  
        elif instance.status == LoanRequest.LoanStatus.REJECTED:
            instance.reject(reason=instance.rejection_reason)


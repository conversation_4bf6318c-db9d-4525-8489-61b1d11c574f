from django.db import models
from apps.deposit.models import Deposit, DepositMembership
from apps.account.models import User
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator



class RequestCreateDeposit(models.Model):
    class StatusChoices(models.TextChoices):
        PENDING = 'pending', 'Pending'
        APPROVED = 'approved', 'Approved'
        REJECTED = 'rejected', 'Rejected'
        
    deposit = models.ForeignKey(Deposit, on_delete=models.CASCADE, related_name='create_requests', verbose_name="Deposit", help_text="The deposit associated with this request.")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='request_create_deposits', verbose_name="User ")        
    
    status = models.CharField(max_length=20, choices=StatusChoices.choices, verbose_name="Status", help_text="The current status of the request.")
    rejection_reason = models.TextField(
        verbose_name=_("Rejection Reason"), 
        help_text=_("The reason for rejecting the request."), 
        null=True, 
        blank=True
    )        
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At", help_text="The date and time when the request was created.")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At", help_text="The date and time when the request was last updated.")

    class Meta:
        verbose_name = "Request Create Deposit"
        verbose_name_plural = "Request Create Deposits"

    def __str__(self):
        return f"Request {self.id} by {self.user.fullname} - {self.status}"
    
    def approve(self):
        from apps.account.services import NotificationService
        
        self.status = self.StatusChoices.APPROVED
        self.save()
        self.deposit.is_active = True
        membership, created = DepositMembership.objects.get_or_create(
            user=self.user,
            deposit=self.deposit,
            defaults={
                'role': DepositMembership.Role.OWNER,
                'is_active': True,
                "requested_unit_count": 1,
                "monthly_installment_amount": self.deposit.unit_amount,
            }
        )
        self.deposit.save()
        
        # Send notification to the user
        NotificationService.send_deposit_request_approved(
            user_id=self.user.id,
            deposit_id=self.deposit.id
        )


    def reject(self):
        from apps.account.services import NotificationService

        self.status = self.StatusChoices.REJECTED
        self.save()

        # Send notification to the user
        NotificationService.send_deposit_request_rejected(
            user_id=self.user.id,
            deposit_id=self.deposit.id
        )
                
    
class RequestJoinDeposit(models.Model):
    class StatusChoices(models.TextChoices):
        PENDING = 'pending', 'Pending'
        APPROVED = 'approved', 'Approved'
        REJECTED = 'rejected', 'Rejected'
        CANCELLED = 'cancelled', 'Cancelled'
    
    deposit = models.ForeignKey(Deposit, on_delete=models.CASCADE, related_name='join_requests', verbose_name="Deposit", help_text="The deposit associated with this request.")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='request_join_deposits', verbose_name="User ")                
    status = models.CharField(max_length=20, choices=StatusChoices.choices, verbose_name="Status", help_text="The current status of the request.")
    rejection_reason = models.TextField(
        verbose_name=_("Rejection Reason"), 
        help_text=_("The reason for rejecting the request."), 
        null=True, 
        blank=True
    )    
    requested_unit_count = models.IntegerField(
        verbose_name=_("Requested Unit Count"), 
        help_text=_("The number of unit shares requested by the user.")
    )        
    monthly_installment_amount = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        verbose_name=_("Monthly Installment Amount"), 
        help_text=_("The monthly installment amount for the user.")
    )
        
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At", help_text="The date and time when the request was created.")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At", help_text="The date and time when the request was last updated.")


    def approve(self):
        from apps.account.services import NotificationService
        
        # Check if the user is already a member of this deposit
        existing_membership = DepositMembership.objects.filter(
            user=self.user,
            deposit=self.deposit
        ).first()

        if existing_membership:
            # Update the existing membership instead of creating a new one
            existing_membership.requested_unit_count = self.requested_unit_count
            existing_membership.monthly_installment_amount = self.monthly_installment_amount
            existing_membership.is_active = True
            existing_membership.save()
        else:
            # Create a new DepositMembership record if user is not already a member
            DepositMembership.objects.create(
                user=self.user,
                deposit=self.deposit,
                role=DepositMembership.Role.MEMBER,
                requested_unit_count=self.requested_unit_count,
                monthly_installment_amount=self.monthly_installment_amount,
                is_active=True
            )

        # Update the status of the request to approved
        self.status = self.StatusChoices.APPROVED
        self.save()
        
        # Send notification to the user
        NotificationService.send_deposit_request_approved(
            user_id=self.user.id,
            deposit_id=self.deposit.id
        )

    def reject(self):
        from apps.account.services import NotificationService

        self.status = self.StatusChoices.REJECTED
        self.save()

        # Send notification to the user
        NotificationService.send_deposit_request_rejected(
            user_id=self.user.id,
            deposit_id=self.deposit.id
        )

    def cancel(self):
        """
        Cancel the join deposit request by the user
        """
        self.status = self.StatusChoices.CANCELLED
        self.save()

    

        
class LoanRequest(models.Model):
    class LoanStatus(models.TextChoices):
        PENDING = 'pending', _('Pending')
        APPROVED = 'approved', _('Approved')
        REJECTED = 'rejected', _('Rejected')

    deposit = models.ForeignKey(Deposit, on_delete=models.CASCADE, related_name='loan_requests', verbose_name="Deposit", help_text="The deposit associated with this request.")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='loan_requests', verbose_name="User ")                
    status = models.CharField(max_length=20, choices=LoanStatus.choices, verbose_name="Status", help_text="The current status of the request.")
    rejection_reason = models.TextField(
        verbose_name=_("Rejection Reason"), 
        help_text=_("The reason for rejecting the request."), 
        null=True, 
        blank=True
    )   
    amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name=_("Amount"), help_text=_("The amount of the loan request."))
    installment_count = models.PositiveIntegerField(
        verbose_name=_('Installment Count')
    )
    installment_date = models.IntegerField(
        verbose_name=_('Installment Day of Month'),
        help_text=_('The day of month (1-31) when installments are due.'),
        validators=[MinValueValidator(1), MaxValueValidator(31)]
    )
    description = models.TextField(
        verbose_name=_('Description'),
        blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At", help_text="The date and time when the request was created.")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At", help_text="The date and time when the request was last updated.")

    def __str__(self):
        return f"{self.user.fullname} - {self.amount}"

    def approve(self):
        """
        Approve the loan request and create a new Loan with installments.
        """
        from apps.loan.models import Loan
        from apps.account.services import NotificationService
        
        # Update the status to approved
        self.status = self.LoanStatus.APPROVED
        self.save()
        
        # Get deposit membership for this user
        deposit_membership = DepositMembership.objects.filter(
            user=self.user,
            deposit=self.deposit,
            is_active=True
        ).first()
        
        if not deposit_membership:
            raise ValueError("No active deposit membership found for this user and deposit")

        # Create a new loan
        loan = Loan.objects.create(
            deposit=self.deposit,
            deposit_membership=deposit_membership,
            amount=self.amount,
            installment_count=self.installment_count,
            installment_date=self.installment_date,
            description=self.description,
            status=Loan.LoanStatus.ACTIVE
        )
        
        # Create installments for the loan
        loan.create_installments()
        
        # Send notification to the user
        NotificationService.send_loan_request_approved(
            user_id=self.user.id,
            deposit_id=self.deposit.id
        )
        
        return loan
        
    def reject(self, reason=None):
        """
        Reject the loan request.
        """
        from apps.account.services import NotificationService
        
        self.status = self.LoanStatus.REJECTED
        if reason:
            self.rejection_reason = reason
        self.save()
        
        # Send notification to the user
        NotificationService.send_loan_request_rejected(
            user_id=self.user.id,
            deposit_id=self.deposit.id
        )
        
        
class WithdrawalRequest(models.Model):
    class RequestStatus(models.TextChoices):
        PENDING = 'pending', _('Pending')
        APPROVED = 'approved', _('Approved')
        REJECTED = 'rejected', _('Rejected')

    deposit = models.ForeignKey(
        Deposit,
        on_delete=models.CASCADE,
        related_name='withdrawal_requests',
        verbose_name=_("Deposit"),
        help_text=_("The deposit associated with this withdrawal request.")
    )
    deposit_membership = models.ForeignKey(
        DepositMembership,
        on_delete=models.CASCADE,
        related_name='withdrawal_requests',
        verbose_name=_("Deposit Membership"),
        help_text=_("The membership that requested the withdrawal.")
    )
    account_holder_name = models.CharField(
        max_length=255,
        verbose_name=_("Account Holder Name"),
        help_text=_("Full name of the account holder.")
    )
    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Withdrawal Amount"),
        help_text=_("The amount to be withdrawn.")
    )
    iban = models.CharField(
        max_length=26,
        verbose_name=_("IBAN"),
        help_text=_("26-digit IBAN number starting with IR.")
    )
    reason = models.TextField(
        verbose_name=_("Withdrawal Reason"),
        help_text=_("Reason for the withdrawal request."),
        blank=True
    )
    status = models.CharField(
        max_length=20,
        choices=RequestStatus.choices,
        default=RequestStatus.PENDING,
        verbose_name=_("Status"),
        help_text=_("The current status of the withdrawal request.")
    )
    rejection_reason = models.TextField(
        verbose_name=_("Rejection Reason"),
        help_text=_("The reason for rejecting the request."),
        null=True,
        blank=True
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created At"),
        help_text=_("The date and time when the request was created.")
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Updated At"),
        help_text=_("The date and time when the request was last updated.")
    )

    class Meta:
        verbose_name = _("Withdrawal Request")
        verbose_name_plural = _("Withdrawal Requests")
        ordering = ['-created_at']

    def __str__(self):
        return f"Withdrawal Request {self.id} - {self.account_holder_name} - {self.amount}"

    def clean(self):
        from django.core.exceptions import ValidationError

        # Validate IBAN format
        if not self.iban.startswith('IR') or len(self.iban) != 26:
            raise ValidationError({'iban': _("IBAN must be 26 characters long and start with 'IR'.")})

    def save(self, *args, **kwargs):
        # Check if this is a new withdrawal request (not an update)
        is_new = self.pk is None
        
        # Clean and save the withdrawal request
        self.clean()
        super().save(*args, **kwargs)
        
        # Only send notification for new withdrawal requests
        if is_new:
            try:
                from apps.account.services import NotificationService
                
                # Send notification to the user who created the withdrawal request
                NotificationService.send_withdrawal_request_created_notification(
                    user_id=self.deposit_membership.user_id,
                    deposit_id=self.deposit.id
                )
            except Exception as e:
                # Log the error but don't prevent the withdrawal request from being saved
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error sending withdrawal request creation notification: {str(e)}")

    def approve(self):
        """
        Approve the withdrawal request.
        """
        from apps.account.services import NotificationService
        
        self.status = self.RequestStatus.APPROVED
        self.save()
        
        # Send notification to the deposit owner
        NotificationService.send_withdrawal_approved_notification_to_owner(
            deposit_id=self.deposit.id
        )

    def reject(self, reason=None):
        """
        Reject the withdrawal request.
        """
        self.status = self.RequestStatus.REJECTED
        if reason:
            self.rejection_reason = reason
        self.save()
        


from django.test import TestCase
from django.contrib.auth import get_user_model
from datetime import datetime

from apps.deposit.models import Deposit, DepositMembership, DepositDueDate
from apps.request.models import RequestCreateDeposit
from apps.region.models import Region, UserRegion

User = get_user_model()

class RequestCreateDepositTestCase(TestCase):
    """Test case for RequestCreateDeposit model"""
    
    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            phone_number='+989123456789',
            fullname='Test User',
            password='testpassword123'
        )
        
        # Create a test region
        self.region = Region.objects.create(
            name='Test Region',
            description='Test Region Description'
        )
        
        # Associate user with region
        self.user_region = UserRegion.objects.create(
            user=self.user,
            region=self.region,
            is_active=True
        )
        
        # Create a test deposit (Poll type)
        self.deposit = Deposit.objects.create(
            deposit_type=Deposit.DepositType.POLL,
            owner=self.user,
            region=self.region,
            title='Test Poll Deposit',
            description='Test Poll Deposit Description',
            total_debt_amount=10000000.00,
            lottery_month_count=10,
            unit_amount=1000000.00,
            payment_cycle=5,
            max_unit_per_request=5,
            max_members_count=20,
            initial_lottery_date=datetime.now().date(),
            is_active=False
        )
        
        # Create a deposit request
        self.request = RequestCreateDeposit.objects.create(
            deposit=self.deposit,
            user=self.user,
            status=RequestCreateDeposit.StatusChoices.PENDING
        )
    
    def test_request_approve(self):
        """Test approving a deposit creation request"""
        # Verify initial state
        self.assertEqual(self.request.status, RequestCreateDeposit.StatusChoices.PENDING)
        self.assertFalse(self.deposit.is_active)
        
        # Check that no membership exists yet
        membership_exists = DepositMembership.objects.filter(
            deposit=self.deposit, 
            user=self.user
        ).exists()
        self.assertFalse(membership_exists)
        
        # Approve the request
        self.request.approve()
        
        # Refresh the deposit from the database
        self.deposit.refresh_from_db()
        
        # Check that the request status is updated
        self.assertEqual(self.request.status, RequestCreateDeposit.StatusChoices.APPROVED)
        
        # Check that the deposit is now active
        self.assertTrue(self.deposit.is_active)
        
        # Check that a membership was created
        membership = DepositMembership.objects.get(deposit=self.deposit, user=self.user)
        self.assertEqual(membership.role, DepositMembership.Role.OWNER)
        self.assertTrue(membership.is_active)
    
    def test_request_reject(self):
        """Test rejecting a deposit creation request"""
        # Verify initial state
        self.assertEqual(self.request.status, RequestCreateDeposit.StatusChoices.PENDING)
        self.assertFalse(self.deposit.is_active)
        
        # Reject the request
        rejection_reason = "Test rejection reason"
        self.request.rejection_reason = rejection_reason
        self.request.reject()
        
        # Refresh the deposit from the database
        self.deposit.refresh_from_db()
        
        # Check that the request status is updated
        self.assertEqual(self.request.status, RequestCreateDeposit.StatusChoices.REJECTED)
        
        # Check that the deposit is still inactive
        self.assertFalse(self.deposit.is_active)
        
        # Check that no membership was created
        membership_exists = DepositMembership.objects.filter(
            deposit=self.deposit, 
            user=self.user
        ).exists()
        self.assertFalse(membership_exists)
    
    def test_due_dates_creation(self):
        """Test that due dates are created for the deposit"""
        # Check that due dates were created
        due_dates = DepositDueDate.objects.filter(deposit=self.deposit)
        self.assertEqual(due_dates.count(), self.deposit.lottery_month_count)
        
        # Check the first due date
        first_due_date = due_dates.order_by('due_date_number').first()
        self.assertEqual(first_due_date.due_date_number, 1)
        self.assertFalse(first_due_date.is_completed)
        
    def test_request_creation(self):
        """Test creating a deposit request"""
        # Create a new user
        new_user = User.objects.create_user(
            email='<EMAIL>',
            phone_number='+989123456788',
            fullname='New Test User',
            password='newpassword123'
        )
        
        # Associate new user with region
        UserRegion.objects.create(
            user=new_user,
            region=self.region,
            is_active=True
        )
        
        # Create a new deposit request
        new_request = RequestCreateDeposit.objects.create(
            deposit=self.deposit,
            user=new_user,
            status=RequestCreateDeposit.StatusChoices.PENDING
        )
        
        # Verify the request was created correctly
        self.assertEqual(new_request.deposit, self.deposit)
        self.assertEqual(new_request.user, new_user)
        self.assertEqual(new_request.status, RequestCreateDeposit.StatusChoices.PENDING)
        self.assertIsNone(new_request.rejection_reason)
        
        # Verify the request is retrievable from the database
        retrieved_request = RequestCreateDeposit.objects.get(id=new_request.id)
        self.assertEqual(retrieved_request, new_request)
    
    def test_due_dates_ordering(self):
        """Test that due dates are properly ordered"""
        # Get all due dates for the deposit
        due_dates = DepositDueDate.objects.filter(deposit=self.deposit).order_by('due_date_number')
        
        # Check that there are exactly 10 due dates (as specified in lottery_month_count)
        self.assertEqual(due_dates.count(), 10)
        
        # Check that due date numbers are sequential
        for i, due_date in enumerate(due_dates, start=1):
            self.assertEqual(due_date.due_date_number, i)
            
        # Check that all due dates are initially not completed
        for due_date in due_dates:
            self.assertFalse(due_date.is_completed)
            
        # Check that due dates are ordered chronologically
        previous_date = None
        for due_date in due_dates:
            if previous_date:
                self.assertGreaterEqual(due_date.due_date, previous_date)
            previous_date = due_date.due_date
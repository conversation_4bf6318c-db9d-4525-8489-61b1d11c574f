from django.contrib import admin
from django.utils.translation import gettext_lazy as _

# Import the models
from apps.request.models import RequestCreateDeposit, RequestJoinDeposit, LoanRequest, WithdrawalRequest

# Import our Unfold admin classes
from apps.request.admin.deposit_request import (
    RequestCreateDepositAdmin,
    RequestJoinDepositAdmin,
    LoanRequestAdmin,
    WithdrawalRequestAdmin
)

# The actual registration is done in the deposit_request.py file
# This file is kept for backward compatibility and to maintain the project structure


from rest_framework import serializers

from apps.request.models import RequestJoinDeposit
from apps.deposit.models import Deposit, DepositMembership


class RequestJoinDepositSerializer(serializers.ModelSerializer):
    fullname = serializers.CharField(source="user.fullname")
    deposit = serializers.CharField(source="deposit.title")
    
    class Meta:
        model = RequestJoinDeposit
        fields = ['id', 'fullname', 'status', 'rejection_reason', 'requested_unit_count', 'deposit', 'created_at']
        read_only_fields = ['id', 'created_at']


class UpdateRequestStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = RequestJoinDeposit
        fields = ['id', 'status', 'rejection_reason']  
        read_only_fields = ['id', ]

    def validate_status(self, value):
        if value not in [RequestJoinDeposit.StatusChoices.APPROVED, RequestJoinDeposit.StatusChoices.REJECTED]:
            raise serializers.ValidationError("Status must be either 'approved' or 'rejected'.")
        return value

    def validate(self, data):
        if data.get('status') == RequestJoinDeposit.StatusChoices.REJECTED and not data.get('rejection_reason'):
            raise serializers.ValidationError("Rejection reason is required when status is 'rejected'.")
        return data
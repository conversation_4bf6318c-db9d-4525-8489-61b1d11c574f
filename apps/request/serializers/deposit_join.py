from rest_framework import serializers
from apps.request.models import RequestJoinDeposit
from apps.deposit.models import Deposit, DepositMembership


class RequestJoinDepositCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = RequestJoinDeposit
        fields = ['requested_unit_count', 'monthly_installment_amount', ]
        read_only_fields = ['monthly_installment_amount']

    def validate(self, data):
        user = self.context['request'].user
        deposit = self.context['view'].get_deposit()

        # چک کردن اینکه کاربر قبلاً برای این صندوق درخواست نداده باشد
        if DepositMembership.objects.filter(user=user, deposit=deposit).exists():
            raise serializers.ValidationError({"requested_unit_count": "You have already joined this deposit."})

        # بررسی حداکثر سهم درخواستی
        if data['requested_unit_count'] > deposit.max_unit_per_request:
            raise serializers.ValidationError({
                'requested_unit_count': f"Maximum {deposit.max_unit_per_request} units allowed per request."
            })

        # بررسی مثبت بودن تعداد سهم درخواستی
        if data['requested_unit_count'] <= 0:
            raise serializers.ValidationError({
                'requested_unit_count': "Requested unit count must be greater than zero."
            })

        # چک کردن ظرفیت صندوق
        # if not deposit.has_capacity(data['requested_unit_count']):
            # raise serializers.ValidationError({'requested_unit_count': "The deposit does not have enough capacity."})
        if deposit.max_members_count and deposit.members.filter(is_active=True).count() >= deposit.max_members_count:
                raise serializers.ValidationError({'requested_unit_count': "The deposit has reached its maximum number of members."})
                # raise serializers.ValidationError({'requested_unit_count': "The deposit does not have enough unit capacity."})

        # محاسبه مبلغ قسط ماهانه
        data['monthly_installment_amount'] = deposit.unit_amount * data['requested_unit_count']

        return data


class CancelRequestJoinDepositSerializer(serializers.Serializer):
    """Serializer for cancelling a RequestJoinDeposit"""
    request_id = serializers.IntegerField(
        help_text="ID of the RequestJoinDeposit to cancel"
    )

    def validate_request_id(self, value):
        """Validate that the request_id is a positive integer"""
        if value <= 0:
            raise serializers.ValidationError("Request ID must be a positive integer.")
        return value
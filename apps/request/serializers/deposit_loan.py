from rest_framework import serializers

from apps.request.models import LoanRequest
from apps.deposit.models import Deposit
from utils import File<PERSON>ieldSerializer, absolute_url


class LoanRequestSerializer(serializers.ModelSerializer):
    fullname = serializers.CharField(source="user.fullname", read_only=True)
    deposit_title = serializers.CharField(source="deposit.title", read_only=True)
    avatar = FileFieldSerializer(required=False, source="user.avatar")
   
    class Meta:
        model = LoanRequest
        fields = [
            'id', 'deposit', 'deposit_title', 'fullname', 'amount', 
            'installment_count', 'installment_date', 'description', 
            'status', 'rejection_reason', 'created_at', 'avatar'
        ]
        read_only_fields = ['id', 'created_at', 'status']

class CreateLoanRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = LoanRequest
        fields = ['deposit', 'amount', 'installment_count', 'installment_date', 'description']
        
    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError({
                'amount': 'Loan amount must be greater than zero.'
            })
        return value
        
    def validate_installment_count(self, value):
        if value <= 0:
            raise serializers.ValidationError({
                'installment_count': 'Installment count must be greater than zero.'
            })
        return value
    
    def create(self, validated_data):
        # Set status to PENDING by default
        validated_data['status'] = LoanRequest.LoanStatus.PENDING
        
        # Get payment_cycle from deposit to set default installment_date if not provided
        deposit = validated_data.get('deposit')
        if 'installment_date' not in validated_data or validated_data['installment_date'] is None:
            # Set installment_date to payment_cycle of the deposit
            validated_data['installment_date'] = deposit.payment_cycle
        
        return super().create(validated_data)


class UpdateLoanRequestStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = LoanRequest
        fields = ['id', 'status', 'rejection_reason']  
        read_only_fields = ['id']

    def validate_status(self, value):
        if value not in [LoanRequest.LoanStatus.APPROVED, LoanRequest.LoanStatus.REJECTED]:
            raise serializers.ValidationError({"status": "Status must be either 'approved' or 'rejected'."})
        return value

    def validate(self, data):
        if data.get('status') == LoanRequest.LoanStatus.REJECTED and not data.get('rejection_reason'):
            raise serializers.ValidationError({"rejection_reason": "Rejection reason is required when status is 'rejected'."})
        return data 
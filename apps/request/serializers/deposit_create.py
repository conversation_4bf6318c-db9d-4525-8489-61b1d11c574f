from rest_framework import serializers
from apps.deposit.models import Deposit
from django.utils import timezone

class RulesSerializer(serializers.Serializer):
    subject = serializers.CharField(max_length=255)
    description = serializers.CharField()
    
class BaseDepositSerializer(serializers.ModelSerializer):
    rules = serializers.ListField(
        child=RulesSerializer(),
        required=False,
        default=list,
        help_text="List of rules as dictionaries. Example: [{'subject': '...', 'description': '...'}]"
    )
    
    class Meta:
        model = Deposit
        fields = [
            'id', 'deposit_type', 'title', 'description', 'total_debt_amount',
            'unit_amount', 'payment_cycle', 'max_unit_per_request', 'max_members_count',
            'rules', 'is_active', 'created', 'updated',
        ]
        read_only_fields = ['id', 'created', 'updated', 'is_active', 'deposit_type']          
    
    def validate_rules(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError("Rules must be a list of dictionaries.")
        for rule in value:
            if not isinstance(rule, dict):
                raise serializers.ValidationError("Rules must be a list of dictionaries.")
            if 'subject' not in rule or 'description' not in rule:
                raise serializers.ValidationError("Rules must be a list of dictionaries.")
        return value

class CreatePollDepositSerializer(BaseDepositSerializer):
    class Meta(BaseDepositSerializer.Meta):
        # Exclude total_debt_amount from fields
        base_fields = list(BaseDepositSerializer.Meta.fields)
        # base_fields.remove('total_debt_amount')
        fields = base_fields + [
            'lottery_month_count', 'lottery_per_month_count', 'initial_lottery_date'
        ]

class CreateSavingDepositSerializer(BaseDepositSerializer):
    start_date = serializers.DateField(required=False)
    
    class Meta(BaseDepositSerializer.Meta):
        fields = BaseDepositSerializer.Meta.fields + [
            'validity_duration', 'start_date'
        ]
        
    def validate(self, data):
        """
        Check that start_date is provided or set it to today's date.
        """
        if 'start_date' not in data or data['start_date'] is None:
            data['start_date'] = timezone.now().date()
        return data

class CreateReportingDepositSerializer(BaseDepositSerializer):
    class Meta(BaseDepositSerializer.Meta):
        fields = BaseDepositSerializer.Meta.fields + [
            'validity_duration'
        ]
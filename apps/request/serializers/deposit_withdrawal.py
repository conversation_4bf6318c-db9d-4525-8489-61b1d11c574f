from rest_framework import serializers
from apps.request.models import WithdrawalRequest
from apps.deposit.models import Deposit, DepositMembership


class WithdrawalRequestSerializer(serializers.ModelSerializer):
    deposit_title = serializers.CharField(source="deposit.title", read_only=True)
    account_holder_name = serializers.CharField(required=True)
    
    class Meta:
        model = WithdrawalRequest
        fields = [
            'id', 'deposit_title', 'account_holder_name', 
            'amount', 'iban', 'reason', 'status', 'rejection_reason', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'status']
    
    def validate_iban(self, value):
        if not value.startswith('IR') or len(value) != 26:
            raise serializers.ValidationError("IBAN must be 26 characters long and start with 'IR'.")
        return value
    
    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Withdrawal amount must be greater than zero.")
        return value


class CreateWithdrawalRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = WithdrawalRequest
        fields = ['deposit', 'account_holder_name', 'amount', 'iban', 'reason']
    
    def validate(self, data):
        user = self.context['request'].user
        deposit = data.get('deposit')
        
        # Check if the user is a member of the deposit
        try:
            membership = DepositMembership.objects.get(
                user=user,
                deposit=deposit,
                is_active=True
            )
        except DepositMembership.DoesNotExist:
            raise serializers.ValidationError({"deposit": "You are not an active member of this deposit."})
        
        # Check if the user is an admin or owner
        if membership.role not in [DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER]:
            raise serializers.ValidationError({"deposit": "Only deposit admins or owners can request withdrawals."})
        
        # Add deposit_membership to the validated data
        data['deposit_membership'] = membership
        
        return data
    
    def create(self, validated_data):
        # Set status to PENDING by default
        validated_data['status'] = WithdrawalRequest.RequestStatus.PENDING
        return super().create(validated_data)


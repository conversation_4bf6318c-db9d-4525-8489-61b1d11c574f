# Generated by Django 3.2.4 on 2025-01-26 16:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('deposit', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='RequestJoinDeposit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], help_text='The current status of the request.', max_length=20, verbose_name='Status')),
                ('rejection_reason', models.TextField(blank=True, help_text='The reason for rejecting the request.', null=True, verbose_name='Rejection Reason')),
                ('requested_unit_count', models.IntegerField(help_text='The number of unit shares requested by the user.', verbose_name='Requested Unit Count')),
                ('monthly_installment_amount', models.DecimalField(decimal_places=2, help_text='The monthly installment amount for the user.', max_digits=15, verbose_name='Monthly Installment Amount')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='The date and time when the request was created.', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='The date and time when the request was last updated.', verbose_name='Updated At')),
                ('deposit', models.ForeignKey(help_text='The deposit associated with this request.', on_delete=django.db.models.deletion.CASCADE, related_name='join_requests', to='deposit.deposit', verbose_name='Deposit')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='request_join_deposits', to=settings.AUTH_USER_MODEL, verbose_name='User ')),
            ],
        ),
        migrations.CreateModel(
            name='RequestCreateDeposit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], help_text='The current status of the request.', max_length=20, verbose_name='Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='The date and time when the request was created.', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='The date and time when the request was last updated.', verbose_name='Updated At')),
                ('deposit', models.ForeignKey(help_text='The deposit associated with this request.', on_delete=django.db.models.deletion.CASCADE, related_name='create_requests', to='deposit.deposit', verbose_name='Deposit')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='request_create_deposits', to=settings.AUTH_USER_MODEL, verbose_name='User ')),
            ],
            options={
                'verbose_name': 'Request Create Deposit',
                'verbose_name_plural': 'Request Create Deposits',
            },
        ),
    ]

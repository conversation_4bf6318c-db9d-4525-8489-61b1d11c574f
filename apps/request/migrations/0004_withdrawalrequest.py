# Generated by Django 3.2.4 on 2025-03-18 23:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('deposit', '0008_delete_depositlottery'),
        ('request', '0003_loanrequest'),
    ]

    operations = [
        migrations.CreateModel(
            name='WithdrawalRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_holder_name', models.Char<PERSON>ield(help_text='Full name of the account holder.', max_length=255, verbose_name='Account Holder Name')),
                ('amount', models.DecimalField(decimal_places=2, help_text='The amount to be withdrawn.', max_digits=15, verbose_name='Withdrawal Amount')),
                ('iban', models.Char<PERSON>ield(help_text='26-digit IBAN number starting with IR.', max_length=26, verbose_name='IBAN')),
                ('reason', models.TextField(blank=True, help_text='Reason for the withdrawal request.', verbose_name='Withdrawal Reason')),
                ('status', models.Char<PERSON>ield(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', help_text='The current status of the withdrawal request.', max_length=20, verbose_name='Status')),
                ('rejection_reason', models.TextField(blank=True, help_text='The reason for rejecting the request.', null=True, verbose_name='Rejection Reason')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='The date and time when the request was created.', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='The date and time when the request was last updated.', verbose_name='Updated At')),
                ('deposit', models.ForeignKey(help_text='The deposit associated with this withdrawal request.', on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_requests', to='deposit.deposit', verbose_name='Deposit')),
                ('deposit_membership', models.ForeignKey(help_text='The membership that requested the withdrawal.', on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_requests', to='deposit.depositmembership', verbose_name='Deposit Membership')),
            ],
            options={
                'verbose_name': 'Withdrawal Request',
                'verbose_name_plural': 'Withdrawal Requests',
                'ordering': ['-created_at'],
            },
        ),
    ]

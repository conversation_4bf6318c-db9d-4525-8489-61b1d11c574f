# Generated by Django 3.2.4 on 2025-03-13 22:04

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('deposit', '0008_delete_depositlottery'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('request', '0002_requestcreatedeposit_rejection_reason'),
    ]

    operations = [
        migrations.CreateModel(
            name='LoanRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], help_text='The current status of the request.', max_length=20, verbose_name='Status')),
                ('rejection_reason', models.TextField(blank=True, help_text='The reason for rejecting the request.', null=True, verbose_name='Rejection Reason')),
                ('amount', models.DecimalField(decimal_places=2, help_text='The amount of the loan request.', max_digits=15, verbose_name='Amount')),
                ('installment_count', models.PositiveIntegerField(verbose_name='Installment Count')),
                ('installment_date', models.IntegerField(help_text='The day of month (1-31) when installments are due.', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)], verbose_name='Installment Day of Month')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='The date and time when the request was created.', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='The date and time when the request was last updated.', verbose_name='Updated At')),
                ('deposit', models.ForeignKey(help_text='The deposit associated with this request.', on_delete=django.db.models.deletion.CASCADE, related_name='loan_requests', to='deposit.deposit', verbose_name='Deposit')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loan_requests', to=settings.AUTH_USER_MODEL, verbose_name='User ')),
            ],
        ),
    ]

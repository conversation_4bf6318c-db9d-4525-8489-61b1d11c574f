from django.urls import path
from . import views


urlpatterns = [
    # create deposit reqeust
    path('deposit/create/poll-deposit/', views.CreatePollDepositView.as_view(), name='request-deposit-create-poll'),
    path('deposit/create/saving-deposit/', views.CreateSavingDepositView.as_view(), name='request-deposit-create-saving'),
    path('deposit/create/reporting-deposit/', views.CreateReportingDepositView.as_view(), name='request-deposit-create-reporting'),

    # join deposit member
    path('deposit/join/<int:deposit_id>/', views.RequestJoinDepositView.as_view(), name='request-join-deposit'),
    path('deposit/join-request/cancel/', views.CancelRequestJoinDepositView.as_view(), name='cancel-request-join-deposit'),

    # list deposit request
    path('deposit/<int:deposit_id>/poll-list/', views.RequestJoinDepositListView.as_view(), name='request-join-deposit-list'),
    path('deposit/<int:id>/poll-update/', views.UpdateRequestStatusView.as_view(), name='update-request-status'),

    # loan requests
    path('deposit/<int:deposit_id>/loan-list/', views.LoanRequestListView.as_view(), name='loan-request-list'),
    path('deposit/loan-request/', views.CreateLoanRequestView.as_view(), name='create-loan-request'),
    path('deposit/loan-request/<int:id>/update/', views.UpdateLoanRequestStatusView.as_view(), name='update-loan-request-status'),

    # withdrawal requests
    path('deposit/<int:deposit_id>/withdrawal-list/', views.WithdrawalRequestListView.as_view(), name='withdrawal-request-list'),
    path('deposit/withdrawal-request/', views.CreateWithdrawalRequestView.as_view(), name='create-withdrawal-request'),
    path('user/withdrawal-requests/', views.UserWithdrawalRequestListView.as_view(), name='user-withdrawal-requests'),
]


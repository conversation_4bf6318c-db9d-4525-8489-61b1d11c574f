from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status

# ============================================================================
# STRUCTURED SWAGGER DOCUMENTATION FOR REGION APP
# ============================================================================

# Region List Swagger Documentation
region_list_swagger = swagger_auto_schema(
    operation_description="Get list of all available regions with user membership status. Each region includes name, member count, and whether the current user belongs to that region.",
    operation_summary="List All Regions",
    tags=['Region'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of regions retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Region unique identifier',
                            example=1
                        ),
                        'name': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Region name',
                            example='تهران'
                        ),
                        'members_count': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Number of active members in this region',
                            example=150
                        ),
                        'is_current_user_region': openapi.Schema(
                            type=openapi.TYPE_BOOLEAN,
                            description='Whether the current authenticated user belongs to this region',
                            example=True
                        ),
                        'created_at': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            format=openapi.FORMAT_DATETIME,
                            description='Region creation timestamp',
                            example='2023-01-01T10:00:00Z'
                        )
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "id": 1,
                        "name": "تهران",
                        "members_count": 150,
                        "is_current_user_region": True,
                        "created_at": "2023-01-01T10:00:00Z"
                    },
                    {
                        "id": 2,
                        "name": "اصفهان",
                        "members_count": 85,
                        "is_current_user_region": False,
                        "created_at": "2023-01-02T10:00:00Z"
                    }
                ]
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Change User Region Swagger Documentation
change_user_region_swagger = swagger_auto_schema(
    operation_description="Change user's region membership. If user already has a region membership, it will be updated. If not, a new membership will be created.",
    operation_summary="Change User Region",
    tags=['Region'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['region_id'],
        properties={
            'region_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='ID of the region to join or switch to',
                example=2
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="User region changed successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='success'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Region changed successfully'
                    )
                }
            ),
            examples={
                'application/json': {
                    "status": "success",
                    "message": "Region changed successfully"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation errors",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='error'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Validation failed'
                    ),
                    'errors': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'region_id': openapi.Schema(
                                type=openapi.TYPE_ARRAY,
                                items=openapi.Schema(type=openapi.TYPE_STRING),
                                example=['This field is required.']
                            )
                        }
                    )
                }
            ),
            examples={
                'application/json': {
                    "status": "error",
                    "message": "Validation failed",
                    "errors": {
                        "region_id": ["This field is required."]
                    }
                }
            }
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Region not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='error'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Region not found'
                    )
                }
            ),
            examples={
                'application/json': {
                    "status": "error",
                    "message": "Region not found"
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# User Invitation Links List Swagger Documentation
user_invitation_links_list_swagger = swagger_auto_schema(
    operation_description="Get list of all invitation links created by the current user with their usage status. Shows which links have been used and by whom.",
    operation_summary="List User's Invitation Links",
    tags=['Region'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of user's invitation links retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Invitation link unique identifier',
                            example=1
                        ),
                        'invitation_code': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Unique invitation code',
                            example='HAMJEEB-ABC123'
                        ),
                        'invitation_url': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Full invitation URL',
                            example='https://hammjeeb.ir/link/HAMJEEB-ABC123'
                        ),
                        'is_used': openapi.Schema(
                            type=openapi.TYPE_BOOLEAN,
                            description='Whether this invitation link has been used',
                            example=False
                        ),
                        'used_by': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='ID of user who used this invitation (null if not used)',
                            example=None
                        ),
                        'used_by_fullname': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Full name of user who used this invitation',
                            example=None
                        ),
                        'user_fullname': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Full name of user who created this invitation',
                            example='علی احمدی'
                        ),
                        'region_name': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Name of the region this invitation belongs to',
                            example='تهران'
                        ),
                        'created_at': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            format=openapi.FORMAT_DATETIME,
                            description='Invitation creation timestamp',
                            example='2023-01-01T10:00:00Z'
                        ),
                        'used_at': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            format=openapi.FORMAT_DATETIME,
                            description='Invitation usage timestamp (null if not used)',
                            example=None
                        )
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "id": 1,
                        "invitation_code": "HAMJEEB-ABC123",
                        "invitation_url": "https://hammjeeb.ir/link/HAMJEEB-ABC123",
                        "is_used": False,
                        "used_by": None,
                        "used_by_fullname": None,
                        "user_fullname": "علی احمدی",
                        "region_name": "تهران",
                        "created_at": "2023-01-01T10:00:00Z",
                        "used_at": None
                    },
                    {
                        "id": 2,
                        "invitation_code": "HAMJEEB-DEF456",
                        "invitation_url": "https://hammjeeb.ir/link/HAMJEEB-DEF456",
                        "is_used": True,
                        "used_by": 25,
                        "used_by_fullname": "سارا محمدی",
                        "user_fullname": "علی احمدی",
                        "region_name": "تهران",
                        "created_at": "2023-01-02T10:00:00Z",
                        "used_at": "2023-01-03T15:30:00Z"
                    }
                ]
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Create Invitation Link Swagger Documentation
create_invitation_link_swagger = swagger_auto_schema(
    operation_description="Create a new invitation link for the current user's region. The user must be a member of a region to create invitation links. No request body is required.",
    operation_summary="Create New Invitation Link",
    tags=['Region'],
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Invitation link created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='success'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Invitation link created successfully.'
                    ),
                    'invitation_link': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'id': openapi.Schema(
                                type=openapi.TYPE_INTEGER,
                                description='Invitation link unique identifier',
                                example=3
                            ),
                            'invitation_code': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Unique invitation code',
                                example='HAMJEEB-XYZ789'
                            ),
                            'invitation_url': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Full invitation URL',
                                example='https://hammjeeb.ir/link/HAMJEEB-XYZ789'
                            ),
                            'is_used': openapi.Schema(
                                type=openapi.TYPE_BOOLEAN,
                                description='Whether this invitation link has been used',
                                example=False
                            ),
                            'used_by': openapi.Schema(
                                type=openapi.TYPE_INTEGER,
                                description='ID of user who used this invitation (null if not used)',
                                example=None
                            ),
                            'used_by_fullname': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Full name of user who used this invitation',
                                example=None
                            ),
                            'user_fullname': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Full name of user who created this invitation',
                                example='علی احمدی'
                            ),
                            'region_name': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Name of the region this invitation belongs to',
                                example='تهران'
                            ),
                            'created_at': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_DATETIME,
                                description='Invitation creation timestamp',
                                example='2023-01-01T10:00:00Z'
                            ),
                            'used_at': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_DATETIME,
                                description='Invitation usage timestamp (null if not used)',
                                example=None
                            )
                        }
                    )
                }
            ),
            examples={
                'application/json': {
                    "status": "success",
                    "message": "Invitation link created successfully.",
                    "invitation_link": {
                        "id": 3,
                        "invitation_code": "HAMJEEB-XYZ789",
                        "invitation_url": "https://hammjeeb.ir/link/HAMJEEB-XYZ789",
                        "is_used": False,
                        "used_by": None,
                        "used_by_fullname": None,
                        "user_fullname": "علی احمدی",
                        "region_name": "تهران",
                        "created_at": "2023-01-01T10:00:00Z",
                        "used_at": None
                    }
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="User not member of any region",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='error'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='User must be a member of a region to create invitation links.'
                    )
                }
            ),
            examples={
                'application/json': {
                    "status": "error",
                    "message": "User must be a member of a region to create invitation links."
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# User Region Detail Swagger Documentation
user_region_detail_swagger = swagger_auto_schema(
    operation_description="Get current user's region details as an object. Returns the user's region membership information including region details.",
    operation_summary="Get User's Region Details",
    tags=['Region'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="User's region details retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(
                        type=openapi.TYPE_INTEGER,
                        description='Region unique identifier',
                        example=1
                    ),
                    'name': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Region name',
                        example='تهران'
                    ),
                    'members_count': openapi.Schema(
                        type=openapi.TYPE_INTEGER,
                        description='Number of active members in this region',
                        example=150
                    ),
                    'is_current_user_region': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Whether the current authenticated user belongs to this region (always True for this endpoint)',
                        example=True
                    ),
                    'created_at': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        format=openapi.FORMAT_DATETIME,
                        description='Region creation timestamp',
                        example='2023-01-01T10:00:00Z'
                    )
                }
            ),
            examples={
                'application/json': {
                    "id": 1,
                    "name": "تهران",
                    "members_count": 150,
                    "is_current_user_region": True,
                    "created_at": "2023-01-01T10:00:00Z"
                }
            }
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="User is not a member of any region",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='error'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='User is not a member of any region'
                    )
                }
            ),
            examples={
                'application/json': {
                    "status": "error",
                    "message": "User is not a member of any region"
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

def doc_region_list():
    return """
# 🌍 Scenario
دریافت لیست تمام ریجن‌ها

کاربر می‌تواند لیست تمام ریجن‌های موجود را مشاهده کند. برای هر ریجن، اطلاعاتی شامل نام، توضیحات، تعداد اعضا و وضعیت عضویت کاربر فعلی نمایش داده می‌شود.

---

## 🚀 درخواست API

### URL:
```
GET /api/region/list/
```

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Token your_auth_token_here     |

### Parameters:
این endpoint هیچ پارامتری نیاز ندارد.

---

## 📄 نمونه درخواست:

### درخواست ساده:
```bash
curl -X GET "http://localhost:8000/api/region/list/" \
     -H "Authorization: Token your_auth_token_here" \
     -H "Content-Type: application/json"
```

### پاسخ موفق:
```json
[
    {
        "id": 1,
        "name": "تهران",
        "description": "ریجن مربوط به شهر تهران",
        "invitation_code": "REGION-ABC123",
        "members_count": 25,
        "is_current_user_region": true,
        "created_at": "2025-01-01T10:00:00Z"
    },
    {
        "id": 2,
        "name": "اصفهان",
        "description": "ریجن مربوط به شهر اصفهان",
        "invitation_code": "REGION-DEF456",
        "members_count": 18,
        "is_current_user_region": false,
        "created_at": "2025-01-02T11:00:00Z"
    },
    {
        "id": 3,
        "name": "شیراز",
        "description": "ریجن مربوط به شهر شیراز",
        "invitation_code": "REGION-GHI789",
        "members_count": 12,
        "is_current_user_region": false,
        "created_at": "2025-01-03T12:00:00Z"
    }
]
```

### پاسخ خطا - عدم احراز هویت:
```json
{
    "detail": "Authentication credentials were not provided."
}
```

---

## 💡 نکات مهم:
1. **احراز هویت:**
   - کاربر باید توکن احراز هویت معتبر ارائه دهد.
2. **فیلد is_current_user_region:**
   - نشان می‌دهد که آیا کاربر فعلی عضو این ریجن است یا خیر.
3. **مرتب‌سازی:**
   - ریجن‌ها بر اساس نام به صورت الفبایی مرتب می‌شوند.
4. **تعداد اعضا:**
   - فیلد members_count تعداد کل اعضای فعال هر ریجن را نشان می‌دهد.

---

## 🔍 کاربردها:
- نمایش لیست ریجن‌ها در اپلیکیشن
- انتخاب ریجن جدید توسط کاربر
- مشاهده آمار اعضای هر ریجن
- تشخیص ریجن فعلی کاربر
"""


def doc_user_invitation_links_list():
    return """
# 📋 Scenario
لیست لینک‌های دعوت کاربر

کاربر می‌تواند لیست تمام لینک‌های دعوتی که ایجاد کرده است را مشاهده کند. برای هر لینک دعوت، اطلاعاتی شامل کد دعوت، وضعیت استفاده، کاربر استفاده‌کننده و تاریخ ایجاد نمایش داده می‌شود.

---

## 🚀 درخواست API

### URL:
```
GET /api/region/invitation-links/
```

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Token your_auth_token_here     |

### Parameters:
این endpoint هیچ پارامتری نیاز ندارد.

---

## 📄 نمونه درخواست:

### درخواست ساده:
```bash
curl -X GET "http://localhost:8000/api/region/invitation-links/" \
     -H "Authorization: Token your_auth_token_here" \
     -H "Content-Type: application/json"
```

### پاسخ موفق:
```json
[
    {
        "id": 1,
        "invitation_code": "HAMJEEB-ABC123",
        "invitation_url": "https://hammjeeb.ir/link/HAMJEEB-ABC123",
        "is_used": false,
        "used_by": null,
        "used_by_fullname": null,
        "user_fullname": "علی احمدی",
        "region_name": "تهران",
        "created_at": "2023-01-01T10:00:00Z",
        "used_at": null
    },
    {
        "id": 2,
        "invitation_code": "HAMJEEB-DEF456",
        "invitation_url": "https://hammjeeb.ir/link/HAMJEEB-DEF456",
        "is_used": true,
        "used_by": 25,
        "used_by_fullname": "سارا محمدی",
        "user_fullname": "علی احمدی",
        "region_name": "تهران",
        "created_at": "2023-01-02T10:00:00Z",
        "used_at": "2023-01-03T15:30:00Z"
    }
]
```

### پاسخ خطا - عدم احراز هویت:
```json
{
    "detail": "Authentication credentials were not provided."
}
```

---

## 💡 نکات مهم:
1. **احراز هویت:**
   - کاربر باید توکن احراز هویت معتبر ارائه دهد.
2. **فیلد is_used:**
   - نشان می‌دهد که آیا این لینک دعوت قبلاً استفاده شده است یا خیر.
3. **فیلد used_by:**
   - شناسه کاربری که از این لینک دعوت استفاده کرده است (اگر استفاده شده باشد).
4. **مرتب‌سازی:**
   - لینک‌های دعوت بر اساس تاریخ ایجاد به صورت نزولی مرتب می‌شوند (جدیدترین اول).

---

## 🔍 کاربردها:
- نمایش لیست لینک‌های دعوت کاربر در اپلیکیشن
- پیگیری وضعیت استفاده از لینک‌های دعوت
- مشاهده کاربرانی که با لینک دعوت ثبت‌نام کرده‌اند
"""


def doc_create_invitation_link():
    return """
# ➕ Scenario
ایجاد لینک دعوت جدید

کاربر می‌تواند یک لینک دعوت جدید برای ریجن خود ایجاد کند. این لینک دعوت می‌تواند برای دعوت کاربران دیگر به ریجن استفاده شود.

---

## 🚀 درخواست API

### URL:
```
POST /api/region/create-invitation-link/
```

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Token your_auth_token_here     |

### Body:
این endpoint نیازی به body ندارد.

---

## 📄 نمونه درخواست:

### درخواست ساده:
```bash
curl -X POST "http://localhost:8000/api/region/create-invitation-link/" \
     -H "Authorization: Token your_auth_token_here" \
     -H "Content-Type: application/json"
```

### پاسخ موفق:
```json
{
    "status": "success",
    "message": "Invitation link created successfully.",
    "invitation_link": {
        "id": 3,
        "invitation_code": "HAMJEEB-XYZ789",
        "invitation_url": "https://hammjeeb.ir/link/HAMJEEB-XYZ789",
        "is_used": false,
        "used_by": null,
        "used_by_fullname": null,
        "user_fullname": "علی احمدی",
        "region_name": "تهران",
        "created_at": "2023-01-01T10:00:00Z",
        "used_at": null
    }
}
```

### پاسخ خطا - کاربر عضو هیچ ریجنی نیست:
```json
{
    "status": "error",
    "message": "User must be a member of a region to create invitation links."
}
```

### پاسخ خطا - عدم احراز هویت:
```json
{
    "detail": "Authentication credentials were not provided."
}
```

---

## 💡 نکات مهم:
1. **احراز هویت:**
   - کاربر باید توکن احراز هویت معتبر ارائه دهد.
2. **عضویت در ریجن:**
   - کاربر باید عضو یک ریجن باشد تا بتواند لینک دعوت ایجاد کند.
3. **کد دعوت:**
   - کد دعوت به صورت خودکار با فرمت HAMJEEB-XXXXXXX تولید می‌شود.
4. **وضعیت استفاده:**
   - لینک دعوت جدید به صورت پیش‌فرض استفاده نشده (is_used=false) است.

---

## 🔍 کاربردها:
- ایجاد لینک دعوت برای دوستان و آشنایان
- گسترش شبکه کاربران در یک ریجن
- افزایش تعداد اعضای ریجن
"""


def doc_change_user_region():
    return """
# 🔄 Scenario
تغییر ریجن کاربر

کاربر می‌تواند ریجن خود را تغییر دهد یا در صورت عدم عضویت در هیچ ریجنی، به ریجن جدیدی بپیوندد. این عملیات باعث بروزرسانی یا ایجاد عضویت کاربر در ریجن انتخابی می‌شود.

---

## 🚀 درخواست API

### URL:
```
POST /api/region/change/
```

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Token your_auth_token_here     |

### Body:
```json
{
    "region_id": 2
}
```

### فیلدهای درخواست:

| فیلد        | نوع     | الزامی | توضیحات                                    |
|------------|---------|--------|--------------------------------------------|
| `region_id`| integer | ✅     | شناسه ریجنی که کاربر می‌خواهد به آن بپیوندد |

---

## 📄 نمونه درخواست:

### درخواست کامل:
```json
{
    "region_id": 3
}
```

### پاسخ موفق - تغییر ریجن موجود:
```json
{
    "status": "success",
    "message": "Region changed successfully",
    "user_region": {
        "id": 15,
        "user": 123,
        "region": 3,
        "region_name": "شیراز",
        "user_fullname": "علی احمدی",
        "invitation_code": "USER-XYZ789",
        "invited_by": null,
        "is_active": true,
        "created_at": "2025-07-14T10:30:00Z"
    }
}
```

### پاسخ موفق - ایجاد عضویت جدید:
```json
{
    "status": "success",
    "message": "Region membership created successfully",
    "user_region": {
        "id": 16,
        "user": 123,
        "region": 2,
        "region_name": "اصفهان",
        "user_fullname": "علی احمدی",
        "invitation_code": "USER-ABC456",
        "invited_by": null,
        "is_active": true,
        "created_at": "2025-07-14T10:35:00Z"
    }
}
```

### پاسخ خطا - ریجن یافت نشد:
```json
{
    "status": "error",
    "message": "Region not found"
}
```

### پاسخ خطا - خطای اعتبارسنجی:
```json
{
    "status": "error",
    "message": "Validation failed",
    "errors": {
        "region_id": ["This field is required."]
    }
}
```

### پاسخ خطا - عدم احراز هویت:
```json
{
    "detail": "Authentication credentials were not provided."
}
```

---

## 💡 نکات مهم:
1. **احراز هویت:**
   - کاربر باید توکن احراز هویت معتبر ارائه دهد.
2. **بروزرسانی عضویت:**
   - اگر کاربر قبلاً عضو ریجن دیگری بوده، عضویت او به ریجن جدید منتقل می‌شود.
3. **ایجاد عضویت جدید:**
   - اگر کاربر هیچ عضویت ریجنی نداشته باشد، عضویت جدید ایجاد می‌شود.
4. **کد دعوت:**
   - هر کاربر کد دعوت منحصر به فرد دریافت می‌کند.
5. **وضعیت فعال:**
   - عضویت کاربر به صورت خودکار فعال می‌شود.

---

## 🔍 کاربردها:
- تغییر ریجن کاربر در تنظیمات
- پیوستن به ریجن جدید
- مدیریت عضویت ریجنی کاربران
- بروزرسانی اطلاعات مکانی کاربر
"""

# Generated manually for UserRegion structure update

from django.db import migrations, models
import django.db.models.deletion


def migrate_existing_userregions(apps, schema_editor):
    """
    Migration function to handle existing UserRegion data
    Set is_current=True for all existing UserRegion records
    """
    UserRegion = apps.get_model('region', 'UserRegion')
    
    # Set all existing UserRegion records as current
    for user_region in UserRegion.objects.all():
        user_region.is_current = True
        user_region.save()


def reverse_migrate_userregions(apps, schema_editor):
    """
    Reverse migration - nothing special needed
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('region', '0003_invitationlink'),
    ]

    operations = [
        # Step 1: Add the is_current field with default False
        migrations.AddField(
            model_name='userregion',
            name='is_current',
            field=models.BooleanField(default=False, help_text="Indicates if this is the user's current active region"),
        ),
        
        # Step 2: Add unique_together constraint
        migrations.AlterUniqueTogether(
            name='userregion',
            unique_together={('user', 'region')},
        ),
        
        # Step 3: Change the user field from OneToOneField to ForeignKey
        migrations.AlterField(
            model_name='userregion',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='region_memberships', to='account.user'),
        ),
        
        # Step 4: Run data migration to set existing records as current
        migrations.RunPython(
            migrate_existing_userregions,
            reverse_migrate_userregions,
        ),
    ]

# Generated by Django 5.2.1 on 2025-07-15 14:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('region', '0002_remove_region_owner_region_invitation_code'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='InvitationLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invitation_code', models.CharField(max_length=255, unique=True)),
                ('is_used', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('used_at', models.DateTimeField(blank=True, null=True)),
                ('used_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='used_invitation_links', to=settings.AUTH_USER_MODEL)),
                ('user_region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitation_links', to='region.userregion')),
            ],
            options={
                'verbose_name': 'Invitation Link',
                'verbose_name_plural': 'Invitation Links',
                'ordering': ['-created_at'],
            },
        ),
    ]

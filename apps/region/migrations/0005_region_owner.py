# Generated by Django 3.2.4 on 2025-08-02 16:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('region', '0004_update_userregion_structure'),
    ]

    operations = [
        migrations.AddField(
            model_name='region',
            name='owner',
            field=models.ForeignKey(blank=True, help_text='The user who owns and manages this region', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owned_regions', to=settings.AUTH_USER_MODEL, verbose_name='Region Owner'),
        ),
    ]

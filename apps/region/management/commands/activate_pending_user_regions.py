from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from apps.region.models import UserRegion
from apps.region.tasks import activate_user_region


class Command(BaseCommand):
    help = 'Activate UserRegions that have been inactive for more than 3 days'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be activated without actually activating',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force activate all inactive UserRegions regardless of creation date',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        # Find UserRegions that should be activated
        three_days_ago = timezone.now() - timedelta(days=3)
        
        if force:
            # Get all inactive UserRegions
            pending_user_regions = UserRegion.objects.filter(is_active=False)
            self.stdout.write(f"Found {pending_user_regions.count()} inactive UserRegions (force mode)")
        else:
            # Get UserRegions created more than 3 days ago and still inactive
            pending_user_regions = UserRegion.objects.filter(
                is_active=False,
                created_at__lte=three_days_ago
            )
            self.stdout.write(f"Found {pending_user_regions.count()} UserRegions ready for activation")

        if not pending_user_regions.exists():
            self.stdout.write(self.style.SUCCESS("No UserRegions need activation"))
            return

        activated_count = 0
        
        for user_region in pending_user_regions:
            if dry_run:
                self.stdout.write(
                    f"Would activate: UserRegion {user_region.id} - "
                    f"{user_region.user.fullname} in {user_region.region.name} "
                    f"(created: {user_region.created_at})"
                )
            else:
                try:
                    # Activate directly instead of using Celery task for management command
                    user_region.is_active = True
                    user_region.save()
                    activated_count += 1
                    
                    self.stdout.write(
                        f"Activated: UserRegion {user_region.id} - "
                        f"{user_region.user.fullname} in {user_region.region.name}"
                    )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Failed to activate UserRegion {user_region.id}: {str(e)}"
                        )
                    )

        if dry_run:
            self.stdout.write(
                self.style.WARNING(f"DRY RUN: Would have activated {pending_user_regions.count()} UserRegions")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f"Successfully activated {activated_count} UserRegions")
            )
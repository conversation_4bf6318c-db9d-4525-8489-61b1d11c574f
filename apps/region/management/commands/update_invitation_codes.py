from django.core.management.base import BaseCommand
from django.db import transaction
from apps.region.models import UserRegion, InvitationLink
from apps.account.models import User


class Command(BaseCommand):
    help = 'Migrates invitation codes from UserRegion to InvitationLink model'

    def handle(self, *args, **options):
        # Get all UserRegion objects that have an invitation code
        user_regions = UserRegion.objects.filter(invitation_code__isnull=False)
        total_count = user_regions.count()

        self.stdout.write(self.style.SUCCESS(f'Found {total_count} UserRegion records with invitation codes to migrate'))

        created_count = 0
        skipped_count = 0

        # Process each UserRegion
        with transaction.atomic():
            for user_region in user_regions:
                try:
                    # Check if InvitationLink already exists for this UserRegion
                    existing_link = InvitationLink.objects.filter(user_region=user_region).first()
                    if existing_link:
                        skipped_count += 1
                        self.stdout.write(f'InvitationLink already exists for UserRegion ID {user_region.id}')
                        continue

                    # Create InvitationLink with the existing invitation code
                    invitation_link = InvitationLink.objects.create(
                        user_region=user_region,
                        invitation_code=user_region.invitation_code
                    )

                    created_count += 1
                    self.stdout.write(f'Created InvitationLink for UserRegion ID {user_region.id}: {invitation_link.invitation_code}')

                except Exception as e:
                    skipped_count += 1
                    self.stdout.write(self.style.ERROR(f'Error creating InvitationLink for UserRegion ID {user_region.id}: {str(e)}'))

        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} invitation links'))
        if skipped_count > 0:
            self.stdout.write(self.style.WARNING(f'Skipped {skipped_count} records'))
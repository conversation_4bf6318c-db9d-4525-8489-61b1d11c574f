from rest_framework import serializers
from .models import Region, UserRegion, InvitationLink


class RegionListSerializer(serializers.ModelSerializer):
    """Serializer for listing regions with user membership status"""
    is_current_user_region = serializers.SerializerMethodField()
    members_count = serializers.ReadOnlyField()
    
    class Meta:
        model = Region
        fields = [
            'id', 
            'name', 
            'members_count',
            'is_current_user_region',
            'created_at'
        ]
        
    def get_is_current_user_region(self, obj):
        """Check if this is the user's current active region"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            current_region = request.user.user_region
            return current_region and current_region.id == obj.id
        return False


class ChangeUserRegionSerializer(serializers.Serializer):
    """Serializer for changing user's region"""
    region_id = serializers.IntegerField(
        help_text="ID of the region to join"
    )
    
    def validate_region_id(self, value):
        """Validate that the region exists and user is a member of it"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            raise serializers.ValidationError("Authentication required.")

        try:
            region = Region.objects.get(id=value)
        except Region.DoesNotExist:
            raise serializers.ValidationError("Region with this ID does not exist.")

        # Check if user is a member of this region
        if not request.user.is_member_of_region(region):
            raise serializers.ValidationError("You are not a member of this region.")

        return value


class UserRegionSerializer(serializers.ModelSerializer):
    """Serializer for UserRegion model"""
    region_name = serializers.CharField(source='region.name', read_only=True)
    user_fullname = serializers.CharField(source='user.fullname', read_only=True)

    class Meta:
        model = UserRegion
        fields = [
            'id',
            'user',
            'region',
            'region_name',
            'user_fullname',
            'invitation_code',
            'invited_by',
            'is_active',
            'is_current',
            'created_at'
        ]
        read_only_fields = ['invitation_code', 'created_at']


class UserRegionDetailSerializer(serializers.ModelSerializer):
    """Serializer for user's own region details"""
    is_current_user_region = serializers.SerializerMethodField()
    members_count = serializers.ReadOnlyField()
    
    class Meta:
        model = Region
        fields = [
            'id', 
            'name', 
            'members_count',
            'is_current_user_region',
            'created_at'
        ]
        
    def get_is_current_user_region(self, obj):
        """Always returns True since this is the user's region"""
        return True


class InvitationLinkSerializer(serializers.ModelSerializer):
    invitation_url = serializers.SerializerMethodField()

    class Meta:
        model = InvitationLink
        fields = [
            'id',
            # 'invitation_code',
            'invitation_url',
            'is_used',
            'created_at',
            'used_at'
        ]

    def get_invitation_url(self, obj):
        """Generate full invitation URL"""
        from django.conf import settings
        return f"{settings.INVITATION_LINK_DOMAIN}{obj.invitation_code}"


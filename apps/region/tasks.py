from celery import shared_task
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


@shared_task
def activate_user_region(user_region_id):
    """
    Task to activate a UserRegion after 3 days
    """
    from apps.region.models import UserRegion
    
    try:
        user_region = UserRegion.objects.get(id=user_region_id)
        
        # Check if it's not already active
        if not user_region.is_active:
            user_region.is_active = True
            user_region.save()
            
            logger.info(f"UserRegion {user_region_id} activated successfully for user {user_region.user.fullname}")
            
            # Optional: Send notification to user about activation
            # You can add notification logic here if needed
            
            return f"UserRegion {user_region_id} activated successfully"
        else:
            logger.info(f"UserRegion {user_region_id} was already active")
            return f"UserRegion {user_region_id} was already active"
            
    except UserRegion.DoesNotExist:
        logger.error(f"UserRegion with id {user_region_id} does not exist")
        return f"UserRegion with id {user_region_id} does not exist"
    except Exception as e:
        logger.error(f"Error activating UserRegion {user_region_id}: {str(e)}")
        return f"Error activating UserRegion {user_region_id}: {str(e)}"


@shared_task
def activate_user_region_immediately(user_region_id):
    """
    Task to activate a UserRegion immediately (for testing purposes)
    """
    return activate_user_region(user_region_id)
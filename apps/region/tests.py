from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.region.models import Region, UserRegion
from apps.region.tasks import activate_user_region

User = get_user_model()


class UserRegionActivationTestCase(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            phone_number='09123456789',
            fullname='Test User'
        )
        
        # Create test region
        self.region = Region.objects.create(
            name='Test Region',
            description='Test region for activation'
        )
        
        # Create inactive UserRegion
        self.user_region = UserRegion.objects.create(
            user=self.user,
            region=self.region,
            is_active=False,
            is_current=True
        )

    def test_activate_user_region_task(self):
        """Test that the activation task works correctly"""
        # Ensure UserRegion is initially inactive
        self.assertFalse(self.user_region.is_active)
        
        # Run the activation task
        result = activate_user_region(self.user_region.id)
        
        # Refresh from database
        self.user_region.refresh_from_db()
        
        # Check that UserRegion is now active
        self.assertTrue(self.user_region.is_active)
        self.assertIn("activated successfully", result)

    def test_activate_already_active_user_region(self):
        """Test that activating an already active UserRegion doesn't cause issues"""
        # Make UserRegion active first
        self.user_region.is_active = True
        self.user_region.save()
        
        # Run the activation task
        result = activate_user_region(self.user_region.id)
        
        # Check that it's still active and returns appropriate message
        self.assertTrue(self.user_region.is_active)
        self.assertIn("was already active", result)

    def test_activate_nonexistent_user_region(self):
        """Test that trying to activate a non-existent UserRegion handles gracefully"""
        # Try to activate a UserRegion that doesn't exist
        result = activate_user_region(99999)
        
        # Check that it returns an error message
        self.assertIn("does not exist", result)

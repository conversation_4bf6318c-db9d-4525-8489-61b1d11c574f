from django.db import models
import random
import string

from apps.account.models import User


class Region(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)  
    invitation_code = models.CharField(max_length=255, unique=True, blank=True, null=True)
    owner = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='owned_regions', verbose_name="Region Owner", help_text="The user who owns and manages this region", null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True) 
    updated_at = models.DateTimeField(auto_now=True)  
    
    def __str__(self):
        return self.name 
    
    def generate_invitation_code(self):
        random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=7))        
        return f"REGION-{random_chars}"
    
    def save(self, *args, **kwargs):
        if not self.invitation_code:
            self.invitation_code = self.generate_invitation_code()
        super().save(*args, **kwargs)
        
    @property
    def members_count(self):
        """Return the number of members in this region"""
        return self.members.count() 


class UserRegion(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='region_memberships')
    region = models.ForeignKey(Region, on_delete=models.CASCADE, related_name='members')
    invitation_code = models.CharField(max_length=255, unique=True, blank=True, null=True)
    invited_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name='invited_users')
    is_active = models.BooleanField(default=False)
    is_current = models.BooleanField(default=False, help_text="Indicates if this is the user's current active region")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'region')  # Prevent duplicate memberships

    def __str__(self):
        
        current_indicator = " (Current)" if self.is_current else ""
        return f"{self.user.fullname} در {self.region.name}"

    def save(self, *args, **kwargs):
        if not self.invitation_code:
            self.invitation_code = self.user.generate_ref_link()

        # If this is being set as current, make sure no other region is current for this user
        if self.is_current:
            UserRegion.objects.filter(user=self.user, is_current=True).exclude(id=self.id).update(is_current=False)

        super().save(*args, **kwargs)
        
    @property
    def invited_users_count(self):
        """Return the number of users who joined using this user's invitation code"""
        from apps.region.models import UserRegion
        return UserRegion.objects.filter(invited_by=self.user).count()
    
    @staticmethod
    def validate_invitation_code(invitation_code):
        """
        Validate invitation code similar to API validation
        Returns tuple (is_valid, region, invited_by, error_message)
        """
        if not invitation_code:
            return False, None, None, "Invitation code is required."
        
        # Clean the invitation code if it contains a URL path
        if '/' in invitation_code:
            invitation_code = invitation_code.split('/')[-1]
        
        # First try to find an InvitationLink with this invitation code
        try:
            invitation_link = InvitationLink.objects.get(invitation_code=invitation_code)
            # Check if the invitation link is already used
            if invitation_link.is_used:
                return False, None, None, "This invitation code has already been used."
            
            # If found, use the InvitationLink's region and set the inviter
            region = invitation_link.user_region.region
            invited_by = invitation_link.user_region.user
            return True, region, invited_by, None
            
        except InvitationLink.DoesNotExist:
            # If not found in InvitationLink, try to find a UserRegion with this invitation code (backward compatibility)
            try:
                invitation_user_region = UserRegion.objects.get(invitation_code=invitation_code)
                # If found, use the UserRegion's region and set the inviter
                region = invitation_user_region.region
                invited_by = invitation_user_region.user
                return True, region, invited_by, None
                
            except UserRegion.DoesNotExist:
                # If not found in UserRegion, try to find a Region with this invitation code
                try:
                    region = Region.objects.get(invitation_code=invitation_code)
                    invited_by = None  # No specific user invited this person
                    return True, region, invited_by, None
                    
                except Region.DoesNotExist:
                    # If not found in either, the code is invalid
                    return False, None, None, "Invalid invitation code."


class InvitationLink(models.Model):
    """
    Model to store multiple invitation links for each UserRegion
    Each UserRegion can have multiple invitation links with different statuses
    """
    user_region = models.ForeignKey(UserRegion, on_delete=models.CASCADE, related_name='invitation_links')
    region = models.ForeignKey(Region, on_delete=models.CASCADE, related_name='invitation_links')
    invitation_code = models.CharField(max_length=255, unique=True)
    is_used = models.BooleanField(default=False)
    used_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name='used_invitation_links')
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Invitation Link'
        verbose_name_plural = 'Invitation Links'

    # def __str__(self):
    #     from django.conf import settings
    #     status = "Used" if self.is_used else "Active"
    #     full_url = f"{settings.INVITATION_LINK_DOMAIN}{self.invitation_code}"
    #     return f"{full_url}"

    def save(self, *args, **kwargs):
        # invitation_code should be provided when creating the instance
        super().save(*args, **kwargs)

    def generate_invitation_code(self):
        """Generate a unique invitation code"""
        random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=7))
        return f"HAMJEEB-{random_chars}"

    def mark_as_used(self, used_by_user):
        """Mark this invitation link as used by a specific user"""
        from django.utils import timezone
        self.is_used = True
        self.used_by = used_by_user
        self.used_at = timezone.now()
        self.save()
    
    @property
    def full_invitation_url(self):
        """Return the complete invitation URL"""
        from django.conf import settings
        return f"{settings.INVITATION_LINK_DOMAIN}{self.invitation_code}"




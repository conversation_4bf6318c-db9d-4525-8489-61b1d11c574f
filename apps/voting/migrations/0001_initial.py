# Generated by Django 3.2.4 on 2025-03-12 05:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('deposit', '0008_delete_depositlottery'),
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='VotingReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(help_text='The subject or title of the report.', max_length=255, verbose_name='Subject')),
                ('description', models.TextField(blank=True, help_text='A detailed description of the report.', null=True, verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='The date and time when the report was created.', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='The date and time when the report was last updated.', verbose_name='Updated At')),
                ('deposit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='deposit.deposit', verbose_name='Deposit')),
            ],
            options={
                'verbose_name': 'Voting Report',
                'verbose_name_plural': 'Voting Reports',
            },
        ),
        migrations.CreateModel(
            name='VotingReportImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('priority', models.IntegerField(default=0, help_text='Priority of the image, lower values mean higher priority.', verbose_name='Priority')),
                ('image', filer.fields.image.FilerImageField(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to=settings.FILER_IMAGE_MODEL, verbose_name='image')),
                ('report', models.ForeignKey(help_text='The report associated with this image.', on_delete=django.db.models.deletion.CASCADE, related_name='images', to='voting.votingreport', verbose_name='Voting Report')),
            ],
            options={
                'verbose_name': 'Report Image',
                'verbose_name_plural': 'Report Images',
                'ordering': ('priority',),
            },
        ),
        migrations.CreateModel(
            name='VotingPoll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Voting Topic')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Voting Description')),
                ('deadline_days', models.PositiveIntegerField(help_text='Number of days the voting will be open', verbose_name='Voting Deadline (days)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('deposit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='voting_polls', to='deposit.deposit', verbose_name='Deposit')),
            ],
            options={
                'verbose_name': 'Voting Poll',
                'verbose_name_plural': 'Voting Polls',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VotingOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Option Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Option Description')),
                ('color_name', models.CharField(blank=True, help_text='Name of the color representing this option', max_length=50, null=True, verbose_name='Color Name')),
                ('voting_poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='voting.votingpoll', verbose_name='Voting Poll')),
            ],
            options={
                'verbose_name': 'Voting Option',
                'verbose_name_plural': 'Voting Options',
            },
        ),
        migrations.CreateModel(
            name='Vote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Voted At')),
                ('membership', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='deposit.depositmembership', verbose_name='Deposit Member')),
                ('option', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='voting.votingoption', verbose_name='Selected Option')),
                ('voting_poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='voting.votingpoll', verbose_name='Voting Poll')),
            ],
            options={
                'verbose_name': 'Vote',
                'verbose_name_plural': 'Votes',
                'unique_together': {('voting_poll', 'membership')},
            },
        ),
    ]

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from datetime import timedelta
from apps.deposit.models.deposit import Deposit, DepositMembership

class VotingPoll(models.Model):
    """
    مدل رای‌گیری برای صندوق‌ها
    """
    deposit = models.ForeignKey(
        Deposit, 
        on_delete=models.CASCADE, 
        related_name='voting_polls',
        verbose_name=_("Deposit")
    )
    title = models.CharField(
        max_length=255, 
        verbose_name=_('Voting Topic')
    )
    description = models.TextField(
        verbose_name=_('Voting Description'),
        null=True,
        blank=True
    )
    deadline_days = models.PositiveIntegerField(
        verbose_name=_('Voting Deadline (days)'),
        help_text=_('Number of days the voting will be open')
    )
    created_at = models.DateTimeField(
        auto_now_add=True, 
        verbose_name=_('Created At')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    is_deleted = models.BooleanField(
        default=False,
        verbose_name=_('Is Deleted')
    )

    class Meta:
        verbose_name = _('Voting Poll')
        verbose_name_plural = _('Voting Polls')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} - {self.deposit.title}"
    
    @property
    def end_date(self):
        """
        محاسبه تاریخ اتمام رای‌گیری بر اساس تاریخ شروع و مهلت رای‌گیری
        """
        return self.created_at + timedelta(days=self.deadline_days)
    
    @property
    def is_expired(self):
        return timezone.now() > self.end_date
    
    def check_expiration(self):
        if self.is_expired and self.is_active:
            self.is_active = False
            self.save(update_fields=['is_active'])
        return self


class VotingOption(models.Model):
    """
    گزینه‌های رای‌گیری
    """
    voting_poll = models.ForeignKey(
        VotingPoll, 
        on_delete=models.CASCADE, 
        related_name='options',
        verbose_name=_('Voting Poll')
    )
    title = models.CharField(
        max_length=255, 
        verbose_name=_('Option Title')
    )
    description = models.TextField(
        verbose_name=_('Option Description'),
        null=True,
        blank=True
    )
    color_name = models.CharField(
        max_length=50,
        verbose_name=_('Color Name'),
        null=True,
        blank=True,
        help_text=_('Name of the color representing this option')
    )
    
    class Meta:
        verbose_name = _('Voting Option')
        verbose_name_plural = _('Voting Options')
    
    def __str__(self):
        return self.title
    
    @property
    def votes_count(self):
        """
        تعداد آرای این گزینه
        """
        return self.votes.count()


class Vote(models.Model):
    """
    رای اعضای صندوق
    """
    voting_poll = models.ForeignKey(
        VotingPoll, 
        on_delete=models.CASCADE, 
        related_name='votes',
        verbose_name=_('Voting Poll')
    )
    option = models.ForeignKey(
        VotingOption, 
        on_delete=models.CASCADE, 
        related_name='votes',
        verbose_name=_('Selected Option')
    )
    membership = models.ForeignKey(
        DepositMembership, 
        on_delete=models.CASCADE, 
        related_name='votes',
        verbose_name=_('Deposit Member')
    )
    created_at = models.DateTimeField(
        auto_now_add=True, 
        verbose_name=_('Voted At')
    )
    
    class Meta:
        verbose_name = _('Vote')
        verbose_name_plural = _('Votes')
        # هر عضو فقط یک بار می‌تواند در هر رای‌گیری شرکت کند
        unique_together = ('voting_poll', 'membership')
    
    def __str__(self):
        return f"{self.membership.user.fullname} - {self.option.title}"
    
    def clean(self):
        """
        اعتبارسنجی رای
        """
        from django.core.exceptions import ValidationError
        
        # بررسی اینکه آیا گزینه انتخابی متعلق به رای‌گیری است
        if self.option.voting_poll != self.voting_poll:
            raise ValidationError(_("The selected option does not belong to this voting poll."))
        
        # بررسی اینکه آیا مهلت رای‌گیری به پایان رسیده است
        if self.voting_poll.is_expired:
            raise ValidationError(_("The voting deadline has passed."))
        
        # بررسی اینکه آیا رای‌گیری فعال است
        if not self.voting_poll.is_active:
            raise ValidationError(_("This voting poll is not active."))
        
        # بررسی اینکه آیا عضو متعلق به صندوق مربوطه است
        if self.membership.deposit != self.voting_poll.deposit:
            raise ValidationError(_("The member does not belong to this deposit."))
        
        # بررسی اینکه آیا عضویت فعال است
        if not self.membership.is_active:
            raise ValidationError(_("The membership is not active."))
    
    def save(self, *args, **kwargs):
        # self.clean()
        super().save(*args, **kwargs)

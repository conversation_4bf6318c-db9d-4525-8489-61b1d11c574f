from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import Filer<PERSON><PERSON><PERSON>ield
from apps.deposit.models.deposit import Deposit


class VotingReport(models.Model):
    deposit = models.ForeignKey(
        Deposit, 
        on_delete=models.CASCADE, 
        related_name='reports', 
        verbose_name=_("Deposit"), 
    )
    amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name=_("Amount"))
    subject = models.CharField(
        max_length=255, 
        verbose_name=_("Subject"), 
        help_text=_("The subject or title of the report.")
    )
    description = models.TextField(
        verbose_name=_("Description"), 
        help_text=_("A detailed description of the report."), 
        null=True, 
        blank=True
    )
    created_at = models.DateTimeField(
        auto_now_add=True, 
        verbose_name=_("Created At"), 
        help_text=_("The date and time when the report was created.")
    )
    updated_at = models.DateTimeField(
        auto_now=True, 
        verbose_name=_("Updated At"), 
        help_text=_("The date and time when the report was last updated.")
    )

    class Meta:
        verbose_name = _("Voting Report")
        verbose_name_plural = _("Voting Reports")

    def __str__(self):
        return f"Report #{self.id} - {self.subject}"


class VotingReportImage(models.Model):
    report = models.ForeignKey(
        VotingReport, 
        on_delete=models.CASCADE, 
        related_name='images', 
        verbose_name=_("Voting Report"), 
        help_text=_("The report associated with this image.")
    )
    image = models.ImageField(
        upload_to='voting_report_images', null=True, blank=True,
        verbose_name=_('image')
    )
    priority = models.IntegerField(
        default=0, 
        verbose_name=_("Priority"),
        help_text=_("Priority of the image, lower values mean higher priority.")
    )

    class Meta:
        verbose_name = _('Report Image')
        verbose_name_plural = _('Report Images')
        ordering = ('priority',)

    def __str__(self):
        return f'{self.report.subject}-{self.id}'
    
    def save(self, *args, **kwargs):
        if VotingReportImage.objects.filter(report=self.report, priority=self.priority).exists():
            VotingReportImage.objects.filter(
                report=self.report,
                priority__gte=self.priority
            ).update(priority=models.F('priority') + 1)
        super().save(*args, **kwargs)

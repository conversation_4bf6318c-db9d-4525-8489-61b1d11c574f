from django.shortcuts import get_object_or_404
from django.db.models import Count, F, Sum, Case, When, IntegerField
from rest_framework import status, generics
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from apps.voting.models import VotingPoll, VotingOption, Vote
from apps.voting.serializers import (
    VotingPollListSerializer, VotingPollCreateSerializer,
    VotingPollDetailSerializer, VoteCreateSerializer
)
from apps.deposit.models.deposit import DepositMembership, Deposit
from utils.exceptions import AppAPIException


class DepositVotingListView(generics.ListAPIView):
    """
    نمایش لیست رای‌گیری‌های یک صندوق مشخص
    deposit/<int:deposit_id>/votings/list
    """
    serializer_class = VotingPollListSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        deposit_id = self.kwargs.get('deposit_id')
        user = self.request.user
        
        try:
            deposit = Deposit.objects.get(id=deposit_id)
        except Deposit.DoesNotExist:
            raise AppAPIException({"message": "Deposit not found"}, status_code=status.HTTP_404_NOT_FOUND)
        
            
        return VotingPoll.objects.filter(deposit_id=deposit_id, is_deleted=False)


class VotingPollCreateView(generics.CreateAPIView):
    """
    ایجاد رای‌گیری جدید
    /votings/create/
    """
    serializer_class = VotingPollCreateSerializer
    permission_classes = [IsAuthenticated]
    
    def perform_create(self, serializer):
        deposit_id = serializer.validated_data.get('deposit').id
        user = self.request.user
        
        membership = DepositMembership.objects.filter(
            user=user,
            deposit_id=deposit_id,
            is_active=True,
            role__in=[DepositMembership.Role.OWNER, DepositMembership.Role.ADMIN]
        ).first()
        
        if not membership:
            raise AppAPIException(
                {"message": "You must be an owner or admin of the deposit to create a voting poll."}, 
                status_code=status.HTTP_403_FORBIDDEN
            )
            
        deposit = Deposit.objects.get(id=deposit_id)
        if deposit.deposit_type != Deposit.DepositType.REPORTING:
            raise AppAPIException(
                {"message": "Voting polls can only be created for Reporting deposits."}, 
                status_code=status.HTTP_400_BAD_REQUEST
            )
            
        # Save the voting poll
        voting_poll = serializer.save()
        
        # Send notification to all deposit members
        from apps.account.services import NotificationService
        try:
            NotificationService.send_voting_poll_created_notification(deposit_id)
        except Exception as e:
            # Log the error but don't prevent the voting poll from being created
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error sending voting poll creation notification: {str(e)}")


class VotingPollDetailView(generics.RetrieveAPIView):
    """
    نمایش جزئیات یک رای‌گیری
    /votings/<int:pk>/
    """
    queryset = VotingPoll.objects.filter(is_deleted=False)
    # serializer_class = VotingPollDetailSerializer
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        try:
            voting_poll = VotingPoll.objects.get(id=pk, is_deleted=False)
        except VotingPoll.DoesNotExist:
            raise AppAPIException(
                {"message": "Voting poll not found."}, 
                status_code=status.HTTP_404_NOT_FOUND
            )
            
        user = request.user
        
            
        total_votes = voting_poll.votes.count()
        
        if total_votes == 0:
            # اگر هیچ رأیی ثبت نشده باشد
            options_data = []
            for option in voting_poll.options.all():
                options_data.append({
                    'id': option.id,
                    'title': option.title,
                    'description': option.description,
                    'color_name': option.color_name,
                    'votes_count': 0,
                    'percentage': 0.0,
                    'voters': []
                })
                
            return Response({
                'id': voting_poll.id,
                'title': voting_poll.title,
                'description': voting_poll.description,
                'total_votes': 0,
                'options': options_data
            })
        
        # محاسبه تعداد آرا و درصد هر گزینه
        options_with_votes = []
        
        for option in voting_poll.options.all():
            votes = option.votes.all().select_related('membership', 'membership__user')
            votes_count = votes.count()
            percentage = (votes_count / total_votes) * 100 if total_votes > 0 else 0
            
            # آماده‌سازی داده‌های گزینه
            option_data = {
                'id': option.id,
                'title': option.title,
                'description': option.description,
                'color_name': option.color_name,
                'votes_count': votes_count,
                'percentage': round(percentage, 2),
                'voters': []
            }
            
            # اضافه کردن اطلاعات رای دهندگان
            for vote in votes:
                option_data['voters'].append({
                    'id': vote.membership.id,
                    'fullname': vote.membership.user.fullname,
                    'role': vote.membership.role
                })
                
            options_with_votes.append(option_data)
            
        return Response({
            'id': voting_poll.id,
            'title': voting_poll.title,
            'description': voting_poll.description,
            'total_votes': total_votes,
            'created_at': voting_poll.created_at,
            'end_date': voting_poll.end_date,
            'options': options_with_votes
        })



class VoteCreateView(generics.CreateAPIView):
    """
    ثبت رای جدید
    /votings/join/
    """
    serializer_class = VoteCreateSerializer
    permission_classes = [IsAuthenticated]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        voting_poll = serializer.validated_data['voting_poll']
        option = serializer.validated_data['option']
        user = request.user
        
        # بررسی اینکه آیا کاربر عضو صندوق است
        membership = DepositMembership.objects.filter(
            user=user,
            deposit=voting_poll.deposit,
            is_active=True
        ).first()
        
        if not membership:
            raise AppAPIException(
                {"message": "You must be a member of the deposit to vote."}, 
                status_code=status.HTTP_403_FORBIDDEN
            )
            
        # بررسی اینکه آیا کاربر قبلاً رای داده است
        if Vote.objects.filter(voting_poll=voting_poll, membership=membership).exists():
            raise AppAPIException(
                {"message": "You have already voted in this poll."}, 
                status_code=status.HTTP_400_BAD_REQUEST
            )
            
        # ایجاد رای جدید
        vote = Vote.objects.create(
            voting_poll=voting_poll,
            option=option,
            membership=membership
        )
        
        return Response(
            {"message": "Your vote has been successfully recorded."},
            status=status.HTTP_201_CREATED
        )


class VotingPollReportView(APIView):
    """
    نمایش گزارش نتایج یک رای‌گیری
    /votings/<int:pk>/reporting
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, pk):
        try:
            voting_poll = VotingPoll.objects.get(id=pk, is_deleted=False)
        except VotingPoll.DoesNotExist:
            raise AppAPIException(
                {"message": "Voting poll not found."}, 
                status_code=status.HTTP_404_NOT_FOUND
            )
            
        user = request.user
        
            
        total_votes = voting_poll.votes.count()
        
        if total_votes == 0:
            # اگر هیچ رأیی ثبت نشده باشد
            options_data = []
            for option in voting_poll.options.all():
                options_data.append({
                    'id': option.id,
                    'title': option.title,
                    'description': option.description,
                    'color_name': option.color_name,
                    'votes_count': 0,
                    'percentage': 0.0,
                    'voters': []
                })
                
            return Response({
                'id': voting_poll.id,
                'title': voting_poll.title,
                'description': voting_poll.description,
                'total_votes': 0,
                'options': options_data
            })
        
        # محاسبه تعداد آرا و درصد هر گزینه
        options_with_votes = []
        
        for option in voting_poll.options.all():
            votes = option.votes.all().select_related('membership', 'membership__user')
            votes_count = votes.count()
            percentage = (votes_count / total_votes) * 100 if total_votes > 0 else 0
            
            # آماده‌سازی داده‌های گزینه
            option_data = {
                'id': option.id,
                'title': option.title,
                'description': option.description,
                'color_name': option.color_name,
                'votes_count': votes_count,
                'percentage': round(percentage, 2),
                'voters': []
            }
            
            # اضافه کردن اطلاعات رای دهندگان
            for vote in votes:
                option_data['voters'].append({
                    'id': vote.membership.id,
                    'fullname': vote.membership.user.fullname,
                    'role': vote.membership.role
                })
                
            options_with_votes.append(option_data)
            
        return Response({
            'id': voting_poll.id,
            'title': voting_poll.title,
            'description': voting_poll.description,
            'total_votes': total_votes,
            'options': options_with_votes
        })


class VotingPollDeleteView(APIView):
    """
    حذف منطقی یک رای‌گیری
    /votings/<int:pk>/delete/
    """
    permission_classes = [IsAuthenticated]
    
    def delete(self, request, pk):
        try:
            voting_poll = VotingPoll.objects.get(id=pk)
        except VotingPoll.DoesNotExist:
            raise AppAPIException(
                {"message": "Voting poll not found."}, 
                status_code=status.HTTP_404_NOT_FOUND
            )
            
        user = request.user
        
        voting_poll.is_deleted = True
        voting_poll.save(update_fields=['is_deleted'])
        
        return Response(
            {"message": "Voting poll has been successfully deleted."}, 
            status=status.HTTP_200_OK
        )

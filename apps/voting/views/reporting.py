from rest_framework.generics import (
    ListAPIView, 
    CreateAPIView, 
    UpdateAPIView, 
    DestroyAPIView,
    RetrieveAPIView
)
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from apps.voting.models import VotingReport, VotingReportImage
from apps.deposit.models import Deposit
from apps.voting.serializers import (
    VotingReportSerializer,
    VotingReportListSerializer,
    VotingReportDetailSerializer,
    VotingReportImageSerializer
)
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers


class VotingReportListAPIView(ListAPIView):
    serializer_class = VotingReportListSerializer
    
    def get_queryset(self):
        """
        Return a list of reports for a specific deposit box.
        """
        deposit_id = self.kwargs.get('deposit_id')
        return VotingReport.objects.filter(deposit_id=deposit_id).order_by('-created_at')


class VotingReportDetailAPIView(RetrieveAPIView):
    serializer_class = VotingReportDetailSerializer
    queryset = VotingReport.objects.all()
    lookup_field = 'id'
    lookup_url_kwarg = 'report_id'


class VotingReportDeleteAPIView(DestroyAPIView):
    queryset = VotingReport.objects.all()
    lookup_field = 'id'
    lookup_url_kwarg = 'report_id'
    



class VotingReportCreateAPIView(CreateAPIView):
    serializer_class = VotingReportSerializer
    
    @swagger_auto_schema(
        request_body=VotingReportSerializer
    )
    def post(self, request, *args, **kwargs):
        deposit_id = kwargs.get('deposit_id')
        print(f'-sss-> {deposit_id}')
        deposit = get_object_or_404(Deposit, id=deposit_id)
        
        # Process report data
        report_data = request.data.copy()
        images_data = report_data.pop('images', None)
        
        # Create report
        voting_report = VotingReport.objects.create(deposit=deposit, **report_data)
        
        # Process images if provided
        if images_data:
            image_serializer = VotingReportImageSerializer(
                data=images_data, 
                many=True, 
                context=self.get_serializer_context()
            )
            image_serializer.is_valid(raise_exception=True)
            image_serializer.save(report=voting_report)
        
        final_serializer = VotingReportSerializer(voting_report, context=self.get_serializer_context())
        return Response(final_serializer.data, status=status.HTTP_201_CREATED)
    
    def get_serializer_context(self):
        return {'request': self.request}


class VotingReportUpdateAPIView(UpdateAPIView):
    queryset = VotingReport.objects.all()
    serializer_class = VotingReportSerializer
    
    @swagger_auto_schema(
        request_body=VotingReportSerializer
    )
    def patch(self, request, *args, **kwargs):
        report_id = kwargs.get('report_id')
        report = get_object_or_404(VotingReport, id=report_id)
        
        try:
            # Update report fields
            report_data = request.data.copy()
            images_data = report_data.pop('images', None)
            
            for key, value in report_data.items():
                setattr(report, key, value)
            report.save()
            
        except Exception as e:
            return Response(
                {"detail": f"Error updating the report: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Handle images update
        if images_data:
            try:
                for data in images_data:
                    image_instance = None
                    
                    # Handle image deletion
                    if 'id' in data and data.get('image') == "delete":
                        try:
                            image_instance = get_object_or_404(VotingReportImage, id=data['id'])
                            image_instance.image = None
                            image_instance.save()
                            data.pop('image')
                            continue  # Skip further processing for this entry
                        except VotingReportImage.DoesNotExist:
                            return Response(
                                {"detail": "Image with the specified ID does not exist."},
                                status=status.HTTP_404_NOT_FOUND
                            )
                    
                    # Validate and update/create image
                    image_serializer = VotingReportImageSerializer(data=data, context=self.get_serializer_context())
                    image_serializer.is_valid(raise_exception=True)
                    
                    if 'id' in data:
                        try:
                            image_instance = get_object_or_404(VotingReportImage, id=data['id'])
                            image_serializer.update(image_instance, image_serializer.validated_data)
                        except VotingReportImage.DoesNotExist:
                            return Response(
                                {"detail": "Image with the specified ID does not exist."},
                                status=status.HTTP_404_NOT_FOUND
                            )
                    else:
                        if data.get('image') is None:
                            continue
                        
                        # Skip if the image is already uploaded
                        if '/static/uploads/' in str(data['image']):
                            continue
                        
                        # Create a new image
                        image_serializer.save(report=report)
                        
            except serializers.ValidationError as ve:
                return Response(
                    {"images_error": ve.detail},
                    status=status.HTTP_400_BAD_REQUEST
                )
            except Exception as e:
                return Response(
                    {"detail": f"Error processing images: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Return the updated report
        try:
            final_serializer = VotingReportSerializer(report, context=self.get_serializer_context())
            return Response(final_serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"detail": f"Error serializing the updated report: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def get_serializer_context(self):
        return {'request': self.request}

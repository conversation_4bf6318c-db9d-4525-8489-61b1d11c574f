from rest_framework import serializers
from apps.voting.models import VotingPoll, VotingOption, Vote
from apps.deposit.serializers.deposit import DepositMembershipSerializer
from apps.deposit.models.deposit import DepositMembership

class VotingOptionSerializer(serializers.ModelSerializer):
    votes_count = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = VotingOption
        fields = ['id', 'title', 'description', 'color_name', 'votes_count']
        read_only_fields = ['id']


class VotingPollSerializer(serializers.ModelSerializer):
    options = VotingOptionSerializer(many=True, read_only=True)
    end_date = serializers.DateTimeField(read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    total_votes = serializers.SerializerMethodField()
    user_voted = serializers.SerializerMethodField()
    user_selected_option = serializers.SerializerMethodField()
    
    class Meta:
        model = VotingPoll
        fields = [
            'id', 'deposit', 'title', 'description', 'deadline_days', 
            'created_at', 'is_active', 'end_date', 'is_expired', 
            'options', 'total_votes', 'user_voted', 'user_selected_option'
        ]
        read_only_fields = ['id', 'created_at', 'end_date', 'is_expired']
    
    def get_total_votes(self, obj):
        """
        مجموع آرای ثبت شده برای این رای‌گیری
        """
        return obj.votes.count()
    
    def get_user_voted(self, obj):
        """
        آیا کاربر فعلی در این رای‌گیری شرکت کرده است
        """
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return False
            
        # یافتن عضویت‌های کاربر در صندوق مربوط به رای‌گیری
        memberships = request.user.deposit_memberships.filter(
            deposit=obj.deposit,
            is_active=True
        )
        
        if not memberships.exists():
            return False
            
        # بررسی اینکه آیا کاربر رای داده است
        return Vote.objects.filter(
            voting_poll=obj,
            membership__in=memberships
        ).exists()
    
    def get_user_selected_option(self, obj):
        """
        گزینه انتخاب شده توسط کاربر فعلی (اگر رای داده باشد)
        """
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None
            
        # یافتن عضویت‌های کاربر در صندوق مربوط به رای‌گیری
        memberships = request.user.deposit_memberships.filter(
            deposit=obj.deposit,
            is_active=True
        )
        
        if not memberships.exists():
            return None
            
        # یافتن رای کاربر
        vote = Vote.objects.filter(
            voting_poll=obj,
            membership__in=memberships
        ).first()
        
        if not vote:
            return None
            
        return vote.option.id
        
    def to_representation(self, instance):
        """
        قبل از برگرداندن داده‌ها، بررسی می‌کند آیا زمان رای‌گیری به پایان رسیده است یا خیر
        """
        # بررسی انقضای رای‌گیری و به‌روزرسانی وضعیت فعال بودن
        instance = instance.check_expiration()
        return super().to_representation(instance)


class VoteSerializer(serializers.ModelSerializer):
    voter = serializers.SerializerMethodField()
    
    class Meta:
        model = Vote
        fields = ['id', 'voting_poll', 'option', 'membership', 'created_at', 'voter']
        read_only_fields = ['id', 'created_at', 'voter']
    
    def get_voter(self, obj):
        """
        نام رای دهنده
        """
        return obj.membership.user.fullname
    
    def validate(self, data):
        """
        اعتبارسنجی داده‌های ورودی
        """
        voting_poll = data.get('voting_poll')
        option = data.get('option')
        membership = data.get('membership')
        
        # بررسی اینکه آیا گزینه انتخابی متعلق به رای‌گیری است
        if option.voting_poll != voting_poll:
            raise serializers.ValidationError("The selected option does not belong to this voting poll.")
        
        # بررسی اینکه آیا مهلت رای‌گیری به پایان رسیده است
        if voting_poll.is_expired:
            raise serializers.ValidationError("The voting deadline has passed.")
        
        # بررسی اینکه آیا رای‌گیری فعال است
        if not voting_poll.is_active:
            raise serializers.ValidationError("This voting poll is not active.")
        
        # بررسی اینکه آیا عضو متعلق به صندوق مربوطه است
        if membership.deposit != voting_poll.deposit:
            raise serializers.ValidationError("The member does not belong to this deposit.")
        
        # بررسی اینکه آیا عضویت فعال است
        if not membership.is_active:
            raise serializers.ValidationError("The membership is not active.")
        
        # بررسی اینکه آیا کاربر قبلاً رای داده است
        if Vote.objects.filter(voting_poll=voting_poll, membership=membership).exists():
            raise serializers.ValidationError({
                "membership": "You have already voted in this poll."
            })
        
        return data


class VotingPollListSerializer(serializers.ModelSerializer):
    end_date = serializers.DateTimeField(read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    user_voted = serializers.SerializerMethodField()
    user_selected_option = serializers.SerializerMethodField()
    
    class Meta:
        model = VotingPoll
        fields = ['id', 'title', 'description', 'created_at', 'end_date', 'is_active', 'is_expired', 'user_voted', 'user_selected_option']
        read_only_fields = ['id', 'created_at', 'end_date']
    


    def get_user_voted(self, obj):
        """
        آیا کاربر فعلی در این رای‌گیری شرکت کرده است
        """
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return False
            
        # یافتن عضویت‌های کاربر در صندوق مربوط به رای‌گیری
        memberships = request.user.deposit_memberships.filter(
            deposit=obj.deposit,
            is_active=True
        )
        
        if not memberships.exists():
            return False
            
        # بررسی اینکه آیا کاربر رای داده است
        return Vote.objects.filter(
            voting_poll=obj,
            membership__in=memberships
        ).exists()
    
    def get_user_selected_option(self, obj):
        """
        گزینه انتخاب شده توسط کاربر فعلی (اگر رای داده باشد)
        """
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None
            
        # یافتن عضویت‌های کاربر در صندوق مربوط به رای‌گیری
        memberships = request.user.deposit_memberships.filter(
            deposit=obj.deposit,
            is_active=True
        )
        
        if not memberships.exists():
            return None
            
        # یافتن رای کاربر
        vote = Vote.objects.filter(
            voting_poll=obj,
            membership__in=memberships
        ).first()
        
        if not vote:
            return None
            
        return vote.option.id

    def to_representation(self, instance):
        instance = instance.check_expiration()
        return super().to_representation(instance)
        


class VotingOptionCreateSerializer(serializers.ModelSerializer):
    """سریالایزر برای ایجاد گزینه‌های رای‌گیری"""
    class Meta:
        model = VotingOption
        fields = ['title', 'description', 'color_name']


class VotingPollCreateSerializer(serializers.ModelSerializer):
    """سریالایزر برای ایجاد رای‌گیری جدید"""
    options = VotingOptionCreateSerializer(many=True)
    
    class Meta:
        model = VotingPoll
        fields = ['deposit', 'title', 'description', 'deadline_days', 'options']
    
    def create(self, validated_data):
        options_data = validated_data.pop('options')
        
        if not options_data:
            raise serializers.ValidationError({
                "options": "At least one option is required."
            })
            
        voting_poll = VotingPoll.objects.create(**validated_data)
        
        # ایجاد گزینه‌های رای‌گیری
        for option_data in options_data:
            VotingOption.objects.create(voting_poll=voting_poll, **option_data)
            
        return voting_poll


class VotingPollDetailSerializer(serializers.ModelSerializer):
    """سریالایزر برای نمایش جزئیات یک رای‌گیری"""
    options = VotingOptionSerializer(many=True, read_only=True)
    end_date = serializers.DateTimeField(read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = VotingPoll
        fields = ['id', 'title', 'description', 'created_at', 'end_date', 'is_active', 'is_expired', 'options']
        read_only_fields = ['id', 'created_at', 'end_date', 'is_expired']
        
    def to_representation(self, instance):
        """
        قبل از برگرداندن داده‌ها، بررسی می‌کند آیا زمان رای‌گیری به پایان رسیده است یا خیر
        """
        # بررسی انقضای رای‌گیری و به‌روزرسانی وضعیت فعال بودن
        instance = instance.check_expiration()
        return super().to_representation(instance)


class VoteCreateSerializer(serializers.Serializer):
    """سریالایزر برای ثبت رای جدید"""
    voting_poll_id = serializers.IntegerField()
    option_id = serializers.IntegerField()
    
    def validate(self, data):
        voting_poll_id = data.get('voting_poll_id')
        option_id = data.get('option_id')
        
        try:
            voting_poll = VotingPoll.objects.get(id=voting_poll_id, is_active=True)
        except VotingPoll.DoesNotExist:
            raise serializers.ValidationError({
                "voting_poll_id": "Voting poll not found or not active."
            })
            
        try:
            option = VotingOption.objects.get(id=option_id, voting_poll_id=voting_poll_id)
        except VotingOption.DoesNotExist:
            raise serializers.ValidationError({
                "option_id": "Option not found or does not belong to the specified voting poll."
            })
            
        # بررسی اینکه آیا مهلت رای‌گیری به پایان رسیده است
        if voting_poll.is_expired:
            raise serializers.ValidationError({
                "voting_poll_id": "The voting deadline has passed."
            })
            
        data['voting_poll'] = voting_poll
        data['option'] = option
        
        return data


class VoterSerializer(serializers.ModelSerializer):
    """سریالایزر برای نمایش اطلاعات رای دهنده"""
    fullname = serializers.CharField(source='user.fullname')
    
    class Meta:
        model = DepositMembership
        fields = ['id', 'fullname', 'role']


class VotingOptionResultSerializer(serializers.ModelSerializer):
    """سریالایزر برای نمایش نتایج یک گزینه رای‌گیری"""
    votes_count = serializers.IntegerField()
    percentage = serializers.FloatField()
    voters = serializers.SerializerMethodField()
    
    class Meta:
        model = VotingOption
        fields = ['id', 'title', 'description', 'color_name', 'votes_count', 'percentage', 'voters']
    
    def get_voters(self, obj):
        """
        لیست رای دهندگان به این گزینه
        """
        votes = obj.votes.all().prefetch_related('membership', 'membership__user')
        memberships = [vote.membership for vote in votes]
        
        return VoterSerializer(memberships, many=True).data


class VotingPollResultSerializer(serializers.ModelSerializer):
    """سریالایزر برای نمایش نتایج کلی یک رای‌گیری"""
    total_votes = serializers.IntegerField()
    options = VotingOptionResultSerializer(many=True)
    
    class Meta:
        model = VotingPoll
        fields = ['id', 'title', 'description', 'total_votes', 'options'] 
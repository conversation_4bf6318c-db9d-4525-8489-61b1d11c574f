from rest_framework import serializers
from apps.voting.models import VotingReport, VotingReportImage
from filer.models import Image as FilerImage
import os
import uuid
from django.conf import settings
# from dj_filer.admin import get_thumbs
from utils import get_thumbs
from utils import FileFieldSerializer, absolute_url

from utils.save_to_filer import import_file


class ReportImageSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = VotingReportImage
        fields = ['id', 'image_url', 'priority']

    def get_image_url(self, obj):
        return get_thumbs(obj.image, self.context.get('request'))


class VotingReportListSerializer(serializers.ModelSerializer):
    class Meta:
        model = VotingReport
        fields = ['id', 'subject', 'description', 'amount',  'created_at', 'updated_at']


class VotingReportDetailSerializer(serializers.ModelSerializer):
    images = serializers.SerializerMethodField()

    class Meta:
        model = VotingReport
        fields = ['id', 'subject', 'description', 'amount', 'created_at', 'updated_at', 'images']

    def get_images(self, obj):
        images = obj.images.all()  # Access related images
        return ReportImageSerializer(images, many=True, context=self.context).data


class VotingReportImageSerializer(serializers.ModelSerializer):
    image = FileFieldSerializer(required=False)
    priority = serializers.IntegerField(required=False, default=0)

    class Meta:
        model = VotingReportImage
        exclude = ['report']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.image:
            request = self.context.get('request')
            representation['image'] = request.build_absolute_uri(instance.image.url) if request else instance.image.url
        else:
            representation['image'] = None
        return representation



class VotingReportSerializer(serializers.ModelSerializer):
    images = VotingReportImageSerializer(many=True, required=False)

    class Meta:
        model = VotingReport
        exclude = ['created_at', 'updated_at', 'deposit']

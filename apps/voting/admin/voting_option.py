from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.templatetags.static import static
from django.utils.html import format_html
from django.db.models import Count

from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)
from unfold.decorators import display, action

from apps.voting.models import VotingOption
from utils.admin import project_admin_site


class VotingOptionAdmin(ModelAdmin):
    """Admin for VotingOption model"""
    list_display = (
        'option_info', 'color_display_unfold', 'option_statistics'
    )
    list_filter = (
        'voting_poll__title', 
        'voting_poll__deposit'
    )
    ordering = ('voting_poll', 'title')
    readonly_fields = ('votes_count', 'votes_percentage')
    search_fields = (
        'title', 'description', 'voting_poll__title'
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    autocomplete_fields = ('voting_poll',)
    
    fieldsets = (
        (None, {"fields": (("title", "voting_poll"),)}),
        (
            _("Basic Information"),
            {
                "fields": ("description", "color_name"),
                "classes": ["tab"],
            },
        ),
        (
            _('Statistics'), {
                'fields': ('votes_count', 'votes_percentage'),
                "classes": ["tab"],
            }
        ),
    )
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('votes', 'voting_poll__votes')
        return queryset
    
    @display(description='Option', header=True)
    def option_info(self, obj):
        # Get the appropriate icon
        icon_path = static("images/option.svg") or None
        
        return [
            obj.title,
            obj.voting_poll.title if obj.voting_poll else _('No Poll'),
            None,
            {
                "path": icon_path,
                "height": 33,
                "width": 38,
                "borderless": True,
                "squared": True,
            },  
        ]
    
    @display(description='Color')
    def color_display_unfold(self, obj):
        if obj.color_name:
            return format_html(
                '<span style="background-color:{}; padding:5px 10px; border-radius:3px; display:inline-block; width:80px; text-align:center;">{}</span>',
                obj.color_name,
                obj.color_name
            )
        return "-"
    
    @display(description='Statistics')
    def option_statistics(self, obj):
        option_votes = obj.votes.count()
        total_votes = obj.voting_poll.votes.count() if obj.voting_poll else 0
        
        if total_votes > 0:
            percentage = (option_votes / total_votes) * 100
            return format_html(
                '<div style="width:150px; background-color:#f1f1f1; height:20px; border-radius:3px; overflow:hidden;"><div style="width:{}%; background-color:{}; height:20px;"></div></div> {:.1f}% ({} {})',
                percentage,
                obj.color_name or '#3498db',
                percentage,
                option_votes,
                _('votes')
            )
        return f"0% (0 {_('votes')})"
    
    def color_display(self, obj):
        if obj.color_name:
            return format_html(
                '<span style="background-color:{}; padding:5px 10px; border-radius:3px;">{}</span>',
                obj.color_name,
                obj.color_name
            )
        return "-"
    color_display.short_description = _('Color')
    
    def votes_percentage(self, obj):
        total_votes = obj.voting_poll.votes.count() if obj.voting_poll else 0
        if total_votes > 0:
            option_votes = obj.votes.count()
            percentage = (option_votes / total_votes) * 100
            return format_html(
                '<div style="width:100%; max-width:300px; background-color:#f1f1f1; height:20px; border-radius:3px; overflow:hidden;"><div style="width:{}%; background-color:{}; height:20px;"></div></div> {:.1f}% ({} {})',
                percentage,
                obj.color_name or '#3498db',
                percentage,
                option_votes,
                _('votes')
            )
        return f"0% (0 {_('votes')})"
    votes_percentage.short_description = _('Votes Percentage')
    
    def votes_count(self, obj):
        return obj.votes.count()
    votes_count.short_description = _('Votes Count')
from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.templatetags.static import static
from django.utils.html import format_html

from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import (
    RangeN<PERSON>ric<PERSON>ilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)
from unfold.decorators import display, action

from apps.voting.models import Vote
from utils.admin import project_admin_site


class VoteAdmin(ModelAdmin):
    """Admin for Vote model"""
    list_display = (
        'voter_info', 'vote_details', 'jalali_created'
    )
    list_filter = (
        'voting_poll__title', 
        'option__title', 
        ('created_at', RangeDateFilter),
        'voting_poll__deposit'
    )
    ordering = ('-created_at',)
    readonly_fields = ('created_at',)
    search_fields = (
        'membership__user__fullname', 'voting_poll__title', 'option__title'
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    autocomplete_fields = ('voting_poll', 'option', )
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (None, {"fields": (("voting_poll", "option"),)}),
        (
            _("Vote Information"),
            {
                "fields": ("membership",),
                "classes": ["tab"],
            },
        ),
        (
            _('Timing'), {
                'fields': ('created_at',),
                "classes": ["tab"],
            }
        ),
    )
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('membership__user', 'voting_poll', 'option')
        return queryset
    
    @display(description='Voter', header=True)
    def voter_info(self, obj):
        # Get the user's avatar URL or use a default image
        avatar_path = obj.membership.user.avatar.url if hasattr(obj.membership.user, 'avatar') and obj.membership.user.avatar else static("images/user1.svg")
        
        return [
            obj.membership.user.fullname if obj.membership and obj.membership.user else _('No User'),
            obj.membership.user.phone_number if obj.membership and obj.membership.user else '',
            None,
            {
                "path": avatar_path,
                "height": 33,
                "width": 38,
                "borderless": True,
                "squared": True,
            },  
        ]
    
    @display(description='Vote Details')
    def vote_details(self, obj):
        poll_title = obj.voting_poll.title if obj.voting_poll else _('No Poll')
        option_title = obj.option.title if obj.option else _('No Option')
        
        # Add color indicator if option has a color
        if obj.option and obj.option.color_name:
            return format_html(
                '{} → <span style="background-color:{}; padding:2px 8px; border-radius:3px; display:inline-block;">{}</span>',
                poll_title,
                obj.option.color_name,
                option_title
            )
        
        return f"{poll_title} → {option_title}"
    
    @display(description=_('Created At'))
    def jalali_created(self, obj):
        from utils.date_utils import format_jalali_date
        if obj.created_at:
            return format_jalali_date(obj.created_at, "%Y/%m/%d %H:%M")
        return "-"
    
    def get_user_fullname(self, obj):
        return obj.membership.user.fullname if obj.membership and obj.membership.user else _('No User')
    get_user_fullname.short_description = _('Voter')
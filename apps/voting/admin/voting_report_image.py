from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.templatetags.static import static
from django.utils.html import format_html

from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)
from unfold.decorators import display, action

from apps.voting.models import VotingReportImage
from utils.admin import project_admin_site


class VotingReportImageAdmin(ModelAdmin):
    """Admin for VotingReportImage model"""
    list_display = (
        'image_info', 'image_preview_unfold', 'priority_display'
    )
    list_filter = (
        'report__deposit',
        ('priority', RangeNumericFilter),
    )
    ordering = ('report', 'priority')
    search_fields = (
        'report__subject',
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    # raw_id_fields = ('image',)
    autocomplete_fields = ('report',)
    
    fieldsets = (
        (None, {"fields": (("report", "priority"),)}),
        (
            _("Image"),
            {
                "fields": ("image",),
                "classes": ["tab"],
            },
        ),
    )
    
    @display(description='Report', header=True)
    def image_info(self, obj):
        # Get the appropriate icon
        icon_path = static("images/image.svg") or None
        
        return [
            f"Image #{obj.id}",
            obj.report.subject if obj.report else _('No Report'),
            None,
            {
                "path": icon_path,
                "height": 33,
                "width": 38,
                "borderless": True,
                "squared": True,
            },  
        ]
    
    @display(description='Image')
    def image_preview_unfold(self, obj):
        if obj.image:
            return format_html('<img src="{}" style="max-width:150px; max-height:100px; border-radius:5px;" />', obj.image.url)
        return "-"
    
    @display(description='Priority')
    def priority_display(self, obj):
        return format_html(
            '<span style="background-color:#f1f1f1; padding:3px 10px; border-radius:10px;">{}</span>',
            obj.priority
        )
    
    def report_subject(self, obj):
        return obj.report.subject if obj.report else _('No Report')
    report_subject.short_description = _('Report Subject')
    
    def image_preview(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="100" />', obj.image.url)
        return "-"
    image_preview.short_description = _('Image Preview')
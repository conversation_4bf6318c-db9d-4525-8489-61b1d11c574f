from apps.voting.admin.voting_poll import VotingPollAdmin, VotingOptionInline
from apps.voting.admin.voting_option import VotingOptionAdmin
from apps.voting.admin.vote import VoteAdmin
from apps.voting.admin.voting_report import VotingReportAdmin, VotingReportImageInline
from apps.voting.admin.voting_report_image import VotingReportImageAdmin

from apps.voting.models import VotingPoll, VotingOption, Vote, VotingReport, VotingReportImage
from utils.admin import project_admin_site
from django.contrib import admin

# Register with project admin site
project_admin_site.register(VotingPoll, VotingPollAdmin)
project_admin_site.register(VotingOption, VotingOptionAdmin)
project_admin_site.register(Vote, VoteAdmin)
project_admin_site.register(VotingReport, VotingReportAdmin)
project_admin_site.register(VotingReportImage, VotingReportImageAdmin)

# Register with default admin site
admin.site.register(VotingPoll, VotingPollAdmin)
admin.site.register(VotingOption, VotingOptionAdmin)
admin.site.register(Vote, VoteAdmin)
admin.site.register(VotingReport, VotingReportAdmin)
admin.site.register(VotingReportImage, VotingReportImageAdmin)
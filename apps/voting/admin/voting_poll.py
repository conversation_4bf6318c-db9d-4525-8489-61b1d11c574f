from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.templatetags.static import static
from django.utils.html import format_html
from django.db.models import Count

from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)
from unfold.decorators import display, action

from apps.voting.models import VotingPoll, VotingOption
from utils.admin import project_admin_site


class VotingOptionInline(TabularInline):
    model = VotingOption
    extra = 1
    min_num = 2
    verbose_name = _("Voting Option")
    verbose_name_plural = _("Voting Options")
    fields = ('title', 'description', 'color_name')
    hide_title = True  # Unfold specific option to hide title row


class VotingPollAdmin(ModelAdmin):
    """Admin for VotingPoll model"""
    list_display = (
        'poll_info', 'poll_status', 'poll_timing', 'poll_statistics'
    )
    list_filter = (
        ('is_active', BooleanRadioFilter),
        ('created_at', RangeDateFilter),
        'deposit',
    )
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'end_date', 'is_expired', 'options_count', 'votes_count')
    search_fields = (
        'title', 'description', 'deposit__title'
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    # autocomplete_fields = ('deposit',)
    inlines = [VotingOptionInline]
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (None, {"fields": (("title", "deposit"),)}),
        (
            _("Basic Information"),
            {
                "fields": ("description", "is_active"),
                "classes": ["tab"],
            },
        ),
        (
            _('Timing'), {
                'fields': ('deadline_days', 'created_at', 'end_date'),
                "classes": ["tab"],
            }
        ),
        (
            _('Statistics'), {
                'fields': ('options_count', 'votes_count', 'is_expired'),
                "classes": ["tab"],
            }
        ),
    )
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('options', 'votes')
        return queryset
    
    @display(description='Poll', header=True)
    def poll_info(self, obj):
        # Get the appropriate icon
        icon_path = static("images/poll.svg") or None
        
        return [
            obj.title,
            obj.deposit.title if obj.deposit else _('No Deposit'),
            None,
            {
                "path": icon_path,
                "height": 33,
                "width": 38,
                "borderless": True,
                "squared": True,
            },  
        ]
    
    @display(description='Status', label={
        True: 'success',   # Active polls in green
        False: 'danger',   # Inactive polls in red
    })
    def poll_status(self, obj):
        if obj.is_expired:
            return False, _('Expired')
        return obj.is_active, _('Active') if obj.is_active else _('Inactive')
    
    @display(description='Timing')
    def poll_timing(self, obj):
        from utils.date_utils import format_jalali_date
        created_at = format_jalali_date(obj.created_at, "%Y/%m/%d") if obj.created_at else "-"
        end_date = format_jalali_date(obj.end_date, "%Y/%m/%d") if obj.end_date else "-"
        
        return f"{created_at} → {end_date} ({obj.deadline_days} {_('days')})"
    
    @display(description='Statistics')
    def poll_statistics(self, obj):
        options_count = obj.options.count()
        votes_count = obj.votes.count()
        
        return f"{options_count} {_('options')} | {votes_count} {_('votes')}"
    
    def end_date_display(self, obj):
        return obj.end_date
    end_date_display.short_description = _('End Date')
    
    def options_count(self, obj):
        return obj.options.count()
    options_count.short_description = _('Options Count')
    
    def votes_count(self, obj):
        return obj.votes.count()
    votes_count.short_description = _('Votes Count')
from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.templatetags.static import static
from django.utils.html import format_html

from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)
from unfold.decorators import display, action

from apps.voting.models import VotingReport, VotingReportImage
from utils.admin import project_admin_site


class VotingReportImageInline(TabularInline):
    model = VotingReportImage
    extra = 1
    fields = ('image', 'priority')
    sortable_field_name = 'priority'
    hide_title = True  # Unfold specific option to hide title row


class VotingReportAdmin(ModelAdmin):
    """Admin for VotingReport model"""
    list_display = (
        'report_info', 'report_amount', 'report_images', 'report_dates'
    )
    list_filter = (
        ('created_at', RangeDateFilter),
        ('updated_at', RangeDateFilter),
        ('amount', RangeNumericFilter),
        'deposit',
    )
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at', 'image_count')
    search_fields = (
        'subject', 'deposit__title', 'description'
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    # autocomplete_fields = ('deposit',)
    inlines = [VotingReportImageInline]
    # date_hierarchy = 'created_at'
    
    fieldsets = (
        (None, {"fields": (("subject", "deposit"),)}),
        (
            _("Report Information"),
            {
                "fields": ("description", "amount"),
                "classes": ["tab"],
            },
        ),
        (
            _('Statistics'), {
                'fields': ('image_count',),
                "classes": ["tab"],
            }
        ),
        (
            _('Timestamps'), {
                'fields': ('created_at', 'updated_at'),
                "classes": ["tab"],
            }
        ),
    )
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('images')
        return queryset
    
    @display(description='Report', header=True)
    def report_info(self, obj):        
        return [
            obj.subject,
            obj.deposit.title if obj.deposit else _('No Deposit'),
            None,
        ]
    
    @display(description='Amount')
    def report_amount(self, obj):
        formatted_amount = f"{obj.amount:,}" if obj.amount else "0"
        return f"{formatted_amount} {_('Toman')}"
    
    @display(description='Images')
    def report_images(self, obj):
        count = obj.images.count()
        return format_html('{} {}', count, _('images'))
    
    @display(description='Dates')
    def report_dates(self, obj):
        from utils.date_utils import format_jalali_date
        created_at = format_jalali_date(obj.created_at, "%Y/%m/%d") if obj.created_at else "-"
        updated_at = format_jalali_date(obj.updated_at, "%Y/%m/%d") if obj.updated_at else "-"
        
        if created_at == updated_at:
            return created_at
        return f"{created_at} → {updated_at}"
    
    def image_count(self, obj):
        count = obj.images.count()
        return format_html('{} {}', count, _('images'))
    image_count.short_description = _('Number of Images')
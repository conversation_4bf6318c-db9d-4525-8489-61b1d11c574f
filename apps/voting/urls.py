from django.urls import path
from . import views

urlpatterns = [
    # مسیرهای API
    path('deposit/<int:deposit_id>/votings/list/', views.DepositVotingListView.as_view(), name='deposit-votings-list'),
    path('votings/create/', views.VotingPollCreateView.as_view(), name='voting-create'),
    path('votings/<int:pk>/', views.VotingPollDetailView.as_view(), name='voting-detail'),
    path('votings/<int:pk>/delete/', views.VotingPollDeleteView.as_view(), name='voting-delete'),
    path('votings/join/', views.VoteCreateView.as_view(), name='voting-join'),
    path('votings/<int:pk>/reporting/', views.VotingPollReportView.as_view(), name='voting-reporting'),
    
    # New reporting API endpoints
    path('deposit/<int:deposit_id>/reports/', views.VotingReportListAPIView.as_view(), name='deposit-reports-list'),
    path('reports/<int:report_id>/', views.VotingReportDetailAPIView.as_view(), name='report-detail'),
    path('deposit/<int:deposit_id>/reports/create/', views.VotingReportCreateAPIView.as_view(), name='report-create'),
    # path('reports/<int:report_id>/update/', views.VotingReportUpdateAPIView.as_view(), name='report-update'),
    path('reports/<int:report_id>/delete/', views.VotingReportDeleteAPIView.as_view(), name='report-delete'),
] 
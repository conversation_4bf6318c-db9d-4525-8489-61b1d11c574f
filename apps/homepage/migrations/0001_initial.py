# Generated by Django 5.2.1 on 2025-06-09 16:01

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AppVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version', models.CharField(help_text='نسخه اپلیکیشن به فرمت X.Y.Z (مثال: 1.0.0)', max_length=20, unique=True, validators=[django.core.validators.RegexValidator(message='Version must be in format X.Y.Z (e.g., 1.0.0)', regex='^\\d+\\.\\d+\\.\\d+$')], verbose_name='نسخه')),
                ('apk_file', models.FileField(help_text='فایل APK اپلیکیشن', upload_to='app_versions/', verbose_name='فایل APK')),
                ('description', models.TextField(blank=True, help_text='توضیحات و تغییرات این نسخه', verbose_name='توضیحات')),
                ('is_active', models.BooleanField(default=True, help_text='آیا این نسخه فعال است؟', verbose_name='فعال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ ایجاد')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاریخ بروزرسانی')),
            ],
            options={
                'verbose_name': 'نسخه اپلیکیشن',
                'verbose_name_plural': 'نسخه\u200cهای اپلیکیشن',
                'ordering': ['-created_at'],
            },
        ),
    ]

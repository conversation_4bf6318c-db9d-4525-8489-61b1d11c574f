from django.db import models
from django.core.validators import RegexValidator


class AppVersion(models.Model):
    """
    Model for storing app versions with APK files and descriptions
    """
    version = models.CharField(
        max_length=20,
        unique=True,
        validators=[
            RegexValidator(
                regex=r'^\d+\.\d+\.\d+$',
                message='Version must be in format X.Y.Z (e.g., 1.0.0)'
            )
        ],
        verbose_name='نسخه',
        help_text='نسخه اپلیکیشن به فرمت X.Y.Z (مثال: 1.0.0)'
    )

    apk_file = models.FileField(
        upload_to='app_versions/',
        verbose_name='فایل APK',
        help_text='فایل APK اپلیکیشن'
    )

    description = models.TextField(
        verbose_name='توضیحات',
        help_text='توضیحات و تغییرات این نسخه',
        blank=True
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name='فعال',
        help_text='آیا این نسخه فعال است؟'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاریخ ایجاد'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاریخ بروزرسانی'
    )

    class Meta:
        verbose_name = 'نسخه اپلیکیشن'
        verbose_name_plural = 'نسخه‌های اپلیکیشن'
        ordering = ['-created_at']

    def __str__(self):
        return f'نسخه {self.version}'

    @classmethod
    def get_latest_active(cls):
        """
        Get the latest active version
        """
        return cls.objects.filter(is_active=True).first()

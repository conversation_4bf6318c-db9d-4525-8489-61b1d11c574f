from django.urls import path
from .views import CreatePaymentView, GetPaymentInfoView, ZarinpalVerifyView, PaymentRedirectView, PaymentStatusView

urlpatterns = [
    # Payment API endpoints
    path('payments/create/', CreatePaymentView.as_view(), name='create-payment'),
    path('payments/info/<int:deposit_id>/', GetPaymentInfoView.as_view(), name='payment-info'),
    path('payments/redirect/', PaymentRedirectView.as_view(), name='payment-redirect'),
    path('payments/status/<int:payment_id>/', PaymentStatusView.as_view(), name='payment-status'),
    path('payments/verify/', ZarinpalVerifyView.as_view(), name='zarinpal-verify'),
]
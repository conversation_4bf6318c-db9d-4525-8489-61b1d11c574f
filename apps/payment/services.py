from decimal import Decimal
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from apps.deposit.models import Deposit, DepositMembership
from apps.loan.models import Loan, LoanInstallment
from apps.transaction.models import Transaction


class PaymentCalculationService:
    """
    Service class for payment-related calculations.
    
    This class handles all calculations related to payments, including:
    - Payment amount based on deposit type
    - Loan installment amount
    - Current due date retrieval
    
    Following the Single Responsibility Principle, this class is solely responsible
    for payment calculations and does not handle API requests or responses.
    """
    
    @staticmethod
    def calculate_payment_amount(deposit, membership):
        """
        Calculate payment amount based on deposit type.

        Args:
            deposit: The Deposit object
            membership: The DepositMembership object for the user

        Returns:
            Decimal: The calculated payment amount (always at least 0.01)

        Raises:
            ValueError: If deposit configuration is invalid
        """
        # بررسی اولیه unit_amount
        if deposit.unit_amount is None:
            raise ValueError(
                _("صندوق '%(deposit_title)s' مبلغ واحد سهم (unit_amount) ندارد. "
                  "لطفاً با مدیر سیستم تماس بگیرید.") % {'deposit_title': deposit.title}
            )

        if deposit.unit_amount <= 0:
            raise ValueError(
                _("مبلغ واحد سهم صندوق '%(deposit_title)s' نامعتبر است (%(unit_amount)s). "
                  "مبلغ باید بیشتر از صفر باشد.") % {
                    'deposit_title': deposit.title,
                    'unit_amount': deposit.unit_amount
                }
            )

        # بررسی‌های خاص هر نوع صندوق
        if deposit.deposit_type == Deposit.DepositType.POLL:
            if not deposit.total_debt_amount:
                raise ValueError(
                    _("صندوق قرعه‌کشی '%(deposit_title)s' مبلغ کل (total_debt_amount) ندارد.") % {
                        'deposit_title': deposit.title
                    }
                )
            if not deposit.lottery_month_count:
                raise ValueError(
                    _("صندوق قرعه‌کشی '%(deposit_title)s' تعداد ماه قرعه‌کشی (lottery_month_count) ندارد.") % {
                        'deposit_title': deposit.title
                    }
                )

        elif deposit.deposit_type == Deposit.DepositType.SAVING:
            if not deposit.validity_duration:
                raise ValueError(
                    _("صندوق پس‌انداز '%(deposit_title)s' مدت اعتبار (validity_duration) ندارد.") % {
                        'deposit_title': deposit.title
                    }
                )

        # محاسبه مبلغ پرداخت
        if deposit.deposit_type in [Deposit.DepositType.POLL, Deposit.DepositType.SAVING]:
            print(f'--->deposit.unit_amount: {deposit.unit_amount}//membership.requested_unit_count: {membership.requested_unit_count}')
            # Calculate payment amount based on unit_amount and requested_unit_count
            # در حال حاضر فقط 1 سهم در نظر گرفته می‌شود
            amount = deposit.unit_amount
            return max(amount, Decimal('0.01'))

        elif deposit.deposit_type == Deposit.DepositType.REPORTING:
            # For Reporting type, use the same logic as Poll/Saving
            amount = deposit.unit_amount
            return max(amount, Decimal('0.01'))

        return Decimal('0.01')  # Return minimum valid amount instead of 0.00
    
    @staticmethod
    def get_loan_installment_amount(deposit, membership):
        """
        Get loan installment amount if user has an active loan.
        
        Args:
            deposit: The Deposit object
            membership: The DepositMembership object for the user
            
        Returns:
            tuple: (amount, has_loan_installment) where amount is a Decimal
                  and has_loan_installment is a boolean
        """
        # Find active loans for this user in this deposit
        loans = Loan.objects.filter(
            deposit=deposit,
            deposit_membership=membership,
            status=Loan.LoanStatus.ACTIVE
        )
        
        if not loans.exists():
            return Decimal('0.00'), False
        
        # For each loan, find the first unpaid installment
        for loan in loans:
            next_installment = LoanInstallment.objects.filter(
                loan=loan,
                is_paid=False
            ).order_by('due_date').first()
            
            if next_installment:
                return next_installment.amount, True
        
        return Decimal('0.00'), False
    
    @staticmethod
    def get_current_due_date(deposit):
        """
        Get the current due date for the deposit.
        
        Args:
            deposit: The Deposit object
            
        Returns:
            date or None: The due date if available, None otherwise
        """
        current_due_date = deposit.get_current_due_date()
        if current_due_date:
            return current_due_date.due_date
        return None
    
    @classmethod
    def get_payment_info(cls, deposit, membership):
        """
        Get all payment information for a deposit and membership.

        This method combines all calculations to provide a complete
        payment information package.

        Args:
            deposit: The Deposit object
            membership: The DepositMembership object for the user

        Returns:
            dict: A dictionary containing all payment information

        Raises:
            ValueError: If deposit or membership configuration is invalid
        """
        try:
            # Calculate payment amount (این method خودش validation انجام می‌دهد)
            payment_amount = cls.calculate_payment_amount(deposit, membership)

            # Get loan installment amount
            loan_installment_amount, has_loan_installment = cls.get_loan_installment_amount(deposit, membership)
            print(f'--get_payment_info-->payment_amount: {payment_amount}/ loan_installment_amount: {loan_installment_amount}')

            # بررسی اضافی برای loan_installment_amount
            if loan_installment_amount is None:
                loan_installment_amount = Decimal('0.00')
                has_loan_installment = False

            # Calculate total amount and ensure it's at least 0.01
            total_amount = payment_amount + loan_installment_amount
            total_amount = max(total_amount, Decimal('0.01'))

        except Exception as e:
            # اگر خطایی در محاسبه رخ داد، خطای واضح‌تری ارائه دهیم
            raise ValueError(
                _("خطا در محاسبه اطلاعات پرداخت برای صندوق '%(deposit_title)s': %(error)s") % {
                    'deposit_title': deposit.title,
                    'error': str(e)
                }
            )
        
        # Get current due date
        current_due_date = cls.get_current_due_date(deposit)
        
        return {
            'payment_amount': payment_amount,
            'has_loan_installment': has_loan_installment,
            'loan_installment_amount': loan_installment_amount,
            'total_amount': total_amount,
            'current_due_date': current_due_date
        }


class PaymentTransactionService:
    """
    Service class for handling payment-related transactions.
    
    This class is responsible for creating or updating transactions
    when a payment is verified.
    """
    
    @staticmethod
    def create_or_update_transaction(payment):
        """
        Create a new transaction or update an existing one when a payment is verified.
        
        Args:
            payment: The verified Payment object
            
        Returns:
            Transaction: The created or updated Transaction object
        """
        # Check if payment already has a transaction
        if payment.transaction:
            # Update existing transaction
            transaction = payment.transaction
            transaction.status = Transaction.TransactionStatus.SUCCESS
            transaction.amount = payment.total_amount
            transaction.save()
            return transaction
        
        # Get current due date if available
        current_due_date = payment.deposit.get_current_due_date()
        
        # Create new transaction
        transaction = Transaction.objects.create(
            deposit=payment.deposit,
            user=payment.user,
            due_date=current_due_date,
            status=Transaction.TransactionStatus.SUCCESS,
            amount=payment.total_amount,
            transaction_type=Transaction.TransactionType.INCOME,
            description=f"پرداخت آنلاین - شماره پیگیری: {payment.reference_number}"
        )
        
        # Update payment with the new transaction
        payment.transaction = transaction
        payment.save(update_fields=['transaction'])
        
        return transaction
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status

# ============================================================================
# STRUCTURED SWAGGER DOCUMENTATION FOR PAYMENT APP
# ============================================================================

# Get Payment Info Swagger Documentation
get_payment_info_swagger = swagger_auto_schema(
    operation_description="Get payment information for a specific deposit including payment gateways, amounts, and loan installment details",
    operation_summary="Get Payment Information",
    tags=['Payment'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Payment information retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'deposit_id': openapi.Schema(type=openapi.TYPE_INTEGER, example=123),
                    'deposit_name': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق خانوادگی'),
                    'payment_gateways': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_STRING, example='zarinpal'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, example='زرین‌پال'),
                                'icon': openapi.Schema(type=openapi.TYPE_STRING, example='https://example.com/icon.png')
                            }
                        )
                    ),
                    'payment_amount': openapi.Schema(type=openapi.TYPE_NUMBER, example=100000.0),
                    'has_loan_installment': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                    'loan_installment_amount': openapi.Schema(type=openapi.TYPE_NUMBER, example=50000.0),
                    'total_amount': openapi.Schema(type=openapi.TYPE_NUMBER, example=150000.0),
                    'current_due_date': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE, example='2023-06-15'),
                    'payment_url': openapi.Schema(type=openapi.TYPE_STRING, example='https://www.zarinpal.com/pg/StartPay/123456'),
                    'authority': openapi.Schema(type=openapi.TYPE_STRING, example='A00000000000000000000000000123456'),
                    'payment_id': openapi.Schema(type=openapi.TYPE_INTEGER, example=456)
                }
            )
        ),
        status.HTTP_403_FORBIDDEN: openapi.Response(
            description="User is not an active member of this deposit",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='You are not an active member of this deposit'
                    )
                }
            )
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Deposit not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Deposit not found'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

def doc_payment_info():
    return """
# 🐈 سناریو
🛠️ دریافت اطلاعات پرداخت برای یک صندوق

این API برای دریافت اطلاعات پرداخت برای یک صندوق خاص استفاده می‌شود. کاربر باید عضو فعال صندوق باشد.

---

## 📌 توضیحات فیلدهای پاسخ

1. **deposit_id** (شناسه صندوق):  
   - شناسه یکتای صندوق - مثال: `123`

2. **deposit_name** (نام صندوق):  
   - عنوان صندوق - مثال: `صندوق خانوادگی`

3. **payment_gateways** (درگاه‌های پرداخت):  
   - لیست درگاه‌های پرداخت در دسترس
   - هر درگاه شامل:
     - `id`: شناسه درگاه
     - `name`: نام درگاه
     - `icon`: آدرس آیکون درگاه

4. **payment_amount** (مبلغ پرداخت):  
   - مبلغ پرداخت بدون احتساب قسط وام (به ریال) - مثال: `100000.0`

5. **has_loan_installment** (دارای قسط وام):  
   - آیا این پرداخت شامل قسط وام نیز می‌شود؟ - مقادیر: `true/false`

6. **loan_installment_amount** (مبلغ قسط وام):  
   - مبلغ قسط وام (به ریال) - مثال: `50000.0`

7. **total_amount** (مبلغ کل):  
   - مجموع مبلغ پرداخت و قسط وام (به ریال) - مثال: `150000.0`

8. **current_due_date** (تاریخ سررسید فعلی):  
   - تاریخ سررسید فعلی صندوق - مثال: `2023-06-15`

9. **payment_url** (آدرس پرداخت):  
   - آدرس درگاه پرداخت - مثال: `https://www.zarinpal.com/pg/StartPay/A00000000000000000000000000123456`

10. **authority** (کد اختصاصی):  
    - کد اختصاصی تراکنش از درگاه پرداخت - مثال: `A00000000000000000000000000123456`

11. **payment_id** (شناسه پرداخت):  
    - شناسه یکتای پرداخت در سیستم - مثال: `456`

---

## 🚀 درخواست API

### نمونه درخواست:
```http
GET /api/payments/info/123/
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### نمونه پاسخ:
```json
{
  "deposit_id": 123,
  "deposit_name": "صندوق خانوادگی",
  "payment_gateways": [
    {
      "id": "zarinpal",
      "name": "زرین‌پال",
      "icon": "https://www.zarinpal.com/blog/wp-content/uploads/2021/06/%D9%86%D9%85%D8%A7%D8%AF-%D8%A7%D8%B7%D9%85%DB%8C%D9%86%D8%A7%D9%86-211x300.png"
    },
    {
      "id": "mellat",
      "name": "به‌پرداخت ملت",
      "icon": "/static/media/payment/mellat.png"
    }
  ],
  "payment_amount": 100000.0,
  "has_loan_installment": true,
  "loan_installment_amount": 50000.0,
  "total_amount": 150000.0,
  "current_due_date": "2023-06-15",
  "payment_url": "https://www.zarinpal.com/pg/StartPay/A00000000000000000000000000123456",
  "authority": "A00000000000000000000000000123456",
  "payment_id": 456
}
```

## ⚠️ خطاهای احتمالی

### صندوق یافت نشد (404 Not Found)
```json
{
    "message": "Deposit not found"
}
```

### کاربر عضو فعال صندوق نیست (403 Forbidden)
```json
{
    "message": "You are not an active member of this deposit"
}
```
"""

def doc_payment_create():
    return """
# 🐈 سناریو
🛠️ ایجاد پرداخت جدید

این API برای ایجاد یک پرداخت جدید استفاده می‌شود. کاربر باید عضو فعال صندوق باشد.

---

## 📌 توضیحات فیلدهای درخواست

1. **deposit** (شناسه صندوق) - اجباری:  
   - شناسه یکتای صندوق - مثال: `123`

2. **gateway_name** (نام درگاه) - اجباری:  
   - نام درگاه پرداخت - مثال: `zarinpal`

3. **security_token** (توکن امنیتی) - اجباری:  
   - توکن امنیتی برای تایید درخواست - مثال: `4BB74D54631B2AEDD915C4181F451`

---

## 🚀 درخواست API

### نمونه درخواست:
```http
POST /api/payments/create/
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "deposit": 123,
  "gateway_name": "zarinpal",
  "security_token": "4BB74D54631B2AEDD915C4181F451"
}
```

### نمونه پاسخ:
```json
{
  "id": 456,
  "deposit": 123,
  "gateway_name": "zarinpal",
  "amount": 100000.0,
  "status": "created",
  "includes_loan_installment": true,
  "loan_installment_amount": 50000.0,
  "total_amount": 150000.0,
  "reference_number": null,
  "authority": null,
  "payment_url": null,
  "payment_date": null,
  "redirected_at": null,
  "created_at": "2023-06-15T14:28:15",
  "updated_at": "2023-06-15T14:28:15"
}
```

## ⚠️ خطاهای احتمالی

### توکن امنیتی نامعتبر (403 Forbidden)
```json
{
    "message": "Invalid or missing security token"
}
```

### صندوق یافت نشد (404 Not Found)
```json
{
    "message": "Deposit not found"
}
```

### کاربر عضو فعال صندوق نیست (403 Forbidden)
```json
{
    "message": "You are not an active member of this deposit"
}
```

### شناسه صندوق ارسال نشده (400 Bad Request)
```json
{
    "message": "Deposit ID is required"
}
```
"""

def doc_payment_redirect():
    return """
# 🐈 سناریو
🛠️ ثبت هدایت کاربر به درگاه پرداخت

این API زمانی فراخوانی می‌شود که کاربر روی دکمه پرداخت کلیک می‌کند و قرار است به درگاه پرداخت هدایت شود. با فراخوانی این API، وضعیت پرداخت به REDIRECTED تغییر می‌کند و زمان هدایت کاربر به درگاه پرداخت ثبت می‌شود. این API در ورودی نام گت‌وی و آیدی پرداخت را می‌گیرد و لینک پرداخت را می‌سازد و در پاسخ می‌فرستد.

---

## 📌 توضیحات فیلدهای درخواست

1. **payment_id** (شناسه پرداخت) - اجباری:  
   - شناسه یکتای پرداخت در سیستم - مثال: `456`

2. **gateway_name** (نام درگاه) - اجباری:  
   - نام درگاه پرداخت - مثال: `zarinpal`

---

## 🚀 درخواست API

### نمونه درخواست:
```http
POST /api/payments/redirect/
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "payment_id": 456,
  "gateway_name": "zarinpal"
}
```

### نمونه پاسخ:
```json
{
  "message": "Payment marked as redirected successfully",
  "payment_url": "https://www.zarinpal.com/pg/StartPay/A00000000000000000000000000123456",
  "gateway_name": "zarinpal"
}
```

## ⚠️ خطاهای احتمالی

### پرداخت یافت نشد (404 Not Found)
```json
{
    "message": "Payment not found or does not belong to you"
}
```

### فیلدهای اجباری ارسال نشده (400 Bad Request)
```json
{
    "message": "Payment ID and authority are required"
}
```

### وضعیت پرداخت نامعتبر (400 Bad Request)
```json
{
    "message": "Payment is not in CREATED status"
}
```
"""

def doc_payment_status():
    return """
# 🐈 سناریو
🛠️ دریافت وضعیت پرداخت

این API برای دریافت وضعیت فعلی یک پرداخت با استفاده از شناسه پرداخت استفاده می‌شود. کاربر می‌تواند با استفاده از این API، وضعیت پرداخت خود را در هر زمان بررسی کند.

---

## 📌 توضیحات فیلدهای پاسخ

1. **id** (شناسه پرداخت):  
   - شناسه یکتای پرداخت در سیستم - مثال: `456`

2. **deposit_id** (شناسه صندوق):  
   - شناسه یکتای صندوق - مثال: `123`

3. **deposit_name** (نام صندوق):  
   - عنوان صندوق - مثال: `صندوق خانوادگی`

4. **status** (وضعیت پرداخت):  
   - وضعیت فعلی پرداخت - مقادیر ممکن:
     - `created` (ایجاد شده)
     - `redirected` (هدایت شده به درگاه)
     - `verified` (تایید شده)
     - `failed` (ناموفق)
     - `cancelled` (لغو شده)

5. **gateway_name** (نام درگاه):  
   - نام درگاه پرداخت - مثال: `zarinpal`

6. **amount** (مبلغ پرداخت):  
   - مبلغ پرداخت بدون احتساب قسط وام (به ریال) - مثال: `100000.0`

7. **includes_loan_installment** (شامل قسط وام):  
   - آیا این پرداخت شامل قسط وام نیز می‌شود؟ - مقادیر: `true/false`

8. **loan_installment_amount** (مبلغ قسط وام):  
   - مبلغ قسط وام (به ریال) - مثال: `50000.0`

9. **total_amount** (مبلغ کل):  
   - مجموع مبلغ پرداخت و قسط وام (به ریال) - مثال: `150000.0`

10. **reference_number** (شماره پیگیری):  
    - شماره پیگیری تراکنش (در صورت موفقیت) - مثال: `12345678`

11. **authority** (کد اختصاصی):  
    - کد اختصاصی تراکنش از درگاه پرداخت - مثال: `A00000000000000000000000000123456`

12. **payment_url** (آدرس پرداخت):  
    - آدرس درگاه پرداخت - مثال: `https://www.zarinpal.com/pg/StartPay/A00000000000000000000000000123456`

13. **created_at** (زمان ایجاد):  
    - زمان ایجاد پرداخت - مثال: `2023-06-15T14:28:15`

14. **redirected_at** (زمان هدایت):  
    - زمان هدایت کاربر به درگاه پرداخت - مثال: `2023-06-15T14:29:30`

15. **payment_date** (تاریخ پرداخت):  
    - تاریخ و زمان پرداخت موفق - مثال: `2023-06-15T14:30:45`

16. **transaction** (اطلاعات تراکنش) - در صورت وجود:  
    - اطلاعات تراکنش مرتبط با این پرداخت
    - شامل:
      - `id`: شناسه تراکنش
      - `status`: وضعیت تراکنش
      - `amount`: مبلغ تراکنش
      - `transaction_type`: نوع تراکنش
      - `created_at`: زمان ایجاد تراکنش

---

## 🚀 درخواست API

### نمونه درخواست:
```http
GET /api/payments/status/456/
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### نمونه پاسخ (پرداخت در وضعیت CREATED):
```json
{
  "id": 456,
  "deposit_id": 123,
  "deposit_name": "صندوق خانوادگی",
  "status": "created",
  "gateway_name": "zarinpal",
  "amount": 100000.0,
  "includes_loan_installment": true,
  "loan_installment_amount": 50000.0,
  "total_amount": 150000.0,
  "reference_number": null,
  "authority": "A00000000000000000000000000123456",
  "payment_url": "https://www.zarinpal.com/pg/StartPay/A00000000000000000000000000123456",
  "created_at": "2023-06-15T14:28:15",
  "redirected_at": null,
  "payment_date": null
}
```

### نمونه پاسخ (پرداخت در وضعیت VERIFIED):
```json
{
  "id": 456,
  "deposit_id": 123,
  "deposit_name": "صندوق خانوادگی",
  "status": "verified",
  "gateway_name": "zarinpal",
  "amount": 100000.0,
  "includes_loan_installment": true,
  "loan_installment_amount": 50000.0,
  "total_amount": 150000.0,
  "reference_number": "12345678",
  "authority": "A00000000000000000000000000123456",
  "payment_url": "https://www.zarinpal.com/pg/StartPay/A00000000000000000000000000123456",
  "created_at": "2023-06-15T14:28:15",
  "redirected_at": "2023-06-15T14:29:30",
  "payment_date": "2023-06-15T14:30:45",
  "transaction": {
    "id": 654,
    "status": "success",
    "amount": 150000.0,
    "transaction_type": "income",
    "created_at": "2023-06-15T14:30:45"
  }
}
```

## ⚠️ خطاهای احتمالی

### پرداخت یافت نشد (404 Not Found)
```json
{
    "message": "Payment not found or does not belong to you"
}
```
"""
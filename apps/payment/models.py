from decimal import Decimal
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator


class Payment(models.Model):
    """
    Payment model for tracking user payments to deposits.
    
    This model records payments made by users to their deposits, including
    whether the payment includes a loan installment.
    """
    class PaymentStatus(models.TextChoices):
        CREATED = 'created', _('Created')  # پرداخت ایجاد شده اما کاربر هنوز به درگاه نرفته
        REDIRECTED = 'redirected', _('Redirected to Gateway')  # کاربر به درگاه پرداخت هدایت شده
        VERIFIED = 'verified', _('Verified')  # پرداخت تایید شده
        FAILED = 'failed', _('Failed')  # پرداخت ناموفق
        CANCELLED = 'cancelled', _('Cancelled')  # پرداخت توسط کاربر لغو شده
    user = models.ForeignKey('account.User', on_delete=models.CASCADE, related_name='payments', verbose_name=_("User"))
    deposit = models.ForeignKey('deposit.Deposit', on_delete=models.CASCADE, related_name='payments', verbose_name=_("Deposit"))
    deposit_membership = models.ForeignKey('deposit.DepositMembership', on_delete=models.CASCADE, related_name='payments', verbose_name=_("Deposit Membership"))
    transaction = models.ForeignKey('transaction.Transaction', on_delete=models.SET_NULL, related_name='payments', null=True, blank=True, verbose_name=_("Transaction"))
    gateway_name = models.CharField(max_length=100, verbose_name=_("Payment Gateway"), help_text=_("Name of the payment gateway used"))
    status = models.CharField(max_length=20, choices=PaymentStatus.choices, default=PaymentStatus.CREATED, verbose_name=_("Payment Status"), help_text=_("Current status of the payment"))
    amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name=_("Amount"), validators=[MinValueValidator(Decimal('0.01'))], help_text=_("Payment amount in IRR"))
    includes_loan_installment = models.BooleanField(default=False, verbose_name=_("Includes Loan Installment"), help_text=_("Whether this payment includes a loan installment"))
    loan_installment_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'), verbose_name=_("Loan Installment Amount"), help_text=_("Amount of loan installment included in this payment"))
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name=_("Total Amount"), help_text=_("Total payment amount including any loan installment"))
    reference_number = models.CharField(max_length=255, null=True, blank=True, verbose_name=_("Reference Number"), help_text=_("Payment reference or tracking number"))
    authority = models.CharField(max_length=255, null=True, blank=True, verbose_name=_("Authority"), help_text=_("Payment authority code from gateway"))
    payment_url = models.URLField(max_length=500, null=True, blank=True, verbose_name=_("Payment URL"), help_text=_("URL to the payment gateway"))
    payment_date = models.DateTimeField(verbose_name=_("Payment Date"), help_text=_("Date and time when the payment was made"))
    redirected_at = models.DateTimeField(null=True, blank=True, verbose_name=_("Redirected At"), help_text=_("Date and time when user was redirected to payment gateway"))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))
    
    class Meta:
        verbose_name = _("Payment")
        verbose_name_plural = _("Payments")
        ordering = ["-payment_date"]
    
    def __str__(self):
        return f"{self.user.fullname} - {self.deposit.title} - {self.total_amount} IRR"
    
    def save(self, *args, **kwargs):
        # Calculate total amount if not set
        if not self.total_amount:
            self.total_amount = self.amount + self.loan_installment_amount
        
        # Ensure loan_installment_amount is 0 if not including loan installment
        if not self.includes_loan_installment:
            self.loan_installment_amount = Decimal('0.00')
            
        super().save(*args, **kwargs)
    
    @classmethod
    def get_user_payments(cls, user):
        """Get all payments made by a specific user."""
        return cls.objects.filter(user=user).order_by('-payment_date')
    
    @classmethod
    def get_deposit_payments(cls, deposit):
        """Get all payments made to a specific deposit."""
        return cls.objects.filter(deposit=deposit).order_by('-payment_date')
    
    @classmethod
    def get_total_user_payments(cls, user, deposit=None):
        """Get total amount paid by a user, optionally filtered by deposit."""
        query = cls.objects.filter(user=user, status=cls.PaymentStatus.VERIFIED)
        if deposit:
            query = query.filter(deposit=deposit)
        
        result = query.aggregate(
            total=models.Sum('total_amount')
        )['total']
        
        return result or Decimal('0.00')
        
    def mark_as_redirected(self, authority):
        """Mark payment as redirected to gateway."""
        from django.utils import timezone
        self.status = self.PaymentStatus.REDIRECTED
        if authority:
            self.authority = authority.strip()
        self.redirected_at = timezone.now()
        
        # Set payment_url if not already set
        if not self.payment_url and self.authority:
            self.payment_url = f"https://www.zarinpal.com/pg/StartPay/{self.authority}"
            
        self.save()
        
    def mark_as_verified(self, reference_number):
        """Mark payment as verified with reference number and create/update transaction."""
        from django.utils import timezone
        from .services import PaymentTransactionService
        
        self.status = self.PaymentStatus.VERIFIED
        self.reference_number = reference_number
        self.payment_date = timezone.now()
        self.save()
        
        # Create or update transaction
        PaymentTransactionService.create_or_update_transaction(self)
        
    def mark_as_failed(self):
        """Mark payment as failed and update transaction if exists."""
        from apps.transaction.models import Transaction
        
        self.status = self.PaymentStatus.FAILED
        self.save()
        
        # Update transaction status if exists
        if self.transaction:
            self.transaction.status = Transaction.TransactionStatus.FAILED
            self.transaction.save()
        
    def mark_as_cancelled(self):
        """Mark payment as cancelled by user and update transaction if exists."""
        from apps.transaction.models import Transaction
        
        self.status = self.PaymentStatus.CANCELLED
        self.save()
        
        # Update transaction status if exists
        if self.transaction:
            self.transaction.status = Transaction.TransactionStatus.FAILED
            self.transaction.save()
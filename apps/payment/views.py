from rest_framework import permissions, status
from rest_framework.response import Response
from rest_framework.generics import CreateAPIView, UpdateAPIView
from rest_framework.views import APIView
from django.utils.translation import gettext_lazy as _
from django.shortcuts import render
from django.views import View
from django.utils import timezone
from django.conf import settings
import requests
import json
from .models import Payment
from .serializers import CreatePaymentSerializer
from .services import PaymentCalculationService
from apps.deposit.models import Deposit, DepositMembership
from utils.exceptions import AppAPIException
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from .doc import get_payment_info_swagger


# Security token for client validation
PAYMENT_SECURITY_TOKEN = "4BB74D54631B2AEDD915C4181F451"
def format_amount_to_toman(amount):
    """
    Format amount to Toman with thousands separator.
    
    Args:
        amount: Amount in Toman (Decimal or float)
        
    Returns:
        str: Formatted amount string with thousands separator
    """
    try:
        # Convert to integer (remove decimal part)
        amount_int = int(amount)
        
        # Format with thousands separator
        return f"{amount_int:,}"
    except (ValueError, TypeError):
        return "0"
 
# Available payment gateways
PAYMENT_GATEWAYS = [
    {
        "id": "zarinpal",
        "name": "زرین‌پال",
        "icon": "https://www.zarinpal.com/blog/wp-content/uploads/2021/06/%D9%86%D9%85%D8%A7%D8%AF-%D8%A7%D8%B7%D9%85%DB%8C%D9%86%D8%A7%D9%86-211x300.png"
    },
    {
        "id": "mellat",
        "name": "به‌پرداخت ملت",
        "icon": "/static/media/payment/mellat.png"
    },
    {
        "id": "saman",
        "name": "سامان",
        "icon": "/static/media/payment/saman.png"
    },
    {
        "id": "parsian",
        "name": "پارسیان",
        "icon": "/static/media/payment/parsian.png"
    }
]

import requests
class GetPaymentInfoView(APIView):
    """
    API endpoint for getting payment information for a specific deposit.

    This endpoint returns payment information including:
    - List of payment gateways
    - Payment amount based on deposit type
    - Loan installment information if the user has an active loan
    - Total payment amount
    - Deposit name
    """
    permission_classes = [permissions.IsAuthenticated]

    @get_payment_info_swagger
    def get(self, request, deposit_id):
        """
        Get payment information for a specific deposit.
        """
        try:
            # Get the deposit
            deposit = Deposit.objects.get(id=deposit_id)

            # Check if user is a member of this deposit
            membership = DepositMembership.objects.filter(
                user=request.user,
                deposit=deposit,
                is_active=True
            ).first()

            if not membership:
                raise AppAPIException({"message": "You are not an active member of this deposit"},status_code=status.HTTP_403_FORBIDDEN)
            
            # Get payment information using the service
            payment_info = PaymentCalculationService.get_payment_info(deposit, membership)
            
            # Set callback URL for payment verification
            # Use the direct URL without api prefix
            base_url = request.build_absolute_uri('/').rstrip('/')
            callback_url = f"{base_url}/payments/verify/"
            
            # Prepare Zarinpal request
            url = 'https://api.zarinpal.com/pg/v4/payment/request.json'
            headers = {'accept': 'application/json', 'content-type': 'application/json'}
            # Convert Decimal to int for JSON serialization and convert from Toman to Rial (multiply by 10)
            amount_in_rials = int(payment_info['total_amount'] * 10)
            
            data = {
                "merchant_id": settings.ZARINPAL_MERCHANT_ID,
                "amount": amount_in_rials,
                "callback_url": callback_url,
                "description": f"پرداخت به صندوق {deposit.title}",
                "metadata": {
                    "email": request.user.email,
                    "mobile": str(getattr(request.user, 'phone_number', ''))
                }
            }
            print(f'-data--> {data}')
            response = requests.post(url, json=data, headers=headers)
            response_data = response.json()
            print(f'response: {response_data}')
            # Check if Zarinpal request was successful
            if response_data.get('data', {}).get('code') == 100:
                # Get the authority code
                authority = response_data.get('data', {}).get('authority', '')
                
                # Store payment information in session for later verification
                # Convert Decimal to float for JSON serialization in session
                request.session['payment_id'] = None  # Will be set after creating payment record
                request.session['amount'] = float(payment_info['total_amount'])
                request.session['deposit_id'] = deposit.id
                
                # Create payment record in database
                payment_data = {
                    'deposit': deposit.id,
                    'gateway_name': 'zarinpal',
                    'amount': payment_info['total_amount'],
                    'includes_loan_installment': payment_info['has_loan_installment'],
                    'loan_installment_amount': payment_info['loan_installment_amount'],
                    'security_token': PAYMENT_SECURITY_TOKEN,
                    'status': Payment.PaymentStatus.CREATED,
                    'authority': authority
                }
                
                # Pass the request in the context
                serializer = CreatePaymentSerializer(data=payment_data, context={'request': request})
                if serializer.is_valid():
                    payment = serializer.save()
                    request.session['payment_id'] = payment.id
                
                # Generate Zarinpal payment URL
                payment_url = f"https://www.zarinpal.com/pg/StartPay/{authority}"
                
                # Update payment with payment URL
                if serializer.is_valid() and payment:
                    payment.payment_url = payment_url
                    payment.authority = authority
                    payment.save(update_fields=['payment_url', 'authority'])
                
                # Prepare response - convert Decimal values to float for JSON serialization
                response_data = {
                    'payment_id': payment.id,
                    'deposit_id': deposit.id,
                    'deposit_name': deposit.title,
                    'payment_gateways': PAYMENT_GATEWAYS,
                    'payment_amount': float(payment_info['payment_amount']),
                    'has_loan_installment': payment_info['has_loan_installment'],
                    'loan_installment_amount': float(payment_info['loan_installment_amount']),
                    'total_amount': float(payment_info['total_amount']),
                    'current_due_date': payment_info['current_due_date'],
                    'payment_url': payment_url,
                    'authority': authority
                }
            else:
                
                # Handle Zarinpal error
                error_code = response_data.get('errors', {}).get('code', 0)
                error_message = response_data.get('errors', {}).get('message', 'خطای نامشخص')
                
                raise AppAPIException({
                    "message": f"خطا در ایجاد درخواست پرداخت: {error_message}",
                    "code": error_code
                }, status_code=status.HTTP_400_BAD_REQUEST)

            return Response(response_data)

        except Deposit.DoesNotExist:
            raise AppAPIException({"message": "Deposit not found"},status_code=status.HTTP_404_NOT_FOUND)


class CreatePaymentView(CreateAPIView):
    """
    API endpoint for creating payments.

    This endpoint allows authenticated users to create new payments to deposits.
    A security token must be included in the request body for validation.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = CreatePaymentSerializer


    def post(self, request, *args, **kwargs):
        """
        Create a new payment.

        Requires a security token in the request body for validation.
        """
        # Validate security token
        security_token = request.data.get('security_token')
        if not security_token or security_token != PAYMENT_SECURITY_TOKEN:
            raise AppAPIException({"message": "Invalid or missing security token"},status_code=status.HTTP_403_FORBIDDEN)
        print(f'-------------1---------')
        # Get deposit ID from request data
        deposit_id = request.data.get('deposit')
        if not deposit_id:
            raise AppAPIException({"message": "Deposit ID is required"},status_code=status.HTTP_400_BAD_REQUEST)
        print(f'-------------2---------')
        try:
            # Get the deposit
            deposit = Deposit.objects.get(id=deposit_id)

            # Check if user is a member of this deposit
            membership = DepositMembership.objects.filter(
                user=request.user,
                deposit=deposit,
                is_active=True
            ).first()

            if not membership:
                raise AppAPIException({"message": "You are not an active member of this deposit"},status_code=status.HTTP_403_FORBIDDEN)

            # Get payment information using the service
            print(f'-->deposit: {deposit}/ membership: {membership}')
            payment_info = PaymentCalculationService.get_payment_info(deposit, membership)

            # Prepare data for serializer
            data = {
                'deposit': deposit.id,
                'gateway_name': request.data.get('gateway_name', 'Unknown'),
                'amount': payment_info['total_amount'],
                'includes_loan_installment': payment_info['has_loan_installment'],
                'loan_installment_amount': payment_info['loan_installment_amount'],
                'security_token': security_token
                # reference_number is now generated automatically in the serializer
            }

            serializer = self.get_serializer(data=data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Deposit.DoesNotExist:
            return Response(
                {'error': _('Deposit not found')},
                status=status.HTTP_404_NOT_FOUND
            )


class PaymentRedirectView(APIView):
    """
    API endpoint for marking a payment as redirected to the payment gateway.
    
    This endpoint should be called when the user clicks on the payment button
    and is about to be redirected to the payment gateway.
    """
    permission_classes = [permissions.IsAuthenticated]
    

    def post(self, request):
        """
        Mark a payment as redirected to the payment gateway.
        
        Required POST parameters:
        - payment_id: ID of the payment to mark as redirected
        
        Optional POST parameters:
        - gateway_id: Name of the payment gateway
        
        Returns:
        - message: Success message
        - payment_url: URL to the payment gateway
        - gateway_id: Name of the payment gateway
        """
        payment_id = request.data.get('payment_id')
        # authority = request.data.get('authority')
        # payment_url = request.data.get('payment_url')
        print(f'----PaymentRedirectView----> {request.data}')

        # if not payment_id or not authority:
        #     raise AppAPIException(
        #         {"message": "Payment ID and authority are required"},
        #         status_code=status.HTTP_400_BAD_REQUEST
        #     )
        
        try:
            # Get the payment
            payment = Payment.objects.get(id=payment_id, user=request.user)
            authority = payment.authority
            payment_url = payment.payment_url
                        
            # Mark payment as redirected
            payment.mark_as_redirected(authority)
            
            # Update payment_url if provided
            if payment_url and not payment.payment_url:
                payment.payment_url = payment_url
                payment.save(update_fields=['payment_url'])
            
            return Response(
                {
                    "message": "Payment marked as redirected successfully",
                    "payment_url": payment.payment_url,
                    "gateway_id": payment.gateway_name
                },
                status=status.HTTP_200_OK
            )
            
        except Payment.DoesNotExist:
            raise AppAPIException(
                {"message": "Payment not found or does not belong to you"},
                status_code=status.HTTP_404_NOT_FOUND
            )


class PaymentStatusView(APIView):
    """
    API endpoint for checking the status of a payment.
    
    This endpoint allows authenticated users to check the current status
    of a payment by its ID.
    """
    permission_classes = [permissions.IsAuthenticated]
    

    def get(self, request, payment_id):
        """
        Get the current status of a payment.
        
        Args:
            payment_id: The ID of the payment to check
            
        Returns:
            Payment details including status, amount, and transaction details if available
        """
        try:
            print(f'----PaymentStatusView---->')
            # Get the payment
            payment = Payment.objects.get(id=payment_id, user=request.user)
            
            # Prepare response data
            response_data = {
                "id": payment.id,
                "deposit_id": payment.deposit.id,
                "deposit_name": payment.deposit.title,
                "status": payment.status,
                "gateway_name": payment.gateway_name,
                "amount": float(payment.amount),
                "includes_loan_installment": payment.includes_loan_installment,
                "loan_installment_amount": float(payment.loan_installment_amount),
                "total_amount": float(payment.total_amount),
                "reference_number": payment.reference_number,
                "authority": payment.authority,
                "payment_url": payment.payment_url,
                "created_at": payment.created_at,
                "redirected_at": payment.redirected_at,
                "payment_date": payment.payment_date
            }
            
            # Add transaction details if available
            if payment.transaction:
                response_data["transaction"] = {
                    "id": payment.transaction.id,
                    "status": payment.transaction.status,
                    "amount": float(payment.transaction.amount),
                    "transaction_type": payment.transaction.transaction_type,
                    "created_at": payment.transaction.created_at
                }
            
            return Response(response_data)
            
        except Payment.DoesNotExist:
            raise AppAPIException(
                {"message": "Payment not found or does not belong to you"},
                status_code=status.HTTP_404_NOT_FOUND
            )


class ZarinpalVerifyView(View):
    """
    View for handling Zarinpal payment verification.
    
    This view receives the callback from Zarinpal after a payment attempt,
    verifies the payment with Zarinpal's API, and shows appropriate
    success or failure templates.
    """
    
    def get(self, request):
        """
        Handle the callback from Zarinpal payment gateway.
        
        GET parameters:
        - Authority: The payment authority code
        - Status: The payment status ('OK' or 'NOK')
        """
        authority = request.GET.get('Authority', '')
        status = request.GET.get('Status', '')
        
        print(f'----ZarinpalVerifyView----> authority:{authority}/status:{status}/{request.GET}')
        
        # Get payment details from database using the authority code
        try:
            payment = Payment.objects.get(authority=authority)
            amount = payment.total_amount
            deposit_id = payment.deposit.id
            payment_id = payment.id
            print(f'----ZarinpalVerifyView----> Found payment: ID={payment_id}, Amount={amount}, Deposit ID={deposit_id}')

            # If payment was successful according to Zarinpal
            if status == 'OK':
                # Verify payment with Zarinpal
                # Convert amount to int for JSON serialization if it's a Decimal and convert from Toman to Rial (multiply by 10)
                amount_int = int(amount * 10) if hasattr(amount, 'quantize') else int(amount * 10)
                
                verify_data = {
                    "merchant_id": settings.ZARINPAL_MERCHANT_ID,
                    "amount": amount_int,
                    "authority": authority
                }
                
                verify_url = 'https://api.zarinpal.com/pg/v4/payment/verify.json'
                headers = {'accept': 'application/json', 'content-type': 'application/json'}
                
                verify_response = requests.post(verify_url, json=verify_data, headers=headers)
                verify_result = verify_response.json()
                print(f'----ZarinpalVerifyView-2---> {verify_result}')
                print(f'مبلغ به تومان: {amount} - مبلغ به ریال: {amount_int}')

                # If payment verification was successful
                if verify_result.get('data', {}).get('code') == 100:
                    # Get the reference ID (transaction ID)
                    ref_id = verify_result.get('data', {}).get('ref_id', '')
                    
                    # Update payment status in database
                    payment.mark_as_verified(ref_id)
                    
                    # Send notification to the deposit owner
                    from apps.account.services import NotificationService
                    NotificationService.send_payment_created_notification_to_owner(
                        deposit_id=payment.deposit.id
                    )
                    
                    # Render success template
                    context = {
                        'ref_id': ref_id,
                        'amount': format_amount_to_toman(amount),
                        'amount_rial': format_amount_to_toman(amount * 10),
                        'payment_date': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'deposit_title': payment.deposit.title,
                        'subject': f"واریزی صندوق {payment.deposit.title}"
                    }
                    return render(request, 'payment_success.html', context)
                else:
                    # Payment verification failed
                    error_code = verify_result.get('errors', {}).get('code', 0)
                    error_message = verify_result.get('errors', {}).get('message', 'خطای نامشخص')
                    
                    # Update payment status in database
                    payment.mark_as_failed()
                    
                    context = {
                        'error_code': error_code,
                        'error_message': error_message,
                        'deposit_id': deposit_id,
                        'amount': format_amount_to_toman(amount),
                        'amount_rial': format_amount_to_toman(amount * 10),
                        'payment_date': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'deposit_title': payment.deposit.title,
                        'subject': f"واریزی صندوق {payment.deposit.title}"
                    }
                    return render(request, 'payment_failed.html', context)
            else:
                print(f'---Payment.mark_as_cancelled----')

                # Payment was cancelled or failed according to Zarinpal callback
                # Update payment status in database
                payment.mark_as_cancelled()
                        
                context = {
                    'error_code': 'NOK',
                    'error_message': 'پرداخت توسط کاربر لغو شد یا با خطا مواجه شد',
                    'deposit_id': deposit_id,
                    'amount': format_amount_to_toman(amount),
                    'amount_rial': format_amount_to_toman(amount * 10),
                    'payment_date': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'deposit_title': payment.deposit.title,
                    'subject': f"واریزی صندوق {payment.deposit.title}"
                }
                return render(request, 'payment_failed.html', context)
                
        except Payment.DoesNotExist:
            print(f'---Payment.DoesNotExist----')
            # Payment with this authority not found
            context = {
                'error_code': 'Not Found',
                'error_message': f'پرداختی با کد پیگیری {authority} یافت نشد',
                'payment_date': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                'amount': '0',
                'amount_rial': '0',
                'subject': 'پرداخت ناموفق'
            }
            return render(request, 'payment_failed.html', context)
                
        except Exception as e:
            print(f'---Payment.Exception----')

            # Handle any exceptions
            context = {
                'error_code': 'Exception',
                'error_message': str(e),
                'deposit_id': deposit_id if 'deposit_id' in locals() else None,
                'amount': format_amount_to_toman(amount) if 'amount' in locals() else '0',
                'amount_rial': format_amount_to_toman(amount * 10) if 'amount' in locals() else '0',
                'payment_date': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                'subject': 'خطا در پردازش پرداخت'
            }
            return render(request, 'payment_failed.html', context)
from rest_framework import serializers
from decimal import Decimal
import random
import string
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from .models import Payment
from apps.transaction.models import Transaction


class CreatePaymentSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new payments.

    This serializer handles the creation of payment records, including
    validation, transaction creation, and loan installment processing.
    """
    # Add security_token field for validation in the view
    security_token = serializers.CharField(write_only=True, required=False)

    def generate_reference_number(self):
        """
        Generate a random 8-digit reference number.
        """
        return ''.join(random.choices(string.digits, k=8))

    class Meta:
        model = Payment
        fields = [
            'id', 'deposit', 'gateway_name', 'amount', 'status',
            'includes_loan_installment', 'loan_installment_amount',
            'reference_number', 'authority', 'payment_url', 'total_amount', 'payment_date',
            'redirected_at', 'created_at', 'updated_at', 'security_token'
        ]
        read_only_fields = [
            'id', 'reference_number', 'payment_url', 'total_amount', 'payment_date',
            'redirected_at', 'created_at', 'updated_at'
        ]

    def validate(self, data):
        # Ensure amount is at least 0.01
        if data.get('amount', 0) < Decimal('0.01'):
            raise serializers.ValidationError({
                'amount': _('Ensure this value is greater than or equal to 0.01.')
            })

        # Ensure loan_installment_amount is positive if includes_loan_installment is True
        if data.get('includes_loan_installment', False):
            if data.get('loan_installment_amount', 0) <= 0:
                raise serializers.ValidationError({
                    'loan_installment_amount': _('Loan installment amount must be greater than zero when includes_loan_installment is True.')
                })

        return data

    def create(self, validated_data):
        request = self.context.get('request')
        if not request:
            raise serializers.ValidationError({
                'error': _('Request context is required for this serializer.')
            })
        user = request.user

        # Get the deposit membership
        from apps.deposit.models import DepositMembership
        deposit_membership = DepositMembership.objects.filter(
            user=user,
            deposit=validated_data['deposit'],
            is_active=True
        ).first()

        if not deposit_membership:
            raise serializers.ValidationError({
                'deposit': _('You are not an active member of this deposit.')
            })

        # Calculate total amount
        amount = validated_data.get('amount', Decimal('0.00'))
        loan_installment_amount = validated_data.get('loan_installment_amount', Decimal('0.00'))
        if validated_data.get('includes_loan_installment', False):
            total_amount = amount + loan_installment_amount
        else:
            total_amount = amount
            loan_installment_amount = Decimal('0.00')

        # Create a transaction record with PENDING status
        transaction = Transaction.objects.create(
            deposit=validated_data['deposit'],
            user=user,
            status=Transaction.TransactionStatus.PENDING,  # Set status to PENDING until payment is verified
            amount=total_amount,
            transaction_type=Transaction.TransactionType.INCOME,
            description=f"Payment via {validated_data.get('gateway_name')}",
            fee=Decimal('0.00')  # Assuming no fee for now
        )

        # Create the payment record with a generated reference number
        payment = Payment.objects.create(
            user=user,
            deposit=validated_data['deposit'],
            deposit_membership=deposit_membership,
            transaction=transaction,
            gateway_name=validated_data.get('gateway_name', 'Unknown'),
            amount=amount,
            includes_loan_installment=validated_data.get('includes_loan_installment', False),
            loan_installment_amount=loan_installment_amount,
            total_amount=total_amount,
            reference_number=self.generate_reference_number(),
            payment_date=timezone.now()
        )

        # We'll process loan installment only after payment is verified
        # This will be handled in the Payment.mark_as_verified method
            
        return payment

    def _process_loan_installment(self, user, payment):
        """Process loan installment payment"""
        from apps.loan.models import Loan, LoanInstallment

        # Find active loans for this user in this deposit
        loans = Loan.objects.filter(
            deposit=payment.deposit,
            deposit_membership__user=user,
            status=Loan.LoanStatus.ACTIVE
        )

        if not loans.exists():
            return

        # For each loan, find the first unpaid installment
        for loan in loans:
            next_installment = LoanInstallment.objects.filter(
                loan=loan,
                is_paid=False
            ).order_by('due_date').first()

            if next_installment and next_installment.amount <= payment.loan_installment_amount:
                # Mark the installment as paid
                next_installment.mark_as_paid()

                # Update the loan status
                loan.update_status()
                break
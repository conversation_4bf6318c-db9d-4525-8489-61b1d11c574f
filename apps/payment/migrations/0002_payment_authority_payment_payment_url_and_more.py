# Generated by Django 5.2.1 on 2025-05-19 12:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payment', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='authority',
            field=models.CharField(blank=True, help_text='Payment authority code from gateway', max_length=255, null=True, verbose_name='Authority'),
        ),
        migrations.AddField(
            model_name='payment',
            name='payment_url',
            field=models.URLField(blank=True, help_text='URL to the payment gateway', max_length=500, null=True, verbose_name='Payment URL'),
        ),
        migrations.AddField(
            model_name='payment',
            name='redirected_at',
            field=models.DateTimeField(blank=True, help_text='Date and time when user was redirected to payment gateway', null=True, verbose_name='Redirected At'),
        ),
        migrations.AddField(
            model_name='payment',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('redirected', 'Redirected to Gateway'), ('verified', 'Verified'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='created', help_text='Current status of the payment', max_length=20, verbose_name='Payment Status'),
        ),
    ]

# Generated by Django 3.2.4 on 2025-03-18 23:11

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('transaction', '0003_transaction_status'),
        ('deposit', '0008_delete_depositlottery'),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gateway_name', models.CharField(help_text='Name of the payment gateway used', max_length=100, verbose_name='Payment Gateway')),
                ('amount', models.DecimalField(decimal_places=2, help_text='Payment amount in IRR', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Amount')),
                ('includes_loan_installment', models.BooleanField(default=False, help_text='Whether this payment includes a loan installment', verbose_name='Includes Loan Installment')),
                ('loan_installment_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount of loan installment included in this payment', max_digits=15, verbose_name='Loan Installment Amount')),
                ('total_amount', models.DecimalField(decimal_places=2, help_text='Total payment amount including any loan installment', max_digits=15, verbose_name='Total Amount')),
                ('reference_number', models.CharField(blank=True, help_text='Payment reference or tracking number', max_length=255, null=True, verbose_name='Reference Number')),
                ('payment_date', models.DateTimeField(help_text='Date and time when the payment was made', verbose_name='Payment Date')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('deposit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='deposit.deposit', verbose_name='Deposit')),
                ('deposit_membership', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='deposit.depositmembership', verbose_name='Deposit Membership')),
                ('transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments', to='transaction.transaction', verbose_name='Transaction')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-payment_date'],
            },
        ),
    ]

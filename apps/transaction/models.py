from decimal import Decimal
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError



class Transaction(models.Model):
    class TransactionStatus(models.TextChoices):
        PENDING = 'pending', _('Pending')
        SUCCESS = 'success', _('Success')
        FAILED = 'failed', _('Failed')
        
    class TransactionType(models.TextChoices):
        INCOME = 'income', _('Income')
        WITHDRAWAL = 'withdrawal', _('Withdrawal')

    deposit = models.ForeignKey('deposit.Deposit', on_delete=models.CASCADE, related_name='transactions')
    user = models.ForeignKey('account.User', on_delete=models.CASCADE, related_name='deposit_transactions')
    due_date = models.ForeignKey("deposit.DepositDueDate", on_delete=models.SET_NULL, verbose_name=_("Due Date"), null=True,blank=True,related_name="transactions") 

    status = models.CharField(max_length=55, choices=TransactionStatus.choices, default=TransactionStatus.PENDING)

    amount = models.DecimalField(max_digits=15,decimal_places=2,verbose_name=_('Amount in IRR'), help_text=_('Amount in Iranian Rial (IRR)'))

    transaction_type = models.CharField(max_length=55, choices=TransactionType.choices)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('Created At'))
    fee = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    description = models.CharField(max_length=255, null=True, blank=True)


    def __str__(self):
        return f"{self.get_status_display()}/{self.get_transaction_type_display()} - {self.amount}"

    class Meta:
        verbose_name = _('Transaction')
        verbose_name_plural = _('Transactions')
        ordering = ["-created_at"]  # Order transactions by most recent first
        indexes = [
            models.Index(fields=["deposit", "due_date"]),  
            models.Index(fields=["created_at"]),  
        ]

    def clean(self):
        """
        Validate the transaction before saving.
        - Ensure the amount is not None and is positive.
        - Ensure the due date belongs to the same deposit.
        """
        # Check for None amount first to prevent TypeError
        if self.amount is None:
            raise ValidationError(_("Transaction amount cannot be None."))

        if self.amount <= 0:
            raise ValidationError(_("Transaction amount must be greater than zero."))

        # if self.due_date and self.due_date.deposit != self.deposit:
        #     raise ValidationError(_("The due date must belong to the same deposit."))

    def save(self, *args, **kwargs):
        """
        Override the save method to ensure validation is always run.
        """
        self.clean()
        super().save(*args, **kwargs)
        
        
    @classmethod
    def get_total_balance(cls, deposit):
        # محاسبه مجموع واریزها (income)
        income_sum = cls.objects.filter(
            deposit=deposit, 
            status=cls.TransactionStatus.SUCCESS,
            transaction_type=cls.TransactionType.INCOME
        ).aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0.00')
        
        # محاسبه مجموع برداشت‌ها (withdrawal)
        withdrawal_sum = cls.objects.filter(
            deposit=deposit, 
            status=cls.TransactionStatus.SUCCESS,
            transaction_type=cls.TransactionType.WITHDRAWAL
        ).aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0.00')
        
        # موجودی صندوق = واریزها - برداشت‌ها
        return income_sum - withdrawal_sum

    
    @classmethod
    def get_due_date_balance(cls, deposit, due_date):
        balance = cls.objects.filter(deposit=deposit, due_date=due_date, status=cls.TransactionStatus.SUCCESS).aggregate(
            due_date_balance=models.Sum('amount')
        )['due_date_balance']
        
        # Ensure the result is a Decimal, defaulting to 0 if no transactions exist
        return Decimal(balance) if balance is not None else Decimal('0.00')
        
    @classmethod
    def get_total_deposits(cls, deposit):
        """
        محاسبه کل واریزی‌های موفق یک صندوق
        """
        total_deposits = cls.objects.filter(
            deposit=deposit,
            status=cls.TransactionStatus.SUCCESS,
            transaction_type=cls.TransactionType.INCOME
        ).aggregate(
            total=models.Sum('amount')
        )['total']

        # اطمینان از برگرداندن یک مقدار Decimal، پیش‌فرض 0 اگر هیچ تراکنشی وجود نداشته باشد
        return Decimal(total_deposits) if total_deposits is not None else Decimal('0.00')

    @classmethod
    def get_total_withdrawals(cls, deposit):
        """
        محاسبه کل برداشت‌های موفق یک صندوق
        """
        total_withdrawals = cls.objects.filter(
            deposit=deposit,
            status=cls.TransactionStatus.SUCCESS,
            transaction_type=cls.TransactionType.WITHDRAWAL
        ).aggregate(
            total=models.Sum('amount')
        )['total']

        # اطمینان از برگرداندن یک مقدار Decimal، پیش‌فرض 0 اگر هیچ تراکنشی وجود نداشته باشد
        return Decimal(total_withdrawals) if total_withdrawals is not None else Decimal('0.00')
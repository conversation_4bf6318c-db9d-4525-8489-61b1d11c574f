from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.templatetags.static import static
from django.db.models import Q

from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)
from unfold.decorators import display, action

from apps.transaction.models import Transaction
from utils.admin import project_admin_site, RegionFilteredAdmin


class TransactionAdmin(RegionFilteredAdmin):
    """Admin for Transaction model"""
    list_display = (
        'transaction_info', 'transaction_status', 'transaction_amount', 'transaction_fee', 'user_info', 'jalali_created'
    )
    list_filter = (
        ('status', ChoicesRadioFilter),
        ('transaction_type', ChoicesRadioFilter),
        ('created_at', RangeDateFilter),
        ('amount', RangeNumericFilter),
        'deposit',
        'user',
    )
    ordering = ('-created_at',)
    readonly_fields = ('created_at',)
    search_fields = (
        'deposit__title', 'user__fullname', 'user__phone_number', 'description'
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    autocomplete_fields = ('user', )
    
    fieldsets = (
        (None, {"fields": (("transaction_type", "status"),)}),
        (
            _("Basic Information"),
            {
                "fields": ("deposit", "user", "due_date"),
                "classes": ["tab"],
            },
        ),
        (
            _('Financial Details'), {
                'fields': ('amount', 'fee'),
                "classes": ["tab"],
            }
        ),
        (
            _('Additional Information'), {
                'fields': ('description',),
                "classes": ["tab"],
            }
        ),
        (
            _('System Information'), {
                'fields': ('created_at',),
                "classes": ["tab"],
            }
        ),
    )
    
    def has_delete_permission(self, request, obj=None):
        return False
    
    def formfield_for_dbfield(self, db_field, **kwargs):
        # تنظیمات فیلد amount
        if db_field.name == 'amount':
            kwargs['required'] = True
        return super().formfield_for_dbfield(db_field, **kwargs)
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        qs = qs.select_related('deposit', 'user', 'due_date')
        return qs
    
    @display(description='Transaction', header=True)
    def transaction_info(self, obj):
        transaction_type_display = obj.get_transaction_type_display()
        
        deposit_name = obj.deposit.title if obj.deposit else _('No Deposit')
        
        return [
            f"{transaction_type_display} - {deposit_name}",
            obj.description or _('No description'),
            None,
        ]
    
    @display(description='Status', label={
        Transaction.TransactionStatus.SUCCESS: 'success',
        Transaction.TransactionStatus.PENDING: 'warning',
        Transaction.TransactionStatus.FAILED: 'danger',
    })
    def transaction_status(self, obj):
        return obj.status, obj.get_status_display()
    
    @display(description='Amount')
    def transaction_amount(self, obj):
        formatted_amount = f"{obj.amount:,}" if obj.amount else "0"
        return f"{formatted_amount} {_('IRR')}"
    
    @display(description='Fee')
    def transaction_fee(self, obj):
        formatted_fee = f"{obj.fee:,}" if obj.fee else "0"
        return f"{formatted_fee} {_('IRR')}"
    
    @display(description='User', header=True)
    def user_info(self, obj):        
        return [
            obj.user.fullname if obj.user else _('No User'),
            obj.user.phone_number if obj.user else '',
            None,
        ]
    
    @display(description=_('Created Date'))
    def jalali_created(self, obj):
        from utils.date_utils import format_jalali_date
        if obj.created_at:
            return format_jalali_date(obj.created_at, "%Y/%m/%d %H:%M")
        return "-"
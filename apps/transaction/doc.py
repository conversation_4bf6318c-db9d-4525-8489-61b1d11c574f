from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status

# ============================================================================
# STRUCTURED SWAGGER DOCUMENTATION FOR TRANSACTION APP
# ============================================================================

# Transaction List Swagger Documentation
transaction_list_swagger = swagger_auto_schema(
    operation_description="Get list of user transactions with filtering options by type, deposit, amount, and date range",
    operation_summary="List User Transactions",
    tags=['Transaction'],
    manual_parameters=[
        openapi.Parameter(
            'transaction_type',
            openapi.IN_QUERY,
            description="Filter by transaction type",
            type=openapi.TYPE_STRING,
            enum=['income', 'withdrawal'],
            required=False
        ),
        openapi.Parameter(
            'deposit_id',
            openapi.IN_QUERY,
            description="Filter by deposit ID",
            type=openapi.TYPE_INTEGER,
            required=False
        ),
        openapi.Parameter(
            'deposit_type',
            openapi.IN_QUERY,
            description="Filter by deposit type",
            type=openapi.TYPE_STRING,
            enum=['Poll', 'Saving', 'Reporting'],
            required=False
        ),
        openapi.Parameter(
            'amount',
            openapi.IN_QUERY,
            description="Filter by amount in Tomans. Format: '10+' for amounts greater than 10 Tomans, '10-' for amounts less than 10 Tomans",
            type=openapi.TYPE_STRING,
            required=False,
            example='100+'
        ),
        openapi.Parameter(
            'date_filter',
            openapi.IN_QUERY,
            description="Filter transactions by time period",
            type=openapi.TYPE_STRING,
            enum=['yesterday', 'last_week', 'last_month', 'last_three_months'],
            required=False
        ),
        openapi.Parameter(
            'ordering',
            openapi.IN_QUERY,
            description="Order by field. Prefix with '-' for descending order",
            type=openapi.TYPE_STRING,
            required=False,
            example='-created_at'
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of transactions retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                        'deposit': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=123),
                                'title': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق خانوادگی')
                            }
                        ),
                        'amount': openapi.Schema(type=openapi.TYPE_NUMBER, example=100000.0),
                        'transaction_type': openapi.Schema(type=openapi.TYPE_STRING, example='income'),
                        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z'),
                        'description': openapi.Schema(type=openapi.TYPE_STRING, example='Monthly deposit payment')
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "id": 1,
                        "deposit": {
                            "id": 123,
                            "title": "صندوق خانوادگی"
                        },
                        "amount": 100000.0,
                        "transaction_type": "income",
                        "created_at": "2023-01-01T10:00:00Z",
                        "description": "Monthly deposit payment"
                    }
                ]
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Invalid filter parameters",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'amount': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=["Invalid format. Use format like '10+' or '5-'"]
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

def doc_transaction_list():
    return """
# لیست تراکنش‌های کاربر

این API لیست تراکنش‌های کاربر را نمایش می‌دهد. به صورت پیش‌فرض، فقط تراکنش‌های موفق نمایش داده می‌شوند.

## گزینه‌های فیلتر:

1. **نوع تراکنش** (transaction_type):
   - فیلتر بر اساس نوع تراکنش (واریز یا برداشت)
   - مقادیر مجاز: `income` (واریز) یا `withdrawal` (برداشت)
   - مثال: `?transaction_type=income`

2. **وضعیت تراکنش** (status):
   - فیلتر بر اساس وضعیت تراکنش
   - مقادیر مجاز: `pending` (در انتظار)، `success` (موفق)، `failed` (ناموفق)
   - مثال: `?status=pending`

3. **شناسه صندوق** (deposit_id):
   - فیلتر بر اساس صندوق خاص
   - مثال: `?deposit_id=5`

4. **نوع صندوق** (deposit_type):
   - فیلتر بر اساس نوع صندوق
   - مقادیر مجاز: `Poll` (صندوق قرعه‌کشی)، `Saving` (صندوق پس‌انداز)، `Reporting` (صندوق گزارشی)
   - مثال: `?deposit_type=Poll`

5. **مبلغ** (amount):
   - فیلتر بر اساس مبلغ (به تومان)
   - فرمت: `[مبلغ]+` برای بیشتر از، `[مبلغ]-` برای کمتر از
   - مثال‌ها:
     - بیشتر از ۵۰ تومان: `?amount=50+`
     - کمتر از ۳۰ تومان: `?amount=30-`

5. **فیلتر تاریخ** (date_filter):
   - فیلتر بر اساس بازه زمانی
   - گزینه‌ها: `yesterday` (دیروز)، `last_week` (هفته گذشته)، `last_month` (ماه گذشته)، `last_three_months` (سه ماه گذشته)
   - مثال: `?date_filter=last_week`
"""

def doc_transaction_detail():
    return """
# جزئیات تراکنش

این API جزئیات یک تراکنش خاص را نمایش می‌دهد.

به صورت پیش‌فرض، فقط تراکنش‌های موفق قابل دسترسی هستند. برای مشاهده تراکنش‌ها با هر وضعیتی، از پارامتر `include_all_statuses` استفاده کنید.

## پارامترها:

1. **include_all_statuses**:
   - برای مشاهده تراکنش‌ها با هر وضعیتی (نه فقط موفق)، مقدار آن را `true` قرار دهید
   - مقدار پیش‌فرض: false
   - مثال: `?include_all_statuses=true`
"""
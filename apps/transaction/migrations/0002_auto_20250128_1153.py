# Generated by Django 3.2.4 on 2025-01-28 11:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('transaction', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='transaction',
            options={'ordering': ['-created_at'], 'verbose_name': 'Transaction', 'verbose_name_plural': 'Transactions'},
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['deposit', 'due_date'], name='transaction_deposit_1a5856_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['created_at'], name='transaction_created_0b821d_idx'),
        ),
    ]

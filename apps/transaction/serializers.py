from rest_framework import serializers
from .models import Transaction


class TransactionListSerializer(serializers.ModelSerializer):
    deposit = serializers.SerializerMethodField()

    class Meta:
        model = Transaction
        fields = [
            'id',
            'deposit',
            'amount',
            'transaction_type',
            'created_at',
            'description',
        ]
        
    def get_deposit(self, obj):
        return {
            "id": obj.deposit.id,
            "title": obj.deposit.title,
        }        

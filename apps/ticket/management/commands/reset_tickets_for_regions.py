from django.core.management.base import BaseCommand
from django.db import transaction
from apps.ticket.models import Ticket, TicketMessage


class Command(BaseCommand):
    help = 'Reset all tickets and ticket messages to prepare for region-based ticket system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm deletion of all tickets and messages',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This command will delete ALL tickets and ticket messages. '
                    'Use --confirm to proceed.'
                )
            )
            return

        try:
            with transaction.atomic():
                # Count existing data
                ticket_count = Ticket.objects.count()
                message_count = TicketMessage.objects.count()
                
                self.stdout.write(f'Found {ticket_count} tickets and {message_count} messages')
                
                # Delete all ticket messages first (due to foreign key constraints)
                TicketMessage.objects.all().delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {message_count} ticket messages')
                )
                
                # Delete all tickets
                Ticket.objects.all().delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {ticket_count} tickets')
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        'Successfully reset all tickets and messages. '
                        'Ready for region-based ticket system implementation.'
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error occurred: {str(e)}')
            )
            raise

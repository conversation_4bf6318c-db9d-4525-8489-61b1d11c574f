from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from unfold.admin import ModelAdmin, TabularInline
from unfold.decorators import display, action
from unfold.contrib.filters.admin import (
    RangeDateFilter,
)
from utils.admin import project_admin_site, RegionFilteredAdmin

from .models import Ticket, TicketMessage
from utils import gregorian_to_jalali


class TicketMessageInline(TabularInline):
    model = TicketMessage
    extra = 0
    readonly_fields = ('user', 'created_at')
    fields = ('user', 'message_type', 'content', 'content_image', 'is_read', 'created_at')
    can_delete = False
    verbose_name = _("Ticket Message")
    verbose_name_plural = _("Ticket Messages")

# @admin.register(Ticket)
class TicketAdmin(RegionFilteredAdmin):
    list_display = ('subject', 'user', 'region', 'deposit', 'is_closed', 'formatted_created_at', 'messages_count', 'view_messages_link')
    list_filter = (
        'is_closed',
        'region',
        ('created_at', RangeDateFilter),
        ('closed_at', RangeDateFilter),
    )
    search_fields = ('subject', 'description', 'user__fullname', 'deposit__title', 'region__name')
    readonly_fields = ('created_at', 'updated_at', 'closed_at')
    inlines = [TicketMessageInline]
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    fieldsets = (
        (_("General Information"), {
            'fields': ('subject', 'description', 'user', 'region', 'deposit')
        }),
        (_("Status"), {
            'fields': ('is_closed', 'closed_at')
        }),
        (_("Metadata"), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    @display(description=_('Created'))
    def formatted_created_at(self, obj):
        try:
            return gregorian_to_jalali(obj.created_at, date_format="%Y/%m/%d") if obj.created_at else "-"
        except:
            # Fallback if gregorian_to_jalali fails
            return obj.created_at.strftime("%Y-%m-%d") if obj.created_at else "-"


    @display(
        description=_("Messages Count"),
        ordering="messages__count",
    )
    def messages_count(self, obj):
        return obj.messages.count()

    @display(
        boolean=True,
        description=_("Is Read")
    )
    def is_read(self, obj):
        """نمایش وضعیت خوانده‌شدن تیکت"""
        unread_messages = obj.messages.filter(is_read=False).exclude(user=obj.user).exists()
        return not unread_messages

    @display(
        description=_("Messages"),
        ordering="id",
    )
    def view_messages_link(self, obj):
        """ایجاد لینک به لیست پیام‌های تیکت"""
        url = reverse('admin:ticket_ticketmessage_changelist') + f'?ticket__id__exact={obj.id}'
        return format_html('<a href="{}" class="button" style="white-space:nowrap;">'
                          '<i class="material-icons"></i> {}</a>',
                          url, _("Messages"))

# @admin.register(TicketMessage)
class TicketMessageAdmin(RegionFilteredAdmin):
    list_display = ('ticket', 'display_user', 'message_type', 'content_preview', 'is_read', 'created_at')
    list_filter = ('is_read', 'message_type',
        ('created_at', RangeDateFilter),
    )
    search_fields = ('content', 'user__fullname', 'ticket__subject')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)

    @display(
        description=_("Content Preview"),
        ordering="content",
    )
    def content_preview(self, obj):
        if obj.message_type == TicketMessage.MessageType.IMAGE:
            if obj.content_image:
                return format_html(
                    '<img src="{}" style="max-width: 50px; max-height: 50px;"> {}',
                    obj.content_image.url,
                    obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
                )
            return _("Image (no file)")
        else:
            return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
    
    @display(
        description=_("User"),
        ordering="user__fullname",
    )
    def display_user(self, obj):
        """نمایش نام کاربر بر اساس نقش در صندوق"""
        try:
            # بررسی نقش کاربر در صندوق مربوط به تیکت
            user_membership = obj.user.deposit_memberships.filter(deposit=obj.ticket.deposit).first()
            
            if user_membership and user_membership.role in ['Owner', 'Admin']:
                # اگر کاربر مالک یا ادمین صندوق است
                return format_html('<span style="color: #1976d2; font-weight: bold;">{} ({})</span>', 
                                  obj.user.fullname, user_membership.role)
            else:
                # اگر کاربر نقش دیگری دارد یا عضو صندوق نیست
                return format_html('<span>{}</span>', obj.user.fullname)
        except Exception as e:
            # در صورت بروز خطا، نام کاربر را به صورت عادی نمایش می‌دهیم
            return obj.user.fullname
        
        
project_admin_site.register(Ticket, TicketAdmin)
project_admin_site.register(TicketMessage, TicketMessageAdmin)

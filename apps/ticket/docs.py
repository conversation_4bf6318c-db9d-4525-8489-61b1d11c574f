from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status

# ============================================================================
# STRUCTURED SWAGGER DOCUMENTATION FOR TICKET APP
# ============================================================================

# Ticket Message Create Swagger Documentation
ticket_message_create_swagger = swagger_auto_schema(
    operation_description="Create a new ticket message. Supports both text and image messages. For text messages, provide content. For image messages, provide content_image and optionally content for caption.",
    operation_summary="Create Ticket Message",
    tags=['Ticket'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'content': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Message content (required for text messages, optional for image messages as caption)',
                example='سلام، من مشکلی با صندوق دارم'
            ),
            'message_type': openapi.Schema(
                type=openapi.TYPE_STRING,
                enum=['text', 'image'],
                description='Type of message (default: text)',
                example='text'
            ),
            'content_image': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Image file path (required for image messages). Use /api/upload-tmp/ to upload image first.',
                example='/tmp/abc123-image.jpg'
            )
        },
        required=[]
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Ticket message created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(
                        type=openapi.TYPE_INTEGER,
                        description='Message unique identifier',
                        example=1
                    ),
                    'ticket': openapi.Schema(
                        type=openapi.TYPE_INTEGER,
                        description='Ticket ID this message belongs to',
                        example=5
                    ),
                    'user': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=10),
                            'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='علی احمدی'),
                            'role': openapi.Schema(type=openapi.TYPE_STRING, example='Member')
                        }
                    ),
                    'content': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Message content',
                        example='سلام، من مشکلی با صندوق دارم'
                    ),
                    'message_type': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Type of message',
                        example='text'
                    ),
                    'content_image': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Image URL (null for text messages)',
                        example=None
                    ),
                    'is_read': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Whether message has been read',
                        example=False
                    ),
                    'created_at': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        format=openapi.FORMAT_DATETIME,
                        description='Message creation timestamp',
                        example='2023-01-01T10:00:00Z'
                    ),
                    'is_self': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Whether this message was sent by current user',
                        example=True
                    ),
                    'deposit_name': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Name of the deposit this ticket belongs to',
                        example='صندوق خانوادگی'
                    ),
                    'region_name': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Name of the region this ticket belongs to',
                        example='تهران'
                    ),
                    'sender_type': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        enum=['user', 'manager', 'support'],
                        description='Type of sender (user, manager, or support)',
                        example='user'
                    ),
                    'sender_info': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=10),
                            'name': openapi.Schema(type=openapi.TYPE_STRING, example='علی احمدی'),
                            'role': openapi.Schema(type=openapi.TYPE_STRING, example='Member'),
                            'type': openapi.Schema(type=openapi.TYPE_STRING, example='user')
                        },
                        description='Detailed sender information'
                    )
                }
            ),
            examples={
                'text_message_from_user': {
                    "id": 1,
                    "ticket": 5,
                    "user": {
                        "id": 10,
                        "fullname": "علی احمدی",
                        "role": "Member"
                    },
                    "content": "سلام، من مشکلی با صندوق دارم",
                    "message_type": "text",
                    "content_image": None,
                    "is_read": False,
                    "created_at": "2023-01-01T10:00:00Z",
                    "is_self": True,
                    "deposit_name": "صندوق خانوادگی",
                    "region_name": "تهران",
                    "sender_type": "user",
                    "sender_info": {
                        "id": 10,
                        "name": "علی احمدی",
                        "role": "Member",
                        "type": "user"
                    }
                },
                'image_message_from_user': {
                    "id": 2,
                    "ticket": 5,
                    "user": {
                        "id": 10,
                        "fullname": "علی احمدی",
                        "role": "Member"
                    },
                    "content": "تصویر مشکل",
                    "message_type": "image",
                    "content_image": "https://example.com/media/ticket_messages/2023/01/image.jpg",
                    "is_read": False,
                    "created_at": "2023-01-01T10:05:00Z",
                    "is_self": True,
                    "deposit_name": "صندوق خانوادگی",
                    "sender_type": "user",
                    "sender_info": {
                        "id": 10,
                        "name": "علی احمدی",
                        "role": "Member",
                        "type": "user"
                    }
                },
                'text_message_from_manager': {
                    "id": 3,
                    "ticket": 5,
                    "user": {
                        "id": 15,
                        "fullname": "صندوق خانوادگی",
                        "role": "Owner"
                    },
                    "content": "سلام، مشکل شما بررسی شد",
                    "message_type": "text",
                    "content_image": None,
                    "is_read": False,
                    "created_at": "2023-01-01T11:00:00Z",
                    "is_self": False,
                    "deposit_name": "صندوق خانوادگی",
                    "sender_type": "manager",
                    "sender_info": {
                        "id": 15,
                        "name": "صندوق خانوادگی",
                        "role": "Owner",
                        "type": "manager"
                    }
                },
                'text_message_from_support': {
                    "id": 4,
                    "ticket": 10,
                    "user": {
                        "id": 20,
                        "fullname": "پشتیبانی فنی",
                        "role": "Support"
                    },
                    "content": "گزارش شما دریافت شد و بررسی خواهد شد",
                    "message_type": "text",
                    "content_image": None,
                    "is_read": False,
                    "created_at": "2023-01-01T12:00:00Z",
                    "is_self": False,
                    "deposit_name": None,
                    "sender_type": "support",
                    "sender_info": {
                        "id": 20,
                        "name": "پشتیبانی فنی",
                        "role": "Support",
                        "type": "support"
                    }
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation errors",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'non_field_errors': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['Content is required for text messages.']
                    ),
                    'content_image': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['Content image is required for image messages.']
                    )
                }
            ),
            examples={
                'text_message_error': {
                    "non_field_errors": ["Content is required for text messages."]
                },
                'image_message_error': {
                    "non_field_errors": ["Content image is required for image messages."]
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Close Ticket Swagger Documentation
close_ticket_swagger = swagger_auto_schema(
    operation_description="Close a ticket by providing its ID. Only users who have access to the ticket can close it. Once closed, the ticket cannot be reopened.",
    operation_summary="Close Ticket",
    tags=['Ticket'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'ticket_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='ID of the ticket to close',
                example=5
            )
        },
        required=['ticket_id']
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Ticket closed successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='success'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Ticket closed successfully.'
                    ),
                    'ticket': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'id': openapi.Schema(
                                type=openapi.TYPE_INTEGER,
                                example=5
                            ),
                            'subject': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                example='مشکل در صندوق'
                            ),
                            'description': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                example='توضیحات مشکل'
                            ),
                            'is_closed': openapi.Schema(
                                type=openapi.TYPE_BOOLEAN,
                                example=True
                            ),
                            'closed_at': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_DATETIME,
                                example='2023-01-01T12:00:00Z'
                            ),
                            'created_at': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_DATETIME,
                                example='2023-01-01T10:00:00Z'
                            )
                        }
                    )
                }
            ),
            examples={
                'application/json': {
                    "status": "success",
                    "message": "Ticket closed successfully.",
                    "ticket": {
                        "id": 5,
                        "deposit": 1,
                        "deposit_name": "صندوق خانوادگی",
                        "user": {
                            "id": 10,
                            "fullname": "علی احمدی"
                        },
                        "subject": "مشکل در صندوق",
                        "description": "توضیحات مشکل",
                        "is_closed": True,
                        "closed_at": "2023-01-01T12:00:00Z",
                        "created_at": "2023-01-01T10:00:00Z",
                        "is_read": True,
                        "last_message_date": "2023-01-01T11:30:00Z",
                        "user_id": 10
                    }
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation errors",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='error'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Failed to close ticket.'
                    ),
                    'errors': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'ticket_id': openapi.Schema(
                                type=openapi.TYPE_ARRAY,
                                items=openapi.Schema(type=openapi.TYPE_STRING),
                                example=['Ticket not found.']
                            )
                        }
                    )
                }
            ),
            examples={
                'ticket_not_found': {
                    "status": "error",
                    "message": "Failed to close ticket.",
                    "errors": {
                        "ticket_id": ["Ticket not found."]
                    }
                },
                'already_closed': {
                    "status": "error",
                    "message": "Failed to close ticket.",
                    "errors": {
                        "ticket_id": ["Ticket is already closed."]
                    }
                },
                'no_permission': {
                    "status": "error",
                    "message": "Failed to close ticket.",
                    "errors": {
                        "ticket_id": ["You don't have permission to close this ticket."]
                    }
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Ticket Message List Swagger Documentation
ticket_message_list_swagger = swagger_auto_schema(
    operation_description="Get list of all messages in a specific ticket. Messages are ordered by creation time. All unread messages will be marked as read when this endpoint is called.",
    operation_summary="List Ticket Messages",
    tags=['Ticket'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of ticket messages retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Message unique identifier',
                            example=1
                        ),
                        'ticket': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Ticket ID this message belongs to',
                            example=5
                        ),
                        'user': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=10),
                                'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='علی احمدی'),
                                'role': openapi.Schema(type=openapi.TYPE_STRING, example='Member')
                            },
                            description='User who sent this message'
                        ),
                        'content': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Message content',
                            example='سلام، من مشکلی با صندوق دارم'
                        ),
                        'message_type': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            enum=['text', 'image'],
                            description='Type of message',
                            example='text'
                        ),
                        'content_image': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Image URL (null for text messages)',
                            example=None
                        ),
                        'is_read': openapi.Schema(
                            type=openapi.TYPE_BOOLEAN,
                            description='Whether message has been read',
                            example=True
                        ),
                        'created_at': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            format=openapi.FORMAT_DATETIME,
                            description='Message creation timestamp',
                            example='2023-01-01T10:00:00Z'
                        ),
                        'is_self': openapi.Schema(
                            type=openapi.TYPE_BOOLEAN,
                            description='Whether this message was sent by current user',
                            example=True
                        ),
                        'deposit_name': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Name of the deposit this ticket belongs to',
                            example='صندوق خانوادگی'
                        ),
                        'sender_type': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            enum=['user', 'manager', 'support'],
                            description='Type of sender (user, manager, or support)',
                            example='user'
                        ),
                        'sender_info': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=10),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, example='علی احمدی'),
                                'role': openapi.Schema(type=openapi.TYPE_STRING, example='Member'),
                                'type': openapi.Schema(type=openapi.TYPE_STRING, example='user')
                            },
                            description='Detailed sender information'
                        )
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "id": 1,
                        "ticket": 5,
                        "user": {
                            "id": 10,
                            "fullname": "علی احمدی",
                            "role": "Member"
                        },
                        "content": "سلام، من مشکلی با صندوق دارم",
                        "message_type": "text",
                        "content_image": None,
                        "is_read": True,
                        "created_at": "2023-01-01T10:00:00Z",
                        "is_self": True,
                        "deposit_name": "صندوق خانوادگی",
                        "sender_type": "user",
                        "sender_info": {
                            "id": 10,
                            "name": "علی احمدی",
                            "role": "Member",
                            "type": "user"
                        }
                    },
                    {
                        "id": 2,
                        "ticket": 5,
                        "user": {
                            "id": 15,
                            "fullname": "صندوق خانوادگی",
                            "role": "Owner"
                        },
                        "content": "سلام، مشکل شما بررسی شد",
                        "message_type": "text",
                        "content_image": None,
                        "is_read": True,
                        "created_at": "2023-01-01T11:00:00Z",
                        "is_self": False,
                        "deposit_name": "صندوق خانوادگی",
                        "sender_type": "manager",
                        "sender_info": {
                            "id": 15,
                            "name": "صندوق خانوادگی",
                            "role": "Owner",
                            "type": "manager"
                        }
                    },
                    {
                        "id": 3,
                        "ticket": 5,
                        "user": {
                            "id": 10,
                            "fullname": "علی احمدی",
                            "role": "Member"
                        },
                        "content": "تصویر مشکل",
                        "message_type": "image",
                        "content_image": "https://example.com/media/ticket_messages/2023/01/image.jpg",
                        "is_read": True,
                        "created_at": "2023-01-01T11:30:00Z",
                        "is_self": True,
                        "deposit_name": "صندوق خانوادگی",
                        "sender_type": "user",
                        "sender_info": {
                            "id": 10,
                            "name": "علی احمدی",
                            "role": "Member",
                            "type": "user"
                        }
                    },
                    {
                        "id": 4,
                        "ticket": 10,
                        "user": {
                            "id": 20,
                            "fullname": "پشتیبانی فنی",
                            "role": "Support"
                        },
                        "content": "گزارش شما دریافت شد و بررسی خواهد شد",
                        "message_type": "text",
                        "content_image": None,
                        "is_read": True,
                        "created_at": "2023-01-01T12:00:00Z",
                        "is_self": False,
                        "deposit_name": None,
                        "sender_type": "support",
                        "sender_info": {
                            "id": 20,
                            "name": "پشتیبانی فنی",
                            "role": "Support",
                            "type": "support"
                        }
                    }
                ]
            }
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Ticket not found or no access",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Not found.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)


def doc_ticket_message_list():
    return """
# 📋 Scenario
لیست پیام‌های تیکت

دریافت لیست تمام پیام‌های یک تیکت خاص. پیام‌ها بر اساس تاریخ ایجاد مرتب می‌شوند و تمام پیام‌های خوانده‌نشده به صورت خودکار به عنوان خوانده‌شده علامت‌گذاری می‌شوند.

---

## 🚀 درخواست API

### URL:
```
GET /api/ticket/{ticket_id}/messages/
```

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Token your_auth_token_here     |

### Parameters:
| پارامتر       | نوع     | الزامی | توضیحات                    |
|---------------|---------|--------|-----------------------------|
| `ticket_id`   | integer | ✅     | شناسه تیکت مورد نظر        |

---

## 📄 نمونه درخواست:

### درخواست ساده:
```bash
curl -X GET "http://localhost:8000/api/ticket/5/messages/" \
     -H "Authorization: Token your_auth_token_here" \
     -H "Content-Type: application/json"
```

### پاسخ موفق:
```json
[
    {
        "id": 1,
        "ticket": 5,
        "user": {
            "id": 10,
            "fullname": "علی احمدی",
            "role": "Member"
        },
        "content": "سلام، من مشکلی با صندوق دارم",
        "message_type": "text",
        "content_image": null,
        "is_read": true,
        "created_at": "2023-01-01T10:00:00Z",
        "is_self": true,
        "deposit_name": "صندوق خانوادگی",
        "sender_type": "user",
        "sender_info": {
            "id": 10,
            "name": "علی احمدی",
            "role": "Member",
            "type": "user"
        }
    },
    {
        "id": 2,
        "ticket": 5,
        "user": {
            "id": 15,
            "fullname": "صندوق خانوادگی",
            "role": "Owner"
        },
        "content": "سلام، مشکل شما بررسی شد",
        "message_type": "text",
        "content_image": null,
        "is_read": true,
        "created_at": "2023-01-01T11:00:00Z",
        "is_self": false,
        "deposit_name": "صندوق خانوادگی",
        "sender_type": "manager",
        "sender_info": {
            "id": 15,
            "name": "صندوق خانوادگی",
            "role": "Owner",
            "type": "manager"
        }
    },
    {
        "id": 3,
        "ticket": 5,
        "user": {
            "id": 10,
            "fullname": "علی احمدی",
            "role": "Member"
        },
        "content": "تصویر مشکل",
        "message_type": "image",
        "content_image": "https://example.com/media/ticket_messages/2023/01/image.jpg",
        "is_read": true,
        "created_at": "2023-01-01T11:30:00Z",
        "is_self": true,
        "deposit_name": "صندوق خانوادگی",
        "sender_type": "user",
        "sender_info": {
            "id": 10,
            "name": "علی احمدی",
            "role": "Member",
            "type": "user"
        }
    }
]
```

### پاسخ خطا - تیکت یافت نشد:
```json
{
    "detail": "Not found."
}
```

### پاسخ خطا - عدم احراز هویت:
```json
{
    "detail": "Authentication credentials were not provided."
}
```

---

## 💡 نکات مهم:
1. **احراز هویت:**
   - کاربر باید توکن احراز هویت معتبر ارائه دهد.
2. **دسترسی:**
   - فقط کاربرانی که دسترسی به تیکت دارند می‌توانند پیام‌ها را مشاهده کنند.
3. **مرتب‌سازی:**
   - پیام‌ها بر اساس تاریخ ایجاد به صورت صعودی مرتب می‌شوند (قدیمی‌ترین اول).
4. **علامت‌گذاری خوانده‌شده:**
   - تمام پیام‌های خوانده‌نشده به صورت خودکار به عنوان خوانده‌شده علامت‌گذاری می‌شوند.
5. **تشخیص فرستنده:**
   - فیلد `sender_type` نشان می‌دهد پیام از طرف کاربر (`user`) یا مدیر (`manager`) است.
   - فیلد `sender_info` اطلاعات کامل فرستنده را شامل نام، نقش و نوع ارائه می‌دهد.
6. **نمایش نام:**
   - برای کاربران: نام واقعی کاربر نمایش داده می‌شود.
   - برای مدیران: نام صندوق به همراه نقش (Owner/Admin) نمایش داده می‌شود.
7. **انواع پیام:**
   - `text`: پیام متنی
   - `image`: پیام تصویری (شامل URL تصویر)

---

## 🔍 کاربردها:
- نمایش تاریخچه مکالمات تیکت
- مشاهده پیام‌های کاربر و مدیر
- تشخیص نوع پیام (متن یا تصویر)
- پیگیری وضعیت خوانده شدن پیام‌ها
"""


def doc_ticket_message_create():
    return """
# 💬 Scenario
ایجاد پیام جدید در تیکت

کاربر یا مدیر صندوق می‌تواند پیام جدید در تیکت ایجاد کند. پیام‌ها می‌توانند از نوع متن یا تصویر باشند. در پاسخ مشخص می‌شود که پیام از طرف کاربر است یا مدیر.

---

## 🚀 درخواست API

### URL:
```
POST /api/ticket/{ticket_id}/messages/create/
```

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Token your_auth_token_here     |

### Body Parameters:

| فیلد           | نوع     | الزامی | توضیحات                                    |
|---------------|---------|--------|--------------------------------------------|
| `content`     | string  | شرطی   | محتوای پیام (الزامی برای پیام متنی)        |
| `message_type`| string  | ❌     | نوع پیام: text یا image (پیش‌فرض: text)   |
| `content_image`| string | شرطی   | مسیر تصویر (الزامی برای پیام تصویری)       |

---

## 📄 نمونه درخواست:

### پیام متنی:
```json
{
    "content": "سلام، من مشکلی با صندوق دارم",
    "message_type": "text"
}
```

### پیام تصویری:
```json
{
    "content": "تصویر مشکل",
    "message_type": "image",
    "content_image": "/tmp/abc123-image.jpg"
}
```

### پیام تصویری بدون متن:
```json
{
    "message_type": "image",
    "content_image": "/tmp/abc123-image.jpg"
}
```

---

## 📄 نمونه پاسخ:

### پاسخ پیام از طرف کاربر:
```json
{
    "id": 1,
    "ticket": 5,
    "user": {
        "id": 10,
        "fullname": "علی احمدی",
        "role": "Member"
    },
    "content": "سلام، من مشکلی با صندوق دارم",
    "message_type": "text",
    "content_image": null,
    "is_read": false,
    "created_at": "2023-01-01T10:00:00Z",
    "is_self": true,
    "deposit_name": "صندوق خانوادگی",
    "sender_type": "user",
    "sender_info": {
        "id": 10,
        "name": "علی احمدی",
        "role": "Member",
        "type": "user"
    }
}
```

### پاسخ پیام از طرف مدیر:
```json
{
    "id": 2,
    "ticket": 5,
    "user": {
        "id": 15,
        "fullname": "صندوق خانوادگی",
        "role": "Owner"
    },
    "content": "سلام، مشکل شما بررسی شد",
    "message_type": "text",
    "content_image": null,
    "is_read": false,
    "created_at": "2023-01-01T11:00:00Z",
    "is_self": false,
    "deposit_name": "صندوق خانوادگی",
    "sender_type": "manager",
    "sender_info": {
        "id": 15,
        "name": "صندوق خانوادگی",
        "role": "Owner",
        "type": "manager"
    }
}
```

---

## 💡 نکات مهم:
1. **نوع پیام:**
   - اگر message_type مشخص نشود، به صورت پیش‌فرض "text" در نظر گرفته می‌شود.
2. **پیام متنی:**
   - برای پیام متنی، فیلد content الزامی است.
3. **پیام تصویری:**
   - برای پیام تصویری، فیلد content_image الزامی است.
   - فیلد content اختیاری است و می‌تواند به عنوان caption استفاده شود.
4. **آپلود تصویر:**
   - ابتدا تصویر را با استفاده از `/api/upload-tmp/` آپلود کنید.
   - سپس مسیر دریافتی را در فیلد content_image استفاده کنید.
5. **تشخیص فرستنده:**
   - فیلد `sender_type` نشان می‌دهد پیام از طرف کاربر (`user`)، مدیر (`manager`)، یا پشتیبانی (`support`) است.
   - فیلد `sender_info` اطلاعات کامل فرستنده را شامل نام، نقش و نوع ارائه می‌دهد.
6. **نمایش نام:**
   - برای کاربران: نام واقعی کاربر نمایش داده می‌شود.
   - برای مدیران: نام صندوق به همراه نقش (Owner/Admin) نمایش داده می‌شود.
   - برای پشتیبانی: نام "پشتیبانی فنی" نمایش داده می‌شود.

---

## 🔍 کاربردها:
- ارسال پیام متنی در تیکت توسط کاربر، مدیر، یا پشتیبانی
- ارسال تصویر در تیکت
- پاسخ به تیکت‌های پشتیبانی
- تشخیص اینکه پیام از طرف کاربر، مدیر صندوق، یا پشتیبانی فنی است
"""

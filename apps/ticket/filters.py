from django.db.models import OuterRef, Subquery, Q, Exists
from rest_framework.filters import BaseFilterBackend
from apps.ticket.models import Ticket, TicketMessage


class IsReadFilterBackend(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        is_read_param = request.query_params.get('is_read', None)
        if is_read_param is None:
            return queryset

        user = request.user
        # سابکوئری برای یافتن پیام‌های خوانده نشده دیگران
        unread_subquery = TicketMessage.objects.filter(
            ticket=OuterRef('pk'),
            is_read=False
        ).exclude(user=user)

        # اضافه کردن فیلد موقت برای فیلتر
        queryset = queryset.annotate(
            has_unread=Exists(unread_subquery)
        )

        # تبدیل پارامتر به بولین
        is_read = is_read_param.lower() in ['true', '1']
        if is_read:
            return queryset.filter(has_unread=False)
        else:
            return queryset.filter(has_unread=True)


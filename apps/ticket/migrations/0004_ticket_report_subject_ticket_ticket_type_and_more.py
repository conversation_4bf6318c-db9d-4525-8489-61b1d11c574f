# Generated by Django 5.2.1 on 2025-07-15 16:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deposit', '0013_remove_deposit_status'),
        ('issues', '0003_remove_reportmessage_report_and_more'),
        ('ticket', '0003_ticket_closed_at_ticket_is_closed'),
    ]

    operations = [
        migrations.AddField(
            model_name='ticket',
            name='report_subject',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tickets', to='issues.reportsubject', verbose_name='Report Subject'),
        ),
        migrations.AddField(
            model_name='ticket',
            name='ticket_type',
            field=models.CharField(choices=[('ticket', 'Ticket'), ('report', 'Report')], default='ticket', max_length=10, verbose_name='Ticket Type'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='ticket',
            name='deposit',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tickets', to='deposit.deposit', verbose_name='Deposit'),
        ),
    ]

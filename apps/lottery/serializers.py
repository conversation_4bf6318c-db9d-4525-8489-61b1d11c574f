from rest_framework import serializers

from utils import FileFieldSerializer, absolute_url

from apps.deposit.models import Deposit, DepositMembership, DepositDueDate
from apps.lottery.models import DepositLottery




class DepositLotterySerializer(serializers.ModelSerializer):
    avatar = FileFieldSerializer(required=False, source="deposit_membership.user.avatar")
    fullname = serializers.CharField(source="deposit_membership.user.fullname")
    due_date_number = serializers.SerializerMethodField()
    role = serializers.SerializerMethodField()
    class Meta:
        model = DepositLottery
        fields = ['id', 'avatar', 'fullname', 'due_date_number', 'role', 'created_at']
        read_only_fields = ['avatar', 'fullname']
        
    def get_due_date_number(self, obj):
        return obj.due_date.due_date_number
    
    def get_role(self, obj):
        return obj.deposit_membership.role
        
            
            
class LotteryWinnerSerializer(serializers.ModelSerializer):
    winner_fullname = serializers.CharField(source='deposit_membership.user.fullname', read_only=True)
    lottery_number = serializers.IntegerField(read_only=True)

    class Meta:
        model = DepositLottery
        fields = ['id', 'lottery_number', 'winner_fullname', 'created_at']

class DepositLotteryInfoSerializer(serializers.ModelSerializer):
    due_date_number = serializers.IntegerField(read_only=True)
    members = serializers.SerializerMethodField()
    list_members = serializers.SerializerMethodField()
    winners = serializers.SerializerMethodField()
    can_perform_lottery = serializers.SerializerMethodField()
    lottery_per_month_count = serializers.SerializerMethodField()
    current_month_lotteries_count = serializers.SerializerMethodField()

    class Meta:
        model = DepositDueDate
        fields = ['due_date_number', 'due_date','is_lottery_completed', 'winners', 'members', 'list_members',
                 'can_perform_lottery', 'lottery_per_month_count', 'current_month_lotteries_count']

    def get_winners(self, obj):
        return LotteryWinnerSerializer(obj.due_data_winners.all() , many=True).data
    
    def get_list_members(self, obj: DepositDueDate):
        """
        Get a list of member objects for the deposit due date.
        
        Each object contains:
        - fullname: The member's full name
        - share_number: The share number (if applicable)
        
        For Poll type deposits:
        - Each member appears in the list based on their remaining shares
        - Share numbers start after the last won share
        - Members who have won all their shares don't appear in the list
        
        For other deposit types:
        - Returns a simple list of all active members without share numbers
        
        Returns:
            list: List of objects with member information
        """
        if obj.deposit.deposit_type == 'Poll':
            members_list = []
            active_members = obj.deposit.members.filter(is_active=True)
            
            for member in active_members:
                share_count = member.requested_unit_count or 0
                
                if share_count > 0:
                    win_count = obj.due_data_winners.filter(deposit_membership=member).count()
                    remaining_shares = max(0, share_count - win_count)
                    
                    for i in range(1, remaining_shares + 1):
                        share_number = win_count + i
                        
                        # Create an object with fullname and share_number
                        member_obj = {
                            'fullname': member.user.fullname,
                            'share_number': share_number if share_count > 1 or win_count > 0 else None
                        }
                        members_list.append(member_obj)
            
            return members_list
        else:
            members_list = []
            active_members = obj.deposit.members.filter(is_active=True)
            
            for member in active_members:
                member_obj = {
                    'fullname': member.user.fullname,
                    'share_number': None
                }
                members_list.append(member_obj)
            
            return members_list

    def get_can_perform_lottery(self, obj):
        """بررسی اینکه آیا می‌توان قرعه‌کشی انجام داد یا خیر"""
        return obj.deposit.can_perform_lottery_this_month()

    def get_lottery_per_month_count(self, obj):
        """تعداد قرعه‌کشی مجاز در ماه"""
        return obj.deposit.lottery_per_month_count or 0

    def get_current_month_lotteries_count(self, obj):
        """تعداد قرعه‌کشی‌های انجام شده در ماه جاری"""
        from datetime import datetime
        current_month = datetime.now().month
        current_year = datetime.now().year

        return DepositLottery.objects.filter(
            deposit=obj.deposit,
            created_at__month=current_month,
            created_at__year=current_year
        ).count()

    def get_members(self, obj: DepositDueDate):
        """
        Get a list of members for the deposit due date.
        
        For Poll type deposits:
        - Each member appears in the list based on their remaining shares (total shares minus won shares)
        - If a member has only one share and hasn't won yet, their name appears without a share number
        - If a member has multiple shares or has already won, their name appears with share numbers
        - Share numbers start after the last won share (e.g., if share 1 won, remaining shares start from 2)
        - Members who have won all their shares don't appear in the list
        
        For other deposit types:
        - Returns a simple list of all active members' names
        
        Args:
            obj (DepositDueDate): The deposit due date object
            
        Returns:
            list: List of member names, formatted according to the rules above
        """
        # بررسی نوع صندوق
        if obj.deposit.deposit_type == 'Poll':
            members_list = []
            active_members = obj.deposit.members.filter(is_active=True)
            
            for member in active_members:
                share_count = member.requested_unit_count or 0
                
                # فقط کاربرانی که سهم دارند در قرعه‌کشی شرکت می‌کنند
                if share_count > 0:
                    win_count = obj.due_data_winners.filter(deposit_membership=member).count()
                    
                    remaining_shares = max(0, share_count - win_count)
                    
                    # اضافه کردن نام کاربر به تعداد سهم‌های باقی‌مانده به لیست
                    for i in range(1, remaining_shares + 1):
                        # شماره سهم را از شماره بعد از آخرین سهم برنده شده شروع می‌کنیم
                        share_number = win_count + i
                        
                        # اگر کاربر فقط یک سهم داشته باشد و هنوز برنده نشده باشد، بدون شماره سهم نمایش می‌دهیم
                        if share_count == 1 and win_count == 0:
                            members_list.append(member.user.fullname)
                        else:
                            members_list.append(f"{member.user.fullname}({share_number})")
                    
            return members_list
        else:
            members = obj.deposit.members.filter(
                # role__in=[DepositMembership.Role.ADMIN],
                is_active=True
            ).values_list('user__fullname', flat=True)
            return list(members)
    
    
class LotteryWinnerDeleteSerializer(serializers.Serializer):
    winner_id = serializers.IntegerField()
    deposit_id = serializers.IntegerField()
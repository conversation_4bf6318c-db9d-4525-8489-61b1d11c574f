from django.db import models
from django.utils.translation import gettext_lazy as _


class DepositLottery(models.Model):
    deposit = models.ForeignKey(
        "deposit.Deposit", 
        on_delete=models.CASCADE, 
        verbose_name=_("Deposit"), 
        related_name="deposit_winners"
    )
    deposit_membership = models.ForeignKey(
        'deposit.DepositMembership', 
        on_delete=models.CASCADE, 
        verbose_name=_("Deposit Membership"), 
        related_name="winners"
    )  
    due_date = models.ForeignKey(
        'deposit.DepositDueDate', 
        on_delete=models.CASCADE, 
        verbose_name=_("Due Date"), 
        related_name="due_data_winners"
    )  
    lottery_number = models.IntegerField(verbose_name=_("Lottery Number"))  

    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))  

    class Meta:
        verbose_name = _("Deposit Lotttery")
        verbose_name_plural = _("Deposit Lotteries")
        unique_together = ('due_date', 'lottery_number')  
        
    def __str__(self):
        return f"Deposit Lottery for {self.deposit.title} - {self.deposit_membership.user.fullname}"


    def save(self, *args, **kwargs):
        if not self.pk:  # فقط برای رکوردهای جدید
            last_lottery = DepositLottery.objects.filter(due_date=self.due_date).order_by('-lottery_number').first()
            self.lottery_number = last_lottery.lottery_number + 1 if last_lottery else 1
        super().save(*args, **kwargs)
        

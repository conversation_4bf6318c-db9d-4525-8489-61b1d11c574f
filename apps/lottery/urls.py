


from django.urls import path
from . import views


urlpatterns = [
    path('deposit/<int:deposit_id>/winners/', views.DepositWinnersListView.as_view(), name='deposit-winners-list'),
    path('deposit/<int:deposit_id>/lottery/create/', views.DepositLotteryCreateView.as_view(), name='deposit-lottery-create'),
    path('deposit/<int:deposit_id>/lottery/info/', views.DepositLotteryInfoView.as_view(), name='deposit-lottery-info'),
    path(
        'deposit/<int:deposit_id>/lottery/<int:winner_id>/delete/',
        views.DepositLotteryDeleteView.as_view(),
        name='deposit-lottery-delete'
    ),
    path(
        'deposit/<int:deposit_id>/lottery/end/',
        views.DepositLotteryEndedView.as_view(),
        name='deposit-lottery-end'
    ),
]

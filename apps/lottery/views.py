import random
from django.utils import timezone
from django.shortcuts import get_object_or_404

from rest_framework.generics import ListAPIView, CreateAPIView, RetrieveAPIView, GenericAPIView
from django.db.models import Q, Count
from django.utils.translation import gettext_lazy as _
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from rest_framework.response import Response
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from datetime import datetime, timedelta

from apps.deposit.doc import *
from apps.lottery.serializers import DepositLotterySerializer, DepositLotteryInfoSerializer, LotteryWinnerDeleteSerializer
from apps.lottery.models import DepositLottery
from apps.deposit.models import Deposit, DepositMembership, DepositDueDate

from apps.transaction.models import Transaction
from utils.exceptions import AppAPIException



class DepositLotteryInfoView(RetrieveAPIView):
    queryset = Deposit.objects.all()
    serializer_class = DepositLotteryInfoSerializer
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_summary="deposit lottery",
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_object(self):
        deposit_id = self.kwargs.get('deposit_id')
        deposit = get_object_or_404(Deposit, id=deposit_id)
        
        # Get the first due date where is_lottery_completed is False, ordered by due_date_number
        current_due_date = DepositDueDate.get_due_date_lottery(deposit)
        
        # If all lotteries are completed, get the last due date instead of raising an error
        if not current_due_date:
            current_due_date = deposit.due_dates.order_by('-due_date_number').first()
            
            if not current_due_date:
                raise AppAPIException({"message": "No due dates found for this deposit"}, status_code=status.HTTP_400_BAD_REQUEST)
        
        return current_due_date
    

class DepositWinnersListView(ListAPIView):
    serializer_class = DepositLotterySerializer

    @swagger_auto_schema(
        operation_summary="deposit lottery",
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_object(self):
        deposit_id = self.kwargs['deposit_id']
        try:
            deposit = Deposit.objects.get(id=deposit_id)
        except Deposit.DoesNotExist:
            raise Http404
        return deposit

    def get_queryset(self):
        return DepositLottery.objects.filter(deposit=self.get_object()) 
    
    
    
class DepositLotteryCreateView(CreateAPIView):
    # serializer_class = DepositLotterySerializer
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_summary="deposit lottery",
    )

    def post(self, request, *args, **kwargs):
        deposit_id = self.kwargs.get('deposit_id')
        deposit = Deposit.objects.filter(id=deposit_id).first()
        if not deposit:
            raise AppAPIException({"message": "Deposit not found"}, status_code=status.HTTP_404_NOT_FOUND)

        # بررسی محدودیت تعداد قرعه‌کشی در ماه
        if not deposit.can_perform_lottery_this_month():
            raise AppAPIException({
                "message": f"Maximum {deposit.lottery_per_month_count} lotteries allowed per month. Limit reached for this month."
            }, status_code=status.HTTP_400_BAD_REQUEST)

        user = request.user
        
        # Get the first due date where is_lottery_completed is False, ordered by due_date_number
        current_due_date = DepositDueDate.get_due_date_lottery(deposit)
        
        # If all lotteries are completed, get the last due date instead of raising an error
        if not current_due_date:
            current_due_date = deposit.due_dates.order_by('-due_date_number').first()
            
            if not current_due_date:
                raise AppAPIException({"message": "No due dates found for this deposit"}, status_code=status.HTTP_400_BAD_REQUEST)
        membership = DepositMembership.objects.filter(deposit=deposit, user=user).first()
        if not membership or membership.role not in [DepositMembership.Role.OWNER, DepositMembership.Role.ADMIN]:
            raise AppAPIException({"message": "You are not a member of this deposit"}, status_code=status.HTTP_403_FORBIDDEN)
        
        # Get eligible members based on the same logic as in DepositLotteryInfoSerializer.get_list_members
        eligible_members = []
        
        active_members = deposit.members.filter(is_active=True)
        
        for member in active_members:
            share_count = member.requested_unit_count or 0
            
            if share_count > 0:
                win_count = current_due_date.due_data_winners.filter(deposit_membership=member).count()
                remaining_shares = max(0, share_count - win_count)
                
                # Add the member to eligible_members based on their remaining shares
                if remaining_shares > 0:
                    # Add the member multiple times based on remaining shares for weighted selection
                    for _ in range(remaining_shares):
                        eligible_members.append(member)
        
        if not eligible_members:
            raise AppAPIException({"message": "No eligible members found for this lottery"}, status_code=status.HTTP_400_BAD_REQUEST)
        
        # Select a random winner from eligible members
        winner = random.choice(eligible_members)
        
        # Find the highest lottery_number for this due_date to avoid unique constraint error
        last_lottery = DepositLottery.objects.filter(due_date=current_due_date).order_by('-lottery_number').first()
        lottery_number = (last_lottery.lottery_number + 1) if last_lottery else 1
        
        lottery = DepositLottery.objects.create(
            deposit=deposit,
            deposit_membership=winner,
            due_date=current_due_date,
            lottery_number=lottery_number
        )
        
        # Send notification to all deposit members
        from apps.account.services import NotificationService
        NotificationService.send_lottery_announcement_to_all_members(
            deposit_id=deposit.id
        )
        
        # Send special notification to the winner
        NotificationService.send_lottery_winner_notification(
            deposit_id=deposit.id,
            winner_user_id=winner.user.id
        )
        
        serializer = DepositLotterySerializer(lottery)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


    
    
    
class DepositLotteryDeleteView(GenericAPIView):
    serializer_class = LotteryWinnerDeleteSerializer
    permission_classes = [IsAuthenticated]

    def delete(self, request, deposit_id, winner_id):
        deposit = get_object_or_404(Deposit, id=deposit_id)
                
        lottery_winner = get_object_or_404(
            DepositLottery,
            id=winner_id,
            deposit=deposit
        )

        lottery_winner.delete()

        return Response(
            {"detail": "Winner successfully deleted"},
            status=status.HTTP_204_NO_CONTENT
        )
        

class DepositLotteryEndedView(GenericAPIView):
    """
    View to mark a deposit lottery as completed by setting is_lottery_completed to True
    for the current due date.
    """
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_summary="Mark deposit lottery as completed",
        responses={
            200: openapi.Response(
                description="Lottery marked as completed successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                    }
                )
            ),
            400: openapi.Response(description="Bad request"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not found"),
        }
    )
    def post(self, request, deposit_id):
        deposit = get_object_or_404(Deposit, id=deposit_id)
        
        # Check if user has permission (owner or admin)
        user = request.user
        membership = DepositMembership.objects.filter(deposit=deposit, user=user).first()
        if not membership or membership.role not in [DepositMembership.Role.OWNER, DepositMembership.Role.ADMIN]:
            raise AppAPIException(
                {"message": "You don't have permission to mark this lottery as completed"}, 
                status_code=status.HTTP_403_FORBIDDEN
            )
        
        current_due_date = DepositDueDate.get_due_date_lottery(deposit)
        
        if current_due_date:
            current_due_date.is_lottery_completed = True
            current_due_date.save(update_fields=['is_lottery_completed'])
            
            # Send notification to all deposit members
            from apps.account.services import NotificationService
            NotificationService.send_lottery_announcement_to_all_members(
                deposit_id=deposit.id
            )
        
        # Return success response even if no due date was found
        return Response(
            {"message": "Lottery marked as completed successfully"},
            status=status.HTTP_200_OK
        )

# Generated by Django 3.2.4 on 2025-03-09 14:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('deposit', '0008_delete_depositlottery'),
    ]

    operations = [
        migrations.CreateModel(
            name='DepositLottery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lottery_number', models.IntegerField(verbose_name='Lottery Number')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('deposit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deposit_winners', to='deposit.deposit', verbose_name='Deposit')),
                ('deposit_membership', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='winners', to='deposit.depositmembership', verbose_name='Deposit Membership')),
                ('due_date', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='due_data_winners', to='deposit.depositduedate', verbose_name='Due Date')),
            ],
            options={
                'verbose_name': 'Deposit Lotttery',
                'verbose_name_plural': 'Deposit Lotteries',
                'unique_together': {('due_date', 'lottery_number')},
            },
        ),
    ]

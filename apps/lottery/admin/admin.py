from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.utils.html import format_html
from django.templatetags.static import static
from django.db.models import Q

from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)
from unfold.decorators import display, action

from apps.lottery.models import DepositLottery
from utils.admin import project_admin_site
from utils.date_utils import format_jalali_date


class DepositLotteryAdmin(ModelAdmin):
    """Admin for deposit lotteries using Django Unfold"""
    list_display = (
        'lottery_info', 'winner_info', 'deposit_info', 'due_date_info', 'lottery_number', 'formatted_created_at'
    )
    list_filter = (
        'deposit',
        'due_date',
    )
    readonly_fields = ('created_at', 'lottery_number')
    search_fields = (
        'deposit__title', 
        'deposit_membership__user__fullname',
        'deposit_membership__user__phone_number',
        'due_date__due_date_number'
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    # autocomplete_fields = ('deposit', 'deposit_membership', 'due_date')
    list_per_page = 10
    fieldsets = (
        (None, {
            "fields": (("deposit", "deposit_membership"),)
        }),
        (
            _("Lottery Information"),
            {
                "fields": ("due_date", "lottery_number"),
                "classes": ["tab"],
            },
        ),
        (
            _('System Information'), {
                'fields': ('created_at',),
                "classes": ["tab"],
            }
        ),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'deposit', 
            'deposit_membership__user', 
            'due_date'
        )
    
    @display(description=_('Lottery'), header=True)
    def lottery_info(self, obj):
        # Get an appropriate icon for lottery        
        return [
            f"{_('Lottery')} #{obj.lottery_number}",
        ]
    
    @display(description=_('Winner'), header=True)
    def winner_info(self, obj):        
        return [
            obj.deposit_membership.user.fullname,
            str(obj.deposit_membership.user.phone_number) if obj.deposit_membership.user.phone_number else "",
            None,  # No initials
        ]
    
    @display(description=_('Deposit'))
    def deposit_info(self, obj):
        return obj.deposit.title if obj.deposit else "-"
    
    @display(description=_('Due Date'))
    def due_date_info(self, obj):
        return f"{_('Due Date')} #{obj.due_date.due_date_number}" if obj.due_date else "-"
    
    @display(description=_('Lottery Number'))
    def lottery_number(self, obj):
        return obj.lottery_number
    
    @display(description=_('Created At'))
    def formatted_created_at(self, obj):
        return format_jalali_date(obj.created_at, "%Y/%m/%d %H:%M") if obj.created_at else "-"


# Register models with the project_admin_site
project_admin_site.register(DepositLottery, DepositLotteryAdmin)

# Register with default admin site
# admin.site.register(DepositLottery, DepositLotteryAdmin)
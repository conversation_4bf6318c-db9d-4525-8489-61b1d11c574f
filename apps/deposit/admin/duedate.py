from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.admin import AjaxDatatable

from apps.deposit.models import DepositDueDate

@admin.register(DepositDueDate)
class DepositDueDateAdmin(AjaxDatatable):
    list_display = (
        'deposit', 
        'due_date_number', 
        'due_date', 
        'is_completed', 
        'formatted_deposit_title'
    )
    list_filter = ('is_completed', 'deposit')
    search_fields = ('deposit__title', 'due_date_number')
    raw_id_fields = ('deposit',)
    date_hierarchy = 'due_date'
    ordering = ('-due_date',)
    list_editable = ('is_completed',)

    fieldsets = (
        (None, {
            'fields': ('deposit', 'due_date_number', 'due_date', 'is_completed')
        }),
    )

    @admin.display(description=_('Deposit Title'))
    def formatted_deposit_title(self, obj):
        return obj.deposit.title
import json
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from django.utils.html import format_html
from django.db.models import Sum, Count, F, Q, Case, When, Value, IntegerField
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponseRedirect
from django.views.generic import TemplateView

from unfold.views import UnfoldModelAdminViewMixin

from apps.deposit.models import (
    Deposit, DepositMembership, PollDeposit, SavingDeposit, ReportingDeposit, 
    DepositDueDate, DepositMedia, DepositMediaImage
)
from apps.transaction.models import Transaction
from utils import gregorian_to_jalali, format_jalali_date
from decimal import Decimal


class DepositDetailBaseView(UnfoldModelAdminViewMixin, TemplateView):
    """Base class for deposit detail views using UnfoldModelAdminViewMixin"""
    permission_required = ('deposit.view_deposit',)
    
    def get_ordered_members(self, deposit):
        """
        Get deposit members ordered by role (Owner, Admin, Member)
        
        Args:
            deposit: The deposit object
            
        Returns:
            QuerySet: Ordered members by role
        """
        # Order by role: first Owner, then Admin, then Member
        return DepositMembership.objects.filter(deposit=deposit).order_by(
            Case(
                When(role=DepositMembership.Role.OWNER, then=Value(1)),
                When(role=DepositMembership.Role.ADMIN, then=Value(2)),
                When(role=DepositMembership.Role.MEMBER, then=Value(3)),
                default=Value(4),
                output_field=IntegerField(),
            )
        )
    
    def prepare_members_table_data(self, members):
        """
        Prepare members data for the Unfold table component
        
        Args:
            members: QuerySet of deposit members
            
        Returns:
            dict: Table data for members
        """
        members_table_data = {
            "headers": [_("Member"), _("Status"), _("Role"), _("Shares"), _("Monthly Installment"), _("Actions")],
            "rows": [],
            "row_classes": []  # Add a new list to store row-specific classes
        }
        
        for member in members:
            # Get member data
            fullname = member.user.get_full_name() if hasattr(member.user, 'get_full_name') else member.user.fullname
            status = _("Active") if member.is_active else _("Inactive")
            role = member.get_role_display() if hasattr(member, 'get_role_display') else str(member.role)
            shares = str(member.requested_unit_count) if member.requested_unit_count is not None else "0"
            installment = f"{member.monthly_installment_amount:,}" if member.monthly_installment_amount is not None else "0"
            
            # Add the row to the table data
            members_table_data["rows"].append([
                fullname,
                status,
                role,
                shares,
                installment,   
                _("View")  # Simple action text
            ])
            
            # Add a class for the row (can be customized in subclasses)
            members_table_data["row_classes"].append("")
            
        return members_table_data
    
    def prepare_due_dates_table_data(self, obj, due_dates):
        """
        Prepare due dates data for the Unfold table component
        
        Args:
            obj: The deposit object
            due_dates: QuerySet of due dates
            
        Returns:
            dict: Table data for due dates
        """
        due_dates_table_data = {
            "headers": [_("Due Date Number"), _("Due Date"), _("Status"), _("Total Deposits"), _("Paid Members")],
            "rows": [],
            "row_classes": []
        }
        
        current_due_date = obj.get_current_due_date()
        # print(f'--current_due_date-> / {due_dates.count()}')
        
        for due_date in due_dates:
            # Format the due date in Jalali calendar
            jalali_date = gregorian_to_jalali(due_date.due_date, date_format="%Y/%m/%d")
            # Set status based on whether this is the current due date
            if current_due_date and due_date.id == current_due_date.id:
                status = _("Current")
            elif due_date.is_completed:
                status = _("Completed")
            else:
                status = _("Pending")
            
            # Calculate total deposits for this due date
            total_deposits = Transaction.get_due_date_balance(obj, due_date)
            
            # Format the amount with commas for better readability
            formatted_amount = f"{int(total_deposits):,} {_('Toman')}" if total_deposits else f"0 {_('Toman')}"
            
            # Calculate the number of unique users who have made successful transactions for this due date
            paid_members_count = Transaction.objects.filter(
                deposit=obj,
                due_date=due_date,
                status=Transaction.TransactionStatus.SUCCESS,
                transaction_type=Transaction.TransactionType.INCOME
            ).values('user').distinct().count()
            
            # Format the paid members count with total members
            total_active_members = obj.members.filter(is_active=True).count()
            paid_members_text = f"{paid_members_count}/{total_active_members}"
            
            # Add the row to the table data
            due_dates_table_data["rows"].append([
                str(due_date.due_date_number),
                jalali_date,
                status,
                formatted_amount,
                paid_members_text
            ])
            
            # Add a class based on whether this is the current due date
            if current_due_date and due_date.id == current_due_date.id:
                due_dates_table_data["row_classes"].append("completed-row")
            elif due_date.is_completed:
                due_dates_table_data["row_classes"].append("row")
            else:
                due_dates_table_data["row_classes"].append("row")
                
        return due_dates_table_data
    
    def get_due_date(self, obj, format_type="numeric"):
        """
        Get the next due date in Jalali (Persian) calendar format.
        
        Args:
            obj: The deposit object
            format_type (str): The format type to return
                - "numeric": Returns date in YYYY/MM/DD format (default)
                - "text": Returns date in DD Month_Name YYYY format
        
        Returns:
            str: Formatted Jalali date or None if no due date exists
        """
        obj.disable_previous_month_due_dates()
        # دریافت نزدیک‌ترین تاریخ قرعه‌کشی که تکمیل نشده است
        next_due_date = DepositDueDate.objects.filter(
            deposit=obj, 
            is_completed=False
        ).order_by('due_date').first()
        
        if not next_due_date:
            return None
        
        # تبدیل تاریخ میلادی به شمسی
        due_date = next_due_date.due_date
        
        if format_type == "numeric":
            # فرمت عددی: 1402/01/15
            return gregorian_to_jalali(due_date, date_format="%Y/%m/%d")
        else:
            # فرمت متنی: 15 فروردین 1402
            return format_jalali_date(due_date)
    
    def get_balance(self, obj):
        
        return Transaction.get_total_balance(obj)

    def get_base_context_data(self, obj, members):
        """
        Get base context data common to all deposit types
        
        Args:
            obj: The deposit object
            members: QuerySet of deposit members
            
        Returns:
            dict: Base context data
        """
        # Prepare members data for the Unfold table component
        members_table_data = self.prepare_members_table_data(members)
        
        # Prepare due dates data for the Unfold table component
        due_dates = obj.due_dates.all().order_by('due_date_number')
        due_dates_table_data = self.prepare_due_dates_table_data(obj, due_dates)
        
        # Return common context data for all deposit types
        return {
            'original': obj,
            'deposit_title': obj.title,
            'deposit_owner': obj.owner.fullname,
            'deposit_region': obj.region,
            'deposit_members': members,
            'members_table_data': members_table_data,
            'due_dates_table_data': due_dates_table_data,
            'has_change_permission': self.model_admin.has_change_permission(self.request, obj),
            'has_delete_permission': self.model_admin.has_delete_permission(self.request, obj),
        }
    
    def get_context_data(self, **kwargs):
        """
        Get context data for the template
        
        This method should be implemented by subclasses to add deposit-type specific context
        """
        context = super().get_context_data(**kwargs)
        object_id = self.kwargs.get('object_id')
        obj = get_object_or_404(self.model_admin.model, pk=object_id)
        
        # Get related data
        members = self.get_ordered_members(obj).select_related('user')
        
        # Get base context data
        base_context = self.get_base_context_data(obj, members)
        
        context.update(base_context)
        
        return context


class PollDepositDetailView(DepositDetailBaseView):
    """Custom detail view for PollDeposit"""
    title = _("Poll Deposit Details")
    permission_required = ('deposit.view_polldeposit',)
    template_name = "admin/deposit/detail_views/poll_deposit_detail_view.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        object_id = self.kwargs.get('object_id')
        obj = get_object_or_404(self.model_admin.model, pk=object_id)
        
        # Add Poll-specific context data
        context.update({
            'amount_per_share': f'{int(obj.unit_amount):,} {_("Toman")}',
            'due_date': self.get_due_date(obj, format_type="numeric"),
            'regin_name': f"{obj.region.name}",
            'deposit_due_dates': obj.due_dates.all(),
            'total_debt_amount': f'{int(obj.total_debt_amount):,} {_("Toman")}' if obj.total_debt_amount else 0, 
            'payment_cycle': f'{obj.payment_cycle} {_("Every Month")}', 
            'number_of_lottery': obj.deposit_winners.count(),
            'change_form_url': reverse(
                'admin:deposit_polldeposit_original_change',
                args=[obj.pk],
                current_app=self.model_admin.admin_site.name
            ),
            'tickets_url': reverse(
                'admin:ticket_ticket_changelist'
            ) + f'?deposit__id__exact={obj.pk}',
            'medias_url': reverse(
                'admin:deposit_depositmedia_changelist'
            ) + f'?deposit__id__exact={obj.pk}',
            'lottery_url': reverse(
                'admin:lottery_depositlottery_changelist',
            ) + f'?deposit__id__exact={obj.pk}',
            'request_join_url': reverse(
                'admin:request_requestcreatedeposit_changelist',
            ) + f'?deposit__id__exact={obj.pk}',
            'payment_url': reverse(
                'admin:payment_payment_changelist',
            ) + f'?deposit__id__exact={obj.pk}',            
            'transaction_url': reverse(
                'admin:transaction_transaction_changelist',
            ) + f'?deposit__id__exact={obj.pk}',                
        })
        
        return context


class ReportingDepositDetailView(DepositDetailBaseView):
    """Custom detail view for ReportingDeposit"""
    title = _("Reporting Deposit Details")
    permission_required = ('deposit.view_reportingdeposit',)
    template_name = "admin/deposit/detail_views/reporting_deposit_detail_view.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        object_id = self.kwargs.get('object_id')
        obj = get_object_or_404(self.model_admin.model, pk=object_id)
        # Add Reporting-specific context data
        context.update({
            'amount_per_share': f'{int(obj.unit_amount):,} {_("Toman")}',
            'due_date': self.get_due_date(obj, format_type="numeric"),
            'deposit_balance':f' {int(self.get_balance(obj)):,} {_("Toman")}' if self.get_balance(obj) else 0, 
            'regin_name': f"{obj.region.name}",
            'deposit_due_dates': obj.due_dates.all(),
            'payment_cycle': f'{obj.payment_cycle} {_("Every Month")}', 
            'total_paid_in': f'{int(self.get_total_income(obj)):,} {_("Toman")}' if self.get_total_income(obj) else 0, 
            'validity_duration': f'{obj.payment_cycle} {_("year")}' if obj.payment_cycle else "None",  
            # 'start_date': f'{gregorian_to_jalali(obj.start_date, date_format="%Y/%m/%d")}',
            'change_form_url': reverse( 
                'admin:deposit_reportingdeposit_original_change',
                args=[obj.pk],
                current_app=self.model_admin.admin_site.name
            ),            
            'tickets_url': reverse(
                'admin:ticket_ticket_changelist'
            ) + f'?deposit__id__exact={obj.pk}',
            
            'medias_url': reverse(
                'admin:deposit_depositmedia_changelist'
            ) + f'?deposit__id__exact={obj.pk}',
            'voting_url': reverse(
                'admin:voting_votingreport_changelist'
            ) + f'?deposit__id__exact={obj.pk}',            
            'request_join_url': reverse(
                'admin:request_requestcreatedeposit_changelist',
            ) + f'?deposit__id__exact={obj.pk}',
            'payment_url': reverse(
                'admin:payment_payment_changelist',
            ) + f'?deposit__id__exact={obj.pk}',            
            'transaction_url': reverse(
                'admin:transaction_transaction_changelist',
            ) + f'?deposit__id__exact={obj.pk}',                            
        })
        
        return context
    
    def get_total_income(self, obj):
        from apps.transaction.models import Transaction
        
        return Transaction.get_total_deposits(obj)


class SavingDepositDetailView(DepositDetailBaseView):
    """Custom detail view for SavingDeposit"""
    title = _("Saving Deposit Details")
    permission_required = ('deposit.view_savingdeposit',)
    template_name = "admin/deposit/detail_views/saving_deposit_detail_view.html"
    
    def prepare_loan_members_table_data(self, obj):
        """
        Prepare loan members data for the Unfold table component
        
        Args:
            obj: The deposit object
            
        Returns:
            dict: Table data for loan members
        """
        from apps.loan.models import Loan, LoanInstallment
        from django.db.models import Sum
        
        loan_members_table_data = {
            "headers": [
                _("Member Name"), 
                _("Loan Amount"), 
                _("Installment Count"), 
                _("Paid Installments"),
                _("Total Paid Amount"),
                _("Status"), 
                _("Created Date"),
            ],
            "rows": [],
            "row_classes": []
        }
        
        # Get all loans for this deposit
        loans = Loan.objects.filter(deposit=obj).select_related('deposit_membership__user')
        
        for loan in loans:
            # Get member's full name
            member = loan.deposit_membership.user
            fullname = member.get_full_name() if hasattr(member, 'get_full_name') else member.fullname
            
            # Format loan amount with commas
            formatted_amount = f"{int(loan.amount):,} {_('Toman')}"
            
            # Get loan status
            status = loan.get_status_display() if hasattr(loan, 'get_status_display') else str(loan.status)
            
            # Format creation date in Jalali calendar
            created_date = gregorian_to_jalali(loan.created_at, date_format="%Y/%m/%d")
            
            # Count paid installments
            total_installments = loan.installment_count
            paid_installments = loan.installments.filter(is_paid=True).count()
            installment_status = f"{paid_installments}/{total_installments}"
            
            # Calculate total paid amount
            total_paid_amount = loan.installments.filter(is_paid=True).aggregate(
                total=Sum('amount')
            )['total'] or 0
            
            # Format total paid amount with commas
            formatted_paid_amount = f"{int(total_paid_amount):,} {_('Toman')}"
            
            # Add the row to the table data
            loan_members_table_data["rows"].append([
                fullname,
                formatted_amount,
                str(total_installments),
                installment_status,
                formatted_paid_amount,
                status,
                created_date,
            ])
            
            # Add a class for the row based on loan status
            if loan.status == Loan.LoanStatus.COMPLETED:
                loan_members_table_data["row_classes"].append("completed-row")
            elif loan.status == Loan.LoanStatus.OVERDUE:
                loan_members_table_data["row_classes"].append("overdue-row")
            else:
                loan_members_table_data["row_classes"].append("row")
                
        return loan_members_table_data
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        object_id = self.kwargs.get('object_id')
        obj = get_object_or_404(self.model_admin.model, pk=object_id)
        # Add Reporting-specific context data
        context.update({
            'amount_per_share': f'{int(obj.unit_amount):,} {_("Toman")}',
            'deposit_balance':f' {int(self.get_balance(obj)):,} {_("Toman")}' if self.get_balance(obj) else 0, 
            'due_date': self.get_due_date(obj, format_type="numeric"),
            'regin_name': f"{obj.region.name}",
            'deposit_due_dates': obj.due_dates.all(),
            'payment_cycle': f'{obj.payment_cycle} {_("Every Month")}', 
            'validity_duration': f'{obj.validity_duration} {_("year")}' if obj.validity_duration else "None",  
            'start_date': f'{gregorian_to_jalali(obj.start_date, date_format="%Y/%m/%d")}',
            'loan_members_table_data': self.prepare_loan_members_table_data(obj),
            'change_form_url': reverse( 
                'admin:deposit_savingdeposit_original_change',
                args=[obj.pk],
                current_app=self.model_admin.admin_site.name
            ),
            'tickets_url': reverse(
                'admin:ticket_ticket_changelist'
            ) + f'?deposit__id__exact={obj.pk}',
            
            'medias_url': reverse(
                'admin:deposit_depositmedia_changelist'
            ) + f'?deposit__id__exact={obj.pk}',
            'request_loan_url': reverse(
                'admin:loan_loan_changelist'
            ) + f'?deposit__id__exact={obj.pk}',            

            'request_join_url': reverse(
                'admin:request_requestcreatedeposit_changelist',
            ) + f'?deposit__id__exact={obj.pk}',
            'payment_url': reverse(
                'admin:payment_payment_changelist',
            ) + f'?deposit__id__exact={obj.pk}',            
            'transaction_url': reverse(
                'admin:transaction_transaction_changelist',
            ) + f'?deposit__id__exact={obj.pk}',                            
            
        })
        
        return context
    
    def get_total_income(self, obj):
        from apps.transaction.models import Transaction
        
        return Transaction.get_total_deposits(obj)


from django.utils.translation import gettext_lazy as _
from django.contrib.admin import SimpleListFilter
from unfold.contrib.filters.admin import AutocompleteSelectFilter
from apps.deposit.models import DepositMembership
from apps.account.models import User
from django.db.models import Q


class MemberUserFilter(AutocompleteSelectFilter):
    """
    Filter deposits by member user
    Shows only deposits where the selected user is a member
    Displays user's fullname, email, and phone number in the dropdown
    """
    title = _('Member User')
    parameter_name = 'member_user'
    
    def lookups(self, request, model_admin):
        """
        Return a list of tuples (value, label) for the filter dropdown
        """
        # Get all users who are members of any deposit
        users = User.objects.filter(deposit_memberships__isnull=False).distinct()
        
        # Create options with user ID as value and formatted string as label
        choices = []
        for user in users:
            label_parts = []
            if user.fullname:
                label_parts.append(user.fullname)
            if user.email:
                label_parts.append(f"({user.email})")
            if user.phone_number:
                label_parts.append(f"- {user.phone_number}")
                
            label = " ".join(label_parts)
            choices.append((user.pk, label))
            
        return choices
    
    def queryset(self, request, queryset):
        if self.value():
            print(f'--====-:{type(queryset)}/-> {self.value()}')
            return queryset.filter(members__user=self.value()).distinct()
        return queryset
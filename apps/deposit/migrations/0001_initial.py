# Generated by Django 3.2.4 on 2025-01-26 16:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Deposit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('description', models.TextField(verbose_name='Deposit Description')),
                ('total_debt_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='Total Debt Amount')),
                ('lottery_month_count', models.IntegerField(blank=True, null=True, verbose_name='Number of Lottery Months')),
                ('unit_amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='Unit Shares Amount')),
                ('payment_cycle', models.IntegerField(verbose_name='Payment Cycle (in months)')),
                ('max_unit_per_request', models.IntegerField(verbose_name='Maximum Unit Shares Per Request')),
                ('max_members_count', models.IntegerField(verbose_name='Maximum Number of Members')),
                ('validity_duration', models.IntegerField(blank=True, null=True, verbose_name='Validity Duration (in months)')),
                ('initial_lottery_date', models.DateField(blank=True, null=True, verbose_name='Initial Lottery Date')),
                ('rules', models.JSONField(default=dict, verbose_name='Deposit Rules')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deposits', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
        ),
        migrations.CreateModel(
            name='DepositDueDate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('due_date_number', models.IntegerField(verbose_name='Due Date Number')),
                ('due_date', models.DateField(verbose_name='Due Date')),
                ('is_completed', models.BooleanField(default=False, verbose_name='Is Completed')),
                ('deposit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='due_dates', to='deposit.deposit', verbose_name='Deposit')),
            ],
            options={
                'verbose_name': 'Deposit Due Date',
                'verbose_name_plural': 'Deposit Due Dates',
                'unique_together': {('deposit', 'due_date_number')},
            },
        ),
        migrations.CreateModel(
            name='DepositMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('Owner', 'Owner'), ('Member', 'Member'), ('Admin', 'Admin')], default='Member', max_length=20, verbose_name='Role')),
                ('requested_unit_count', models.IntegerField(blank=True, help_text='The number of unit shares requested by the user.', null=True, verbose_name='Requested Unit Count')),
                ('monthly_installment_amount', models.DecimalField(blank=True, decimal_places=0, help_text='The monthly installment amount for the user.', max_digits=15, null=True, verbose_name='Monthly Installment Amount')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('joined_date', models.DateTimeField(auto_now_add=True, verbose_name='Joined Date')),
                ('deposit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='members', to='deposit.deposit', verbose_name='Deposit')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deposit_memberships', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Membership',
                'verbose_name_plural': 'Memberships',
                'unique_together': {('user', 'deposit')},
            },
        ),
        migrations.CreateModel(
            name='Ticket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=255, verbose_name='Subject')),
                ('description', models.TextField(verbose_name='Description')),
                ('status', models.CharField(choices=[('Open', 'Open'), ('In Progress', 'In Progress'), ('Closed', 'Closed')], default='Open', max_length=20, verbose_name='Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('response', models.TextField(blank=True, null=True, verbose_name='Response')),
                ('responded_at', models.DateTimeField(blank=True, null=True, verbose_name='Responded At')),
                ('deposit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tickets', to='deposit.deposit', verbose_name='Deposit')),
                ('responded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='responded_tickets', to='deposit.depositmembership', verbose_name='Responded By')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tickets', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Ticket',
                'verbose_name_plural': 'Tickets',
            },
        ),
        migrations.CreateModel(
            name='DepositWinner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('deposit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deposit_winners', to='deposit.deposit', verbose_name='Deposit')),
                ('deposit_membership', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='winners', to='deposit.depositmembership', verbose_name='Deposit Membership')),
                ('due_date', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='due_data_winners', to='deposit.depositduedate', verbose_name='Due Date')),
            ],
            options={
                'verbose_name': 'Deposit Winner',
                'verbose_name_plural': 'Deposit Winners',
            },
        ),
    ]

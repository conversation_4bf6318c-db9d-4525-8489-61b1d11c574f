# Generated by Django 3.2.4 on 2025-01-27 00:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
        ('region', '0001_initial'),
        ('deposit', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DepositMedia',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(help_text='The subject or title of the media.', max_length=255, verbose_name='Subject')),
                ('description', models.TextField(blank=True, help_text='A detailed description of the media.', null=True, verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='The date and time when the media was created.', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='The date and time when the media was last updated.', verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Deposit Media',
                'verbose_name_plural': 'Deposit Media',
            },
        ),
        migrations.AddField(
            model_name='deposit',
            name='region',
            field=models.ForeignKey(default=1, help_text='The region to which this deposit belongs.', on_delete=django.db.models.deletion.CASCADE, related_name='region_deposits', to='region.region', verbose_name='Region'),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='DepositMediaImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('priority', models.IntegerField(default=0, help_text='Priority of the image, lower values mean higher priority.', verbose_name='Priority')),
                ('deposit_media', models.ForeignKey(help_text='The media associated with this image.', on_delete=django.db.models.deletion.CASCADE, related_name='images', to='deposit.depositmedia', verbose_name='Deposit Media')),
                ('image', filer.fields.image.FilerImageField(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to=settings.FILER_IMAGE_MODEL, verbose_name='image')),
            ],
            options={
                'verbose_name': 'Merchant Image',
                'verbose_name_plural': 'Merchant Images',
                'ordering': ('priority',),
            },
        ),
        migrations.AddField(
            model_name='depositmedia',
            name='deposit',
            field=models.ForeignKey(help_text='The deposit associated with this media.', on_delete=django.db.models.deletion.CASCADE, related_name='media', to='deposit.deposit', verbose_name='Deposit'),
        ),
    ]

# Generated by Django 3.2.4 on 2025-01-28 13:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deposit', '0003_deposit_deposit_type'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='DepositWinner',
            new_name='DepositLottery',
        ),
        migrations.CreateModel(
            name='PollDeposit',
            fields=[
            ],
            options={
                'verbose_name': 'Poll Deposit',
                'verbose_name_plural': 'Poll Deposits',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('deposit.deposit',),
        ),
        migrations.CreateModel(
            name='ReportingDeposit',
            fields=[
            ],
            options={
                'verbose_name': 'Reporting Deposit',
                'verbose_name_plural': 'Reporting Deposits',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('deposit.deposit',),
        ),
        migrations.CreateModel(
            name='SavingDeposit',
            fields=[
            ],
            options={
                'verbose_name': 'Saving Deposit',
                'verbose_name_plural': 'Saving Deposits',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('deposit.deposit',),
        ),
        migrations.AlterModelOptions(
            name='depositlottery',
            options={'verbose_name': 'Deposit Lotttery', 'verbose_name_plural': 'Deposit Lotteries'},
        ),
        migrations.AddField(
            model_name='deposit',
            name='start_date',
            field=models.DateField(blank=True, null=True, verbose_name='Start Date'),
        ),
    ]

from django.db import models
from django.utils.translation import gettext_lazy as _


class DepositDueDate(models.Model):
    deposit = models.ForeignKey(
        "deposit.Deposit", 
        on_delete=models.CASCADE, 
        verbose_name=_("Deposit"), 
        related_name="due_dates"
    )
    due_date_number = models.IntegerField(verbose_name=_("Due Date Number"))  # شماره سررسید
    due_date = models.DateField(verbose_name=_("Due Date"))  # تاریخ سررسید
    is_completed = models.BooleanField(verbose_name=_("Is Completed"), default=False)  # آیا این سررسید تکمیل شده است؟
    is_lottery_completed = models.BooleanField(verbose_name=_("Is Lottery Completed"), default=False)  



    class Meta:
        verbose_name = _("Deposit Due Date")
        verbose_name_plural = _("Deposit Due Dates")
        unique_together = ("deposit", "due_date_number")  

    def __str__(self):
        return f"Due Date {self.due_date_number} - {self.deposit.title}"
        
    @classmethod
    def get_due_date_lottery(cls, deposit):
        """
        Get the first due date for a deposit where is_lottery_completed is False,
        ordered by due_date_number.
        
        Args:
            deposit: The deposit object to get the due date for
            
        Returns:
            DepositDueDate: The first due date with is_lottery_completed=False,
                           or None if all due dates have is_lottery_completed=True
        """
        return cls.objects.filter(
            deposit=deposit,
            is_lottery_completed=False
        ).order_by('due_date_number').first()
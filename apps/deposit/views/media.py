
from rest_framework.generics import ListAPIView, CreateAPIView, UpdateAPIView, DestroyAPIView, GenericAPIView
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.db.models import F

from apps.deposit.models import Deposit, DepositMedia, DepositMediaImage
from apps.deposit.serializers import DepositMediaListSerializer, DepositMediaSerializer, DepositMediaImageSerializer
from apps.deposit.doc import deposit_media_create_swagger, deposit_media_update_swagger, deposit_media_view_increment_swagger
from rest_framework import serializers



class DepositMediaAPIView(ListAPIView):
    serializer_class = DepositMediaListSerializer

    def get_queryset(self):
        deposit_id = self.kwargs.get('deposit_id')
        try:
            deposit = Deposit.objects.get(id=deposit_id)
            return DepositMedia.objects.filter(deposit=deposit).order_by('-id')
        except Deposit.DoesNotExist:
            return DepositMedia.objects.none()
        

class DepositMediaDeleteAPIView(DestroyAPIView):
    queryset = DepositMedia.objects.all()
    lookup_field = 'id'  # نام فیلد جستجو (مطابق با `deposit_media_id` در URL)
    lookup_url_kwarg = 'deposit_media_id'  # نام پارامتر URL

    def delete(self, request, *args, **kwargs):
        deposit_media = self.get_object()
        deposit_media.delete()
        return Response(
            {"message": "Deposit media and associated images deleted successfully."},
            status=status.HTTP_204_NO_CONTENT
        )        



class DepositMediaCreateAPIView(CreateAPIView):
    serializer_class = DepositMediaSerializer

    @deposit_media_create_swagger
    def post(self, request, *args, **kwargs):
        deposit_id = kwargs.get('deposit_id')
        deposit = Deposit.objects.get(id=deposit_id)
        
        media_data = request.data.copy()
        images_data = media_data.pop('images', None)

        deposit_media = DepositMedia.objects.create(deposit=deposit, **media_data)

        if images_data:
            image_serializer = DepositMediaImageSerializer(
                data=images_data, 
                many=True, 
                context=self.get_serializer_context()
            )
            image_serializer.is_valid(raise_exception=True)
            image_serializer.save(deposit_media=deposit_media)
        
        final_serializer = DepositMediaSerializer(deposit_media, context=self.get_serializer_context())
        return Response(final_serializer.data, status=status.HTTP_201_CREATED)

    def get_serializer_context(self):
        return {'request': self.request}
    
    
class DepositMediaUpdateAPIView(UpdateAPIView):

    @deposit_media_update_swagger
    def patch(self, request, *args, **kwargs):
        deposit_media_id = kwargs.get('deposit_media_id')
        deposit_media = get_object_or_404(DepositMedia, id=deposit_media_id)

        try:
            # Update deposit media fields
            media_data = request.data.copy()
            images_data = media_data.pop('images', None)

            for key, value in media_data.items():
                setattr(deposit_media, key, value)
            deposit_media.save()

        except Exception as e:
            return Response(
                {"detail": f"Error updating the deposit media: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Handle images update
        if images_data:
            try:
                for data in images_data:
                    image_instance = None

                    # Handle image deletion
                    if 'id' in data and data.get('image') == "delete":
                        try:
                            image_instance = get_object_or_404(DepositMediaImage, id=data['id'])
                            image_instance.image = None
                            image_instance.save()
                            data.pop('image')
                            continue  # Skip further processing for this entry
                        except DepositMediaImage.DoesNotExist:
                            return Response(
                                {"detail": "Image with the specified ID does not exist."},
                                status=status.HTTP_404_NOT_FOUND
                            )

                    # Validate and update/create image
                    image_serializer = DepositMediaImageSerializer(data=data, context=self.get_serializer_context())
                    image_serializer.is_valid(raise_exception=True)

                    if 'id' in data:
                        try:
                            image_instance = get_object_or_404(DepositMediaImage, id=data['id'])
                            image_serializer.update(image_instance, image_serializer.validated_data)
                        except DepositMediaImage.DoesNotExist:
                            return Response(
                                {"detail": "Image with the specified ID does not exist."},
                                status=status.HTTP_404_NOT_FOUND
                            )
                    else:
                        if data.get('image') is None:
                            continue

                        # Skip if the image is already uploaded
                        if '/static/uploads/' in str(data['image']):
                            continue

                        # Create a new image
                        image_serializer.save(deposit_media=deposit_media)

            except serializers.ValidationError as ve:
                return Response(
                    {"images_error": ve.detail},
                    status=status.HTTP_400_BAD_REQUEST
                )
            except Exception as e:
                return Response(
                    {"detail": f"Error processing images: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Return the updated deposit media
        try:
            final_serializer = DepositMediaSerializer(deposit_media, context=self.get_serializer_context())
            return Response(final_serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"detail": f"Error serializing the updated deposit media: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

    def get_serializer_context(self):
        return {'request': self.request}


class DepositMediaViewIncrementAPIView(GenericAPIView):
    """
    API view to increment the view count of a specific media
    """

    @deposit_media_view_increment_swagger
    def post(self, request, *args, **kwargs):
        """Increment view count for a specific media"""
        media_id = request.data.get('media_id')

        if not media_id:
            return Response(
                {"status": "error", "message": "media_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get the media object
            media = get_object_or_404(DepositMedia, id=media_id)

            # Increment view count using F() to avoid race conditions
            DepositMedia.objects.filter(id=media_id).update(view_count=F('view_count') + 1)

            # Refresh from database to get updated count
            media.refresh_from_db()

            return Response(
                {
                    "status": "success",
                    "message": "View count incremented successfully",
                    "media_id": media_id,
                    "current_view_count": media.view_count
                },
                status=status.HTTP_200_OK
            )

        except DepositMedia.DoesNotExist:
            return Response(
                {"status": "error", "message": "Media not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"status": "error", "message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
from decimal import Decimal
from rest_framework import serializers
from apps.deposit.models import Deposit, DepositMembership, DepositDueDate
from apps.transaction.models import Transaction

from utils import FileFieldSerializer, absolute_url


class DepositSerializer(serializers.ModelSerializer):
    deposit_type = serializers.ChoiceField(
        choices=Deposit.DepositType.choices,
        help_text="Type of deposit. Choices: Poll, Saving, Reporting."
    )
    is_user_member = serializers.SerializerMethodField()
    has_pending_request = serializers.SerializerMethodField()
    members_count = serializers.SerializerMethodField()
    balance = serializers.SerializerMethodField()

    due_date = serializers.SerializerMethodField()
    owner = serializers.SerializerMethodField()
    user_role = serializers.SerializerMethodField()

    due_date_balance = serializers.SerializerMethodField()
    completed_lotteries_count = serializers.SerializerMethodField()
    request_count = serializers.SerializerMethodField()
    loan_request_count = serializers.SerializerMethodField()
    total_income = serializers.SerializerMethodField()
    loan_installment_amount = serializers.SerializerMethodField()
    btn_disable_lottery = serializers.SerializerMethodField()
    can_create_invitation_link = serializers.SerializerMethodField()
    total_shares = serializers.SerializerMethodField()


    class Meta:
        model = Deposit
        fields = [
            'id', 'deposit_type', 'title', 'description', 'total_debt_amount', 'lottery_month_count', 'lottery_per_month_count', 'owner', 'user_role',
            'unit_amount', 'payment_cycle', 'max_unit_per_request', 'max_members_count','validity_duration', 'initial_lottery_date', 'completed_lotteries_count',
            'start_date', 'rules', 'is_active', 'created', 'updated', 'is_user_member', 'has_pending_request', 'members_count', 'balance', 'due_date', 'due_date_balance', 'request_count',
            'total_income', 'loan_installment_amount', 'btn_disable_lottery', 'loan_request_count', 'can_create_invitation_link', 'total_shares'
        ]
        read_only_fields = ['id', 'created', 'updated', 'is_active', 'is_user_member', 'has_pending_request']
        
        
    def get_completed_lotteries_count(self, obj):
        return obj.deposit_winners.count()

    def get_owner(self, obj):
        return f"{obj.owner.fullname}"

    def get_due_date_balance(self, obj):
        if current_due_date := DepositDueDate.objects.filter(
            deposit=obj,  
            is_completed=False  # دوره تکمیل نشده
        ).order_by('due_date').first():
            return Transaction.get_due_date_balance(obj, current_due_date)
        return Decimal('0.00')

            
    def get_balance(self, obj):
        return Transaction.get_total_balance(obj)
    
    def get_total_income(self, obj):
        """
        محاسبه مجموع واریزی‌های موفق یک صندوق
        """
        return Transaction.get_total_deposits(obj)

    def get_is_user_member(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return DepositMembership.objects.filter(
                deposit=obj,
                user=request.user,
                is_active=True
            ).exists()
        return False

    def get_has_pending_request(self, obj):
        from apps.request.models import RequestJoinDeposit

        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return RequestJoinDeposit.objects.filter(
                deposit=obj,
                user=request.user,
                status=RequestJoinDeposit.StatusChoices.PENDING
            ).exists()
        return False
        
    def get_members_count(self, obj):
        # تعداد اعضای فعال این صندوق را برمی‌گرداند
        return DepositMembership.objects.filter(
            deposit=obj,  # Deposit فعلی
            is_active=True  # عضویت فعال
        ).count()
        

    def get_due_date(self, obj):
        obj.disable_previous_month_due_dates()
        # دریافت نزدیک‌ترین تاریخ قرعه‌کشی که تکمیل نشده است
        next_due_date = DepositDueDate.objects.filter(
            deposit=obj,  # Deposit فعلی
            is_completed=False  # قرعه‌کشی تکمیل نشده
        ).order_by('due_date').first()  # اولین تاریخ نزدیک‌ترین به امروز
        
        if next_due_date:
            return next_due_date.due_date  # تاریخ قرعه‌کشی بعدی
        return None     
    
    def get_user_role(self, obj):
        request = self.context.get('request')  # دریافت request از context
        if request and request.user.is_authenticated:  # اگر کاربر لاگین کرده باشد
            membership = DepositMembership.objects.filter(
                deposit=obj,  # Deposit فعلی
                user=request.user,  # کاربر فعلی
                is_active=True  # عضویت فعال
            ).first()
            
            if membership:
                return membership.role  # نقش کاربر در این صندوق
        return None 
    
    def get_request_count(self, obj):
        from apps.request.models import RequestJoinDeposit, LoanRequest
        
        # تعداد درخواست‌های عضویت در حالت انتظار
        request_count = RequestJoinDeposit.objects.filter(
            deposit=obj,
            status=RequestJoinDeposit.StatusChoices.PENDING
        ).count()
        
        # اگر صندوق از نوع "Saving" بود، تعداد درخواست‌های وام را نیز اضافه کن
        if obj.deposit_type == Deposit.DepositType.SAVING:
            loan_request_count = LoanRequest.objects.filter(
                deposit=obj,
                status=LoanRequest.LoanStatus.PENDING
            ).count()
            request_count += loan_request_count
            
        return request_count
        
    def get_loan_request_count(self, obj):
        """
        تعداد درخواست‌های وام در حالت انتظار (PENDING) را برای صندوق‌های با نوع "Saving" برمی‌گرداند
        برای سایر انواع صندوق مقدار None را برمی‌گرداند
        """
        # اگر نوع صندوق "Saving" نباشد، مقدار None را برمی‌گرداند
        if obj.deposit_type != Deposit.DepositType.SAVING:
            return None
            
        from apps.request.models import LoanRequest
        loan_request_count = LoanRequest.objects.filter(
            deposit=obj,
            status=LoanRequest.LoanStatus.PENDING
        ).count()
        return loan_request_count

    def get_loan_installment_amount(self, obj):
        """
        Return the amount of the next unpaid loan installment for the current user
        if they have an active loan in a SAVING type deposit.
        If no unpaid installment is found or the deposit is not of SAVING type, return None.
        """
        # Check if deposit is of SAVING type
        if obj.deposit_type != Deposit.DepositType.SAVING:
            return None

        # Get the current user from the request context
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None

        # Get the user's membership in this deposit
        membership = obj.members.filter(
            user=request.user,
            is_active=True
        ).first()

        if not membership:
            return None

        # Import Loan and LoanInstallment models
        from apps.loan.models import Loan, LoanInstallment

        # Find active loans for this user in this deposit
        loans = Loan.objects.filter(
            deposit=obj,
            deposit_membership=membership,
            status=Loan.LoanStatus.ACTIVE
        )

        if not loans.exists():
            return None

        # For each loan, find the first unpaid installment ordered by due date
        from django.utils import timezone
        today = timezone.now().date()

        for loan in loans:
            # Find the first unpaid installment for this loan ordered by due date
            next_installment = LoanInstallment.objects.filter(
                loan=loan,
                is_paid=False
            ).order_by('due_date').first()

            if next_installment:
                return next_installment.amount

        return None
        
    def get_btn_disable_lottery(self, obj):
        """
        Return True if the lottery button should be disabled.
        Button is disabled if:
        1. All lotteries are completed (no more due dates with is_lottery_completed=False), OR
        2. Monthly lottery limit has been reached for current month

        Returns:
            bool: True if lottery button should be disabled, False otherwise
        """
        # بررسی اینکه آیا همه قرعه‌کشی‌ها تکمیل شده‌اند
        next_lottery_due_date = DepositDueDate.get_due_date_lottery(obj)

        # اگر هیچ سررسید قرعه‌کشی باقی نمانده، دکمه غیرفعال شود
        if next_lottery_due_date is None:
            return True

        # بررسی محدودیت ماهانه قرعه‌کشی (فقط برای صندوق‌های قرعه‌کشی)
        if obj.deposit_type == obj.DepositType.POLL:
            # اگر امکان قرعه‌کشی در این ماه وجود ندارد، دکمه غیرفعال شود
            if not obj.can_perform_lottery_this_month():
                return True

        # در غیر این صورت، دکمه فعال است
        return False

    def get_total_shares(self, obj):
        """
        نمایش تعداد کل سهم‌ها برای صندوق قرعه‌کشی
        """
        if obj.deposit_type == obj.DepositType.POLL:
            return obj.total_unit_amount
        return None

    def get_can_create_invitation_link(self, obj):
        """
        Returns True if the user can create invitation links for this deposit.
        User can create invitation links if:
        1. User is the owner of this deposit, OR
        2. User has ADMIN or OWNER role in this deposit membership
        """
        # Get the current user from the request context
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return False

        # Check if user is the owner of this deposit
        if obj.owner == request.user:
            return True

        # Check if user has ADMIN or OWNER role in this deposit membership
        membership = obj.members.filter(
            user=request.user,
            is_active=True,
            role__in=[DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER]
        ).exists()

        return membership


class DepositMembershipSerializer(serializers.ModelSerializer):
    avatar = FileFieldSerializer(required=False, source="user.avatar")
    fullname = serializers.CharField(source="user.fullname")
    payment_status = serializers.SerializerMethodField()
    user_id = serializers.IntegerField(source="user.id")

    class Meta:
        model = DepositMembership
        fields = ['id', 'user_id', 'fullname', 'avatar', 'role', 'payment_status', 'requested_unit_count', 'monthly_installment_amount', 'is_active', 'joined_date']
        
    def get_payment_status(self, obj):
        # اگر تاریخ سررسید نرسیده یا پرداخت انجام شده است
        # return None
        # اگر تاریخ سررسید رسیده و پرداخت انجام نشده است
        # return "yellow"
        # اگر ۳۰ روز از تاریخ سررسید گذشته باشد
        return "red"



   
class SetRoleSerializer(serializers.Serializer):
    role = serializers.ChoiceField(choices=DepositMembership.Role.choices)

    def validate_role(self, value):
        if value not in [role[0] for role in DepositMembership.Role.choices]:
            raise serializers.ValidationError("Invalid role.")
        elif value == "Owner":
            raise serializers.ValidationError("Invalid role.")        
        return value
    
class DepositTransactionSerializer(serializers.ModelSerializer):
    fullname = serializers.CharField(source="user.fullname")
    role = serializers.SerializerMethodField()
    class Meta:
        model = Transaction
        fields = ['id', 'fullname', 'role', 'amount', 'transaction_type', 'created_at']
        
    def get_role(self, obj):
        request = self.context.get('request')  # دریافت request از context
        if request and request.user.is_authenticated:  # اگر کاربر لاگین کرده باشد
            membership = DepositMembership.objects.filter(
                deposit=obj.deposit,  # Deposit فعلی
                user=request.user,  # کاربر فعلی
                is_active=True  # عضویت فعال
            ).first()

            if membership:
                return membership.role  # نقش کاربر در این صندوق
        return None



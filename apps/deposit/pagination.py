

from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response

        
class NoPagination(PageNumberPagination):
    def paginate_queryset(self, queryset, request, view=None):
        # Override to return all items instead of paginated ones
        self.count = len(queryset)
        self.request = request
        self.page = None
        self.page_size = len(queryset)
        return list(queryset)

    def get_paginated_response(self, data):
        # Keep the structure but include all results
        return Response({
            'count': self.count,
            'next': None,  # No next page
            'previous': None,  # No previous page
            'results': data,
        })
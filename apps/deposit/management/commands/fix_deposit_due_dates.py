from django.core.management.base import BaseCommand
from django.db import transaction
from apps.deposit.models import Deposit, DepositDueDate
from datetime import datetime
import jdatetime
from django.db.models import Min
import json
import os


class Command(BaseCommand):
    help = 'اصلاح تاریخ‌های سررسید صندوق‌های موجود بر اساس منطق سیگنال‌های فعلی'

    def add_arguments(self, parser):
        parser.add_argument(
            '--deposit_id',
            type=int,
            help='شناسه صندوق خاص برای اصلاح (اختیاری)'
        )
        parser.add_argument(
            '--deposit_type',
            type=str,
            choices=['Poll', 'Saving', 'Reporting'],
            help='نوع صندوق برای اصلاح (اختیاری)'
        )
        parser.add_argument(
            '--max-due-dates',
            type=int,
            default=0,
            help='حداکثر تعداد سررسیدهایی که باید ایجاد شوند (0 برای بدون محدودیت)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='اجرای آزمایشی بدون ذخیره تغییرات'
        )
        parser.add_argument(
            '--find-large-deposits',
            action='store_true',
            help='شناسایی صندوق‌هایی که بیش از 24 سررسید دارند و ذخیره اطلاعات آنها در فایل JSON'
        )
        parser.add_argument(
            '--output-file',
            type=str,
            default='large_deposits.json',
            help='مسیر فایل JSON برای ذخیره اطلاعات صندوق‌های با بیش از 24 سررسید'
        )

    def create_due_date(self, deposit, due_date_number, gregorian_due_date, dry_run=False):
        """
        ایجاد یک سررسید جدید برای صندوق
        """
        if not dry_run:
            DepositDueDate.objects.create(
                deposit=deposit,
                due_date_number=due_date_number,
                due_date=gregorian_due_date,
                is_completed=False
            )
            self.stdout.write(self.style.SUCCESS(f'سررسید جدید {due_date_number} برای صندوق {deposit.id} ایجاد شد'))
        else:
            self.stdout.write(f'[اجرای آزمایشی] سررسید جدید {due_date_number} برای صندوق {deposit.id} ایجاد می‌شد')

    def calculate_due_date(self, current_jalali_year, current_jalali_month, current_jalali_day, payment_day, i):
        """
        محاسبه تاریخ سررسید شمسی و تبدیل به میلادی
        """
        # محاسبه تاریخ سررسید شمسی
        if current_jalali_month + (i - 1) > 12:
            jalali_due_month = (current_jalali_month + (i - 1)) % 12
            if jalali_due_month == 0:
                jalali_due_month = 12
            jalali_due_year = current_jalali_year + ((current_jalali_month + (i - 1) - 1) // 12)
        else:
            jalali_due_month = current_jalali_month + (i - 1)
            jalali_due_year = current_jalali_year
        
        # اطمینان از اینکه روز سررسید در محدوده مجاز ماه شمسی است
        max_days_in_month = 31 if jalali_due_month <= 6 else (30 if jalali_due_month <= 11 else 29)
        jalali_due_day = min(payment_day, max_days_in_month)
        
        # ایجاد تاریخ شمسی
        jalali_due_date = jdatetime.date(jalali_due_year, jalali_due_month, jalali_due_day)
        
        # تبدیل به تاریخ میلادی برای ذخیره در دیتابیس
        return jalali_due_date.togregorian()

    def fix_due_dates_for_deposit(self, deposit, dry_run=False, max_due_dates=0):
        """
        اصلاح تاریخ‌های سررسید برای یک صندوق خاص
        """
        # حذف سررسیدهای قبلی نمی‌کنیم، فقط آنها را اصلاح می‌کنیم
        due_dates = DepositDueDate.objects.filter(deposit=deposit).order_by('due_date_number')
        
        # تعیین تاریخ شروع بر اساس نوع صندوق
        if deposit.deposit_type == "Poll":
            start_date = deposit.initial_lottery_date if deposit.initial_lottery_date else datetime.now().date()
            duration = deposit.lottery_month_count if deposit.lottery_month_count else 12
            self.stdout.write(f'صندوق نوع Poll با شناسه {deposit.id} - تاریخ شروع: {start_date} - تعداد ماه قرعه‌کشی: {duration}')
        elif deposit.deposit_type == "Saving":
            start_date = deposit.start_date if deposit.start_date else datetime.now().date()
            # اگر validity_duration برابر با None بود، آن را به 1 تغییر می‌دهیم و ذخیره می‌کنیم
            if deposit.validity_duration is None:
                self.stdout.write(f'صندوق {deposit.id}: validity_duration برابر با None است، آن را به 1 تغییر می‌دهیم')
                deposit.validity_duration = 1
                deposit.save(update_fields=['validity_duration'])
            duration = deposit.validity_duration * 12
            self.stdout.write(f'صندوق نوع Saving با شناسه {deposit.id} - تاریخ شروع: {start_date} - مدت اعتبار: {deposit.validity_duration} سال ({duration} ماه)')
        elif deposit.deposit_type == "Reporting":
            start_date = deposit.start_date if deposit.start_date else datetime.now().date()
            # اگر validity_duration برابر با None بود، آن را به 1 تغییر می‌دهیم و ذخیره می‌کنیم
            if deposit.validity_duration is None:
                self.stdout.write(f'صندوق {deposit.id}: validity_duration برابر با None است، آن را به 1 تغییر می‌دهیم')
                deposit.validity_duration = 1
                deposit.save(update_fields=['validity_duration'])
            duration = deposit.validity_duration * 12
            self.stdout.write(f'صندوق نوع Reporting با شناسه {deposit.id} - تاریخ شروع: {start_date} - مدت اعتبار: {deposit.validity_duration} سال ({duration} ماه)')
        else:
            self.stdout.write(self.style.ERROR(f'نوع صندوق نامعتبر: {deposit.deposit_type}'))
            return
            
        # هشدار برای تعداد زیاد سررسید
        if duration > 24:
            self.stdout.write(self.style.WARNING(f'⚠️⚠️⚠️ هشدار: تعداد سررسیدهای این صندوق ({duration} ماه) بیشتر از حد معمول است! ⚠️⚠️⚠️'))
            
        # اگر محدودیت تعداد سررسید تعیین شده باشد، آن را اعمال می‌کنیم
        original_duration = duration
        if max_due_dates > 0 and duration > max_due_dates:
            self.stdout.write(self.style.WARNING(f'محدود کردن تعداد سررسیدها به {max_due_dates} (از {duration} سررسید)'))
            duration = max_due_dates
        
        # تبدیل تاریخ میلادی به شمسی
        jalali_date = jdatetime.date.fromgregorian(date=start_date)
        self.stdout.write(f'تاریخ شروع شمسی: {jalali_date.year}/{jalali_date.month}/{jalali_date.day}')
        
        # سررسید هر پرداخت
        payment_day = deposit.payment_cycle
        self.stdout.write(f'روز سررسید در هر ماه: {payment_day}')
        
        # بررسی می‌کنیم آیا روز سررسید (payment_day) قبل از روز فعلی است یا نه
        current_jalali_day = jalali_date.day
        current_jalali_month = jalali_date.month
        current_jalali_year = jalali_date.year
        
        # اگر روز سررسید قبل از روز فعلی است، از ماه بعد شروع می‌کنیم
        if payment_day < current_jalali_day:
            self.stdout.write(f'روز سررسید ({payment_day}) قبل از روز فعلی ({current_jalali_day}) است، از ماه بعد شروع می‌کنیم')
            if current_jalali_month == 12:  # اگر اسفند است
                current_jalali_month = 1
                current_jalali_year += 1
            else:
                current_jalali_month += 1
        
        # اگر صندوق هیچ سررسیدی ندارد، تمام سررسیدها را ایجاد می‌کنیم
        if not due_dates.exists():
            self.stdout.write(self.style.WARNING(f'صندوق {deposit.id} هیچ سررسیدی ندارد، ایجاد {duration} سررسید بر اساس منطق سیگنال...'))
            self.stdout.write(f'--{deposit.deposit_type}-deposit-signal-> start_date:{start_date}/payment_day:{payment_day}/ duration:{duration}')
            
            # نمایش اطلاعات محاسبه شده برای ایجاد سررسیدها
            self.stdout.write(f'محاسبه سررسیدها از تاریخ شمسی {jalali_date.year}/{jalali_date.month}/{jalali_date.day} با روز سررسید {payment_day} برای {duration} ماه')
            
            for i in range(1, duration + 1):
                gregorian_due_date = self.calculate_due_date(
                    current_jalali_year, current_jalali_month, current_jalali_day, payment_day, i
                )
                jalali_due_date = jdatetime.date.fromgregorian(date=gregorian_due_date)
                self.stdout.write(f'===due_date=> {i}: شمسی: {jalali_due_date.year}/{jalali_due_date.month}/{jalali_due_date.day} - میلادی: {gregorian_due_date}')
                self.create_due_date(deposit, i, gregorian_due_date, dry_run)
            
            self.stdout.write(self.style.SUCCESS(f'ایجاد {duration} سررسید برای صندوق {deposit.id} به پایان رسید'))
            return
        
        # اصلاح تاریخ‌های سررسید موجود و ایجاد سررسیدهای جدید در صورت نیاز
        existing_count = due_dates.count()
        if existing_count < duration:
            self.stdout.write(self.style.WARNING(f'صندوق {deposit.id} دارای {existing_count} سررسید است، اما نیاز به {duration} سررسید دارد'))
            self.stdout.write(f'اصلاح {existing_count} سررسید موجود و ایجاد {duration - existing_count} سررسید جدید...')
        else:
            self.stdout.write(f'صندوق {deposit.id} دارای {existing_count} سررسید است، اصلاح سررسیدهای موجود...')
        
        # نمایش اطلاعات محاسبه شده برای اصلاح سررسیدها
        self.stdout.write(f'--{deposit.deposit_type}-deposit-signal-> start_date:{start_date}/payment_day:{payment_day}/ duration:{duration}')
        self.stdout.write(f'محاسبه سررسیدها از تاریخ شمسی {jalali_date.year}/{jalali_date.month}/{jalali_date.day} با روز سررسید {payment_day} برای {duration} ماه')
        
        for i in range(1, duration + 1):
            due_date = due_dates.filter(due_date_number=i).first()
            
            # محاسبه تاریخ سررسید جدید
            gregorian_due_date = self.calculate_due_date(
                current_jalali_year, current_jalali_month, current_jalali_day, payment_day, i
            )
            jalali_due_date = jdatetime.date.fromgregorian(date=gregorian_due_date)
            
            # اگر سررسید وجود ندارد، آن را ایجاد می‌کنیم
            if not due_date:
                self.stdout.write(f'===due_date=> {i}: شمسی: {jalali_due_date.year}/{jalali_due_date.month}/{jalali_due_date.day} - میلادی: {gregorian_due_date} (جدید)')
                self.create_due_date(deposit, i, gregorian_due_date, dry_run)
                continue
            
            # اگر سررسید وجود دارد، آن را اصلاح می‌کنیم
            old_date = due_date.due_date
            old_jalali_date = jdatetime.date.fromgregorian(date=old_date)
            
            self.stdout.write(f'===due_date=> {i}: شمسی قدیمی: {old_jalali_date.year}/{old_jalali_date.month}/{old_jalali_date.day} - میلادی قدیمی: {old_date} -> '
                             f'شمسی جدید: {jalali_due_date.year}/{jalali_due_date.month}/{jalali_due_date.day} - میلادی جدید: {gregorian_due_date}')
            
            if not dry_run:
                due_date.due_date = gregorian_due_date
                due_date.save(update_fields=['due_date'])
                self.stdout.write(self.style.SUCCESS(f'سررسید {i} برای صندوق {deposit.id} اصلاح شد'))
            else:
                self.stdout.write(f'[اجرای آزمایشی] سررسید {i} برای صندوق {deposit.id} اصلاح می‌شد')

    def find_large_deposits(self, output_file):
        """
        شناسایی صندوق‌هایی که بیش از 24 سررسید دارند و ذخیره اطلاعات آنها در فایل JSON
        """
        self.stdout.write(f'شناسایی صندوق‌هایی که بیش از 24 سررسید دارند...')
        
        large_deposits = []
        deposits = Deposit.objects.all()
        total_deposits = deposits.count()
        
        self.stdout.write(f'بررسی {total_deposits} صندوق...')
        
        for index, deposit in enumerate(deposits, 1):
            duration = 0
            
            if deposit.deposit_type == "Poll":
                duration = deposit.lottery_month_count if deposit.lottery_month_count else 12
            elif deposit.deposit_type == "Saving":
                # اگر validity_duration برابر با None بود، آن را به 1 تغییر می‌دهیم و ذخیره می‌کنیم
                if deposit.validity_duration is None:
                    self.stdout.write(f'صندوق {deposit.id}: validity_duration برابر با None است، آن را به 1 تغییر می‌دهیم')
                    deposit.validity_duration = 1
                    deposit.save(update_fields=['validity_duration'])
                duration = deposit.validity_duration * 12
            elif deposit.deposit_type == "Reporting":
                # اگر validity_duration برابر با None بود، آن را به 1 تغییر می‌دهیم و ذخیره می‌کنیم
                if deposit.validity_duration is None:
                    self.stdout.write(f'صندوق {deposit.id}: validity_duration برابر با None است، آن را به 1 تغییر می‌دهیم')
                    deposit.validity_duration = 1
                    deposit.save(update_fields=['validity_duration'])
                duration = deposit.validity_duration * 12
            
            if duration > 24:
                # تعیین تاریخ شروع بر اساس نوع صندوق
                if deposit.deposit_type == "Poll":
                    start_date = deposit.initial_lottery_date if deposit.initial_lottery_date else datetime.now().date()
                elif deposit.deposit_type == "Saving" or deposit.deposit_type == "Reporting":
                    start_date = deposit.start_date if deposit.start_date else datetime.now().date()
                else:
                    start_date = datetime.now().date()
                
                # تبدیل تاریخ میلادی به شمسی
                jalali_date = jdatetime.date.fromgregorian(date=start_date)
                
                # اضافه کردن اطلاعات صندوق به لیست
                large_deposits.append({
                    'id': deposit.id,
                    'deposit_type': deposit.deposit_type,
                    'duration': duration,
                    'start_date': str(start_date),
                    'jalali_start_date': f'{jalali_date.year}/{jalali_date.month}/{jalali_date.day}',
                    'payment_day': deposit.payment_cycle,
                    'validity_duration': deposit.validity_duration if hasattr(deposit, 'validity_duration') else None,
                    'lottery_month_count': deposit.lottery_month_count if hasattr(deposit, 'lottery_month_count') else None,
                    'due_dates_count': DepositDueDate.objects.filter(deposit=deposit).count()
                })
                
                self.stdout.write(f'صندوق {deposit.id} ({index}/{total_deposits}): نوع {deposit.deposit_type} - تعداد سررسید: {duration}')
            
            # نمایش پیشرفت هر 10 صندوق
            if index % 10 == 0:
                self.stdout.write(f'پیشرفت: {index}/{total_deposits} صندوق بررسی شده')
        
        # ذخیره اطلاعات در فایل JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(large_deposits, f, ensure_ascii=False, indent=4)
        
        self.stdout.write(self.style.SUCCESS(f'{len(large_deposits)} صندوق با بیش از 24 سررسید شناسایی شد و در فایل {output_file} ذخیره شد'))
        
        return large_deposits

    @transaction.atomic
    def handle(self, *args, **options):
        deposit_id = options.get('deposit_id')
        deposit_type = options.get('deposit_type')
        max_due_dates = options.get('max_due_dates', 0)
        dry_run = options.get('dry_run', False)
        find_large_deposits = options.get('find_large_deposits', False)
        output_file = options.get('output_file', 'large_deposits.json')
        
        # اگر پارامتر find_large_deposits فعال باشد، فقط صندوق‌های با بیش از 24 سررسید را شناسایی می‌کنیم
        if find_large_deposits:
            self.find_large_deposits(output_file)
            return
        
        if dry_run:
            self.stdout.write(self.style.WARNING('اجرای آزمایشی - تغییرات ذخیره نخواهند شد'))
            
        if max_due_dates > 0:
            self.stdout.write(f'محدود کردن تعداد سررسیدها به حداکثر {max_due_dates} سررسید')
        
        # فیلتر کردن صندوق‌ها بر اساس پارامترهای ورودی
        deposits_query = Deposit.objects.all()
        
        if deposit_id:
            deposits_query = deposits_query.filter(id=deposit_id)
            self.stdout.write(f'اصلاح سررسیدها فقط برای صندوق با شناسه {deposit_id}')
        
        if deposit_type:
            deposits_query = deposits_query.filter(deposit_type=deposit_type)
            self.stdout.write(f'اصلاح سررسیدها فقط برای صندوق‌های نوع {deposit_type}')
        
        deposits = deposits_query.all()
        total_deposits = deposits.count()
        
        if total_deposits == 0:
            self.stdout.write(self.style.ERROR('هیچ صندوقی با معیارهای مشخص شده یافت نشد'))
            return
        
        self.stdout.write(f'شروع اصلاح سررسیدها برای {total_deposits} صندوق...')
        
        # اصلاح سررسیدها برای هر صندوق
        for index, deposit in enumerate(deposits, 1):
            self.stdout.write(f'پردازش صندوق {deposit.id} ({index}/{total_deposits})')
            try:
                self.fix_due_dates_for_deposit(deposit, dry_run, max_due_dates)
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'خطا در اصلاح سررسیدهای صندوق {deposit.id}: {str(e)}'))
        
        if not dry_run:
            self.stdout.write(self.style.SUCCESS(f'اصلاح سررسیدها برای {total_deposits} صندوق با موفقیت انجام شد'))
        else:
            self.stdout.write(self.style.SUCCESS(f'اجرای آزمایشی برای {total_deposits} صندوق با موفقیت انجام شد'))
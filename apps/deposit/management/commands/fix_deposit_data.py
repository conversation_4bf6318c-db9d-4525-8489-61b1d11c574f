from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from decimal import Decimal
from apps.deposit.models import Deposit, PollDeposit, SavingDeposit, ReportingDeposit


class Command(BaseCommand):
    help = 'اصلاح داده‌های صندوق‌هایی که unit_amount یا فیلدهای مرتبط ندارند'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='نمایش تغییرات بدون اعمال آن‌ها',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='اعمال تغییرات بدون تأیید',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        self.stdout.write(
            self.style.SUCCESS('🔧 شروع اصلاح داده‌های صندوق‌ها...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('⚠️  حالت Dry Run - هیچ تغییری اعمال نمی‌شود')
            )
        
        # آمار کلی
        total_fixed = 0
        
        # اصلاح صندوق‌های Poll
        poll_fixed = self.fix_poll_deposits(dry_run)
        total_fixed += poll_fixed
        
        # اصلاح صندوق‌های Saving
        saving_fixed = self.fix_saving_deposits(dry_run)
        total_fixed += saving_fixed
        
        # اصلاح صندوق‌های Reporting
        reporting_fixed = self.fix_reporting_deposits(dry_run)
        total_fixed += reporting_fixed
        
        # خلاصه نتایج
        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS(f'✅ خلاصه: {total_fixed} صندوق اصلاح شد')
        )
        self.stdout.write(f'   - صندوق‌های Poll: {poll_fixed}')
        self.stdout.write(f'   - صندوق‌های Saving: {saving_fixed}')
        self.stdout.write(f'   - صندوق‌های Reporting: {reporting_fixed}')
        
        if not dry_run and total_fixed > 0:
            if not force:
                confirm = input('\nآیا می‌خواهید تغییرات را اعمال کنید؟ (y/N): ')
                if confirm.lower() != 'y':
                    self.stdout.write(
                        self.style.WARNING('❌ عملیات لغو شد')
                    )
                    return
            
            self.stdout.write(
                self.style.SUCCESS('✅ تغییرات با موفقیت اعمال شد!')
            )

    def fix_poll_deposits(self, dry_run=False):
        """اصلاح صندوق‌های Poll که lottery_month_count یا unit_amount ندارند"""
        self.stdout.write('\n🎲 بررسی صندوق‌های قرعه‌کشی...')
        
        # یافتن صندوق‌های مشکل‌دار
        problematic_polls = PollDeposit.objects.filter(
            lottery_month_count__isnull=True
        ).exclude(total_debt_amount__isnull=True)
        
        fixed_count = 0
        
        for deposit in problematic_polls:
            self.stdout.write(f'   📋 {deposit.title} (ID: {deposit.id})')
            
            # تخمین تعداد ماه بر اساس منطق کسب‌وکار
            estimated_months = self.estimate_lottery_months(deposit)
            
            if not dry_run:
                with transaction.atomic():
                    deposit.lottery_month_count = estimated_months
                    if deposit.total_debt_amount:
                        deposit.unit_amount = deposit.total_debt_amount / estimated_months
                    deposit.save()
            
            self.stdout.write(
                f'      ✓ lottery_month_count: {estimated_months}'
            )
            if deposit.total_debt_amount:
                unit_amount = deposit.total_debt_amount / estimated_months
                self.stdout.write(
                    f'      ✓ unit_amount: {unit_amount:,.0f} تومان'
                )
            
            fixed_count += 1
        
        if fixed_count == 0:
            self.stdout.write('   ✅ همه صندوق‌های Poll سالم هستند')
        
        return fixed_count

    def fix_saving_deposits(self, dry_run=False):
        """اصلاح صندوق‌های Saving که validity_duration ندارند"""
        self.stdout.write('\n💰 بررسی صندوق‌های پس‌انداز...')
        
        # یافتن صندوق‌های مشکل‌دار
        problematic_savings = SavingDeposit.objects.filter(
            validity_duration__isnull=True
        )
        
        fixed_count = 0
        
        for deposit in problematic_savings:
            self.stdout.write(f'   📋 {deposit.title} (ID: {deposit.id})')
            
            # تخمین مدت اعتبار بر اساس منطق کسب‌وکار
            estimated_duration = self.estimate_validity_duration(deposit)
            
            if not dry_run:
                with transaction.atomic():
                    deposit.validity_duration = estimated_duration
                    deposit.save()
            
            self.stdout.write(
                f'      ✓ validity_duration: {estimated_duration} ماه'
            )
            
            fixed_count += 1
        
        if fixed_count == 0:
            self.stdout.write('   ✅ همه صندوق‌های Saving سالم هستند')
        
        return fixed_count

    def fix_reporting_deposits(self, dry_run=False):
        """اصلاح صندوق‌های Reporting که unit_amount ندارند"""
        self.stdout.write('\n📊 بررسی صندوق‌های گزارشی...')
        
        # یافتن صندوق‌های مشکل‌دار
        problematic_reporting = ReportingDeposit.objects.filter(
            unit_amount__isnull=True
        )
        
        fixed_count = 0
        
        for deposit in problematic_reporting:
            self.stdout.write(f'   📋 {deposit.title} (ID: {deposit.id})')
            
            # مقدار پیش‌فرض برای صندوق گزارشی
            default_amount = Decimal('100000.00')  # 100 هزار تومان
            
            if not dry_run:
                with transaction.atomic():
                    deposit.unit_amount = default_amount
                    deposit.save()
            
            self.stdout.write(
                f'      ✓ unit_amount: {default_amount:,.0f} تومان (پیش‌فرض)'
            )
            
            fixed_count += 1
        
        if fixed_count == 0:
            self.stdout.write('   ✅ همه صندوق‌های Reporting سالم هستند')
        
        return fixed_count

    def estimate_lottery_months(self, deposit):
        """تخمین تعداد ماه قرعه‌کشی بر اساس اطلاعات موجود"""
        # اگر تعداد اعضا مشخص باشد
        members_count = deposit.members.filter(is_active=True).count()
        if members_count > 0:
            return members_count
        
        # اگر حداکثر تعداد اعضا مشخص باشد
        if deposit.max_members_count:
            return deposit.max_members_count
        
        # پیش‌فرض: 12 ماه (یک سال)
        return 12

    def estimate_validity_duration(self, deposit):
        """تخمین مدت اعتبار بر اساس اطلاعات موجود"""
        # بررسی نام صندوق برای تشخیص مدت
        title_lower = deposit.title.lower()
        
        if 'سال' in title_lower or 'ساله' in title_lower:
            # استخراج عدد از نام
            import re
            numbers = re.findall(r'\d+', title_lower)
            if numbers:
                years = int(numbers[0])
                return years * 12  # تبدیل به ماه
        
        # پیش‌فرض: 24 ماه (2 سال)
        return 24

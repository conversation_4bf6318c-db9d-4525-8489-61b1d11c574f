from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils.translation import gettext as _
import logging

# Import all models that have relationships with Deposit
from apps.deposit.models import (
    Deposit, DepositMembership, DepositDueDate, DepositMedia, DepositMediaImage
)
from apps.loan.models import Loan, LoanInstallment
from apps.payment.models import Payment
from apps.transaction.models import Transaction
from apps.request.models import (
    RequestCreateDeposit, RequestJoinDeposit, LoanRequest, WithdrawalRequest
)
from apps.voting.models import VotingPoll, VotingOption, Vote
from apps.voting.models.reporting import VotingReport, VotingReportImage
from apps.lottery.models import DepositLottery
from apps.ticket.models import Ticket, TicketMessage
from apps.issues.models import IssueReport

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Delete all Deposit instances (Boxes/صندوق) and their related data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting anything',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Skip confirmation prompts',
        )

    def handle(self, **options):
        dry_run = options['dry_run']
        force = options['force']

        self.stdout.write(
            self.style.WARNING('=' * 80)
        )
        self.stdout.write(
            self.style.WARNING('DELETE ALL BOXES (DEPOSITS) AND RELATED DATA')
        )
        self.stdout.write(
            self.style.WARNING('=' * 80)
        )

        if dry_run:
            self.stdout.write(
                self.style.NOTICE('🔍 DRY RUN MODE - No data will be deleted')
            )
        else:
            self.stdout.write(
                self.style.ERROR('⚠️  LIVE MODE - Data will be permanently deleted!')
            )

        # Get all deposits
        deposits = Deposit.objects.all()
        total_deposits = deposits.count()

        if total_deposits == 0:
            self.stdout.write(
                self.style.SUCCESS('✅ No deposits found. Nothing to delete.')
            )
            return

        self.stdout.write(f'\n📊 Found {total_deposits} deposits to process\n')

        # Show summary of what will be deleted
        self._show_deletion_summary(deposits, dry_run)

        if not dry_run and not force:
            # Ask for confirmation
            confirm = input(
                f'\n⚠️  Are you sure you want to delete ALL {total_deposits} deposits '
                'and their related data? This action cannot be undone!\n'
                'Type "DELETE ALL DEPOSITS" to confirm: '
            )
            if confirm != "DELETE ALL DEPOSITS":
                self.stdout.write(
                    self.style.ERROR('❌ Operation cancelled.')
                )
                return

        # Process deletion
        if dry_run:
            self._dry_run_deletion(deposits)
        else:
            self._execute_deletion(deposits)

    def _show_deletion_summary(self, deposits, dry_run):
        """Show summary of what will be deleted"""
        self.stdout.write('📋 DELETION SUMMARY:')
        self.stdout.write('-' * 40)

        total_counts = {
            'deposits': deposits.count(),
            'memberships': 0,
            'due_dates': 0,
            'media': 0,
            'transactions': 0,
            'payments': 0,
            'loans': 0,
            'loan_installments': 0,
            'lotteries': 0,
            'voting_polls': 0,
            'voting_options': 0,
            'votes': 0,
            'voting_reports': 0,
            'voting_report_images': 0,
            'tickets': 0,
            'ticket_messages': 0,
            'issue_reports': 0,
            'create_requests': 0,
            'join_requests': 0,
            'loan_requests': 0,
            'withdrawal_requests': 0,
        }

        # Calculate totals using efficient queries
        deposit_ids = list(deposits.values_list('id', flat=True))

        total_counts['memberships'] = DepositMembership.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['due_dates'] = DepositDueDate.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['media'] = DepositMedia.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['transactions'] = Transaction.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['payments'] = Payment.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['loans'] = Loan.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['lotteries'] = DepositLottery.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['voting_polls'] = VotingPoll.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['voting_reports'] = VotingReport.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['tickets'] = Ticket.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['issue_reports'] = IssueReport.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['create_requests'] = RequestCreateDeposit.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['join_requests'] = RequestJoinDeposit.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['loan_requests'] = LoanRequest.objects.filter(deposit_id__in=deposit_ids).count()
        total_counts['withdrawal_requests'] = WithdrawalRequest.objects.filter(deposit_id__in=deposit_ids).count()

        # Calculate nested counts
        loan_ids = list(Loan.objects.filter(deposit_id__in=deposit_ids).values_list('id', flat=True))
        if loan_ids:
            total_counts['loan_installments'] = LoanInstallment.objects.filter(loan_id__in=loan_ids).count()

        poll_ids = list(VotingPoll.objects.filter(deposit_id__in=deposit_ids).values_list('id', flat=True))
        if poll_ids:
            total_counts['voting_options'] = VotingOption.objects.filter(voting_poll_id__in=poll_ids).count()
            total_counts['votes'] = Vote.objects.filter(voting_poll_id__in=poll_ids).count()

        report_ids = list(VotingReport.objects.filter(deposit_id__in=deposit_ids).values_list('id', flat=True))
        if report_ids:
            total_counts['voting_report_images'] = VotingReportImage.objects.filter(report_id__in=report_ids).count()

        ticket_ids = list(Ticket.objects.filter(deposit_id__in=deposit_ids).values_list('id', flat=True))
        if ticket_ids:
            total_counts['ticket_messages'] = TicketMessage.objects.filter(ticket_id__in=ticket_ids).count()

        media_ids = list(DepositMedia.objects.filter(deposit_id__in=deposit_ids).values_list('id', flat=True))
        if media_ids:
            total_counts['media_images'] = DepositMediaImage.objects.filter(media_id__in=media_ids).count()

        # Display counts
        for key, count in total_counts.items():
            if count > 0:
                self.stdout.write(f'  • {key.replace("_", " ").title()}: {count:,}')

        action = "would be deleted" if dry_run else "will be deleted"
        self.stdout.write(f'\n📊 Total items that {action}: {sum(total_counts.values()):,}')

    def _dry_run_deletion(self, deposits):
        """Show what would be deleted in dry run mode"""
        self.stdout.write('\n🔍 DRY RUN - Showing deletion order:')
        self.stdout.write('-' * 50)

        for i, deposit in enumerate(deposits, 1):
            self.stdout.write(f'\n{i}. Processing deposit: {deposit.title} (ID: {deposit.id})')
            self._show_related_models_info(deposit)

        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Dry run completed. {deposits.count()} deposits would be deleted.'
            )
        )

    def _execute_deletion(self, deposits):
        """Execute the actual deletion"""
        self.stdout.write('\n🗑️  Starting deletion process...')
        self.stdout.write('-' * 50)

        deleted_count = 0
        
        try:
            with transaction.atomic():
                for i, deposit in enumerate(deposits, 1):
                    self.stdout.write(
                        f'\n{i}/{deposits.count()} Deleting deposit: {deposit.title} (ID: {deposit.id})'
                    )
                    
                    # Delete related models in correct order
                    self._delete_related_models(deposit)
                    
                    # Finally delete the deposit itself
                    deposit.delete()
                    deleted_count += 1
                    
                    self.stdout.write(f'  ✅ Deleted deposit {deposit.id}')

            self.stdout.write(
                self.style.SUCCESS(
                    f'\n🎉 Successfully deleted {deleted_count} deposits and all related data!'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f'\n❌ Error during deletion: {str(e)}\n'
                    'Transaction rolled back. No data was deleted.'
                )
            )
            logger.error(f"Deletion failed: {str(e)}", exc_info=True)

    def _delete_related_models(self, deposit):
        """Delete all models related to a deposit in the correct order"""
        
        # 1. Delete ticket messages first (depends on tickets)
        tickets = deposit.tickets.all()
        for ticket in tickets:
            message_count = ticket.messages.count()
            if message_count > 0:
                self.stdout.write(f'  - Deleting {message_count} ticket messages')
                ticket.messages.all().delete()

        # 2. Delete tickets
        ticket_count = tickets.count()
        if ticket_count > 0:
            self.stdout.write(f'  - Deleting {ticket_count} tickets')
            tickets.delete()

        # 3. Delete loan installments (depends on loans)
        loans = deposit.loans.all()
        for loan in loans:
            installment_count = loan.installments.count()
            if installment_count > 0:
                self.stdout.write(f'  - Deleting {installment_count} loan installments')
                loan.installments.all().delete()

        # 4. Delete loans
        loan_count = loans.count()
        if loan_count > 0:
            self.stdout.write(f'  - Deleting {loan_count} loans')
            loans.delete()

        # 5. Delete voting report images (depends on voting reports)
        voting_reports = deposit.reports.all()
        for report in voting_reports:
            image_count = report.images.count()
            if image_count > 0:
                self.stdout.write(f'  - Deleting {image_count} voting report images')
                report.images.all().delete()

        # 6. Delete voting reports
        report_count = voting_reports.count()
        if report_count > 0:
            self.stdout.write(f'  - Deleting {report_count} voting reports')
            voting_reports.delete()

        # 7. Delete votes (depends on voting polls)
        voting_polls = deposit.voting_polls.all()
        for poll in voting_polls:
            vote_count = poll.votes.count()
            if vote_count > 0:
                self.stdout.write(f'  - Deleting {vote_count} votes')
                poll.votes.all().delete()
            
            # Delete voting options
            option_count = poll.options.count()
            if option_count > 0:
                self.stdout.write(f'  - Deleting {option_count} voting options')
                poll.options.all().delete()

        # 8. Delete voting polls
        poll_count = voting_polls.count()
        if poll_count > 0:
            self.stdout.write(f'  - Deleting {poll_count} voting polls')
            voting_polls.delete()

        # 9. Delete deposit lotteries
        lottery_count = deposit.deposit_winners.count()
        if lottery_count > 0:
            self.stdout.write(f'  - Deleting {lottery_count} deposit lotteries')
            deposit.deposit_winners.all().delete()

        # 10. Delete payments
        payment_count = deposit.payments.count()
        if payment_count > 0:
            self.stdout.write(f'  - Deleting {payment_count} payments')
            deposit.payments.all().delete()

        # 11. Delete transactions
        transaction_count = deposit.transactions.count()
        if transaction_count > 0:
            self.stdout.write(f'  - Deleting {transaction_count} transactions')
            deposit.transactions.all().delete()

        # 12. Delete issue reports
        issue_count = deposit.issue_reports.count()
        if issue_count > 0:
            self.stdout.write(f'  - Deleting {issue_count} issue reports')
            deposit.issue_reports.all().delete()

        # 13. Delete request models
        self._delete_request_models(deposit)

        # 14. Delete deposit media images
        for media in deposit.media.all():
            image_count = media.images.count()
            if image_count > 0:
                self.stdout.write(f'  - Deleting {image_count} deposit media images')
                media.images.all().delete()

        # 15. Delete deposit media
        media_count = deposit.media.count()
        if media_count > 0:
            self.stdout.write(f'  - Deleting {media_count} deposit media')
            deposit.media.all().delete()

        # 16. Delete deposit due dates
        due_date_count = deposit.due_dates.count()
        if due_date_count > 0:
            self.stdout.write(f'  - Deleting {due_date_count} deposit due dates')
            deposit.due_dates.all().delete()

        # 17. Delete deposit memberships (should be last before deposit)
        membership_count = deposit.members.count()
        if membership_count > 0:
            self.stdout.write(f'  - Deleting {membership_count} deposit memberships')
            deposit.members.all().delete()

    def _delete_request_models(self, deposit):
        """Delete all request models related to a deposit"""
        
        # Delete loan requests
        loan_request_count = deposit.loan_requests.count()
        if loan_request_count > 0:
            self.stdout.write(f'  - Deleting {loan_request_count} loan requests')
            deposit.loan_requests.all().delete()

        # Delete withdrawal requests
        withdrawal_request_count = deposit.withdrawal_requests.count()
        if withdrawal_request_count > 0:
            self.stdout.write(f'  - Deleting {withdrawal_request_count} withdrawal requests')
            deposit.withdrawal_requests.all().delete()

        # Delete join requests
        join_request_count = deposit.join_requests.count()
        if join_request_count > 0:
            self.stdout.write(f'  - Deleting {join_request_count} join requests')
            deposit.join_requests.all().delete()

        # Delete create requests
        create_request_count = deposit.create_requests.count()
        if create_request_count > 0:
            self.stdout.write(f'  - Deleting {create_request_count} create requests')
            deposit.create_requests.all().delete()

    def _show_related_models_info(self, deposit):
        """Show information about related models for a deposit in dry run mode"""
        
        # Show counts for each related model
        related_counts = {
            'Memberships': deposit.members.count(),
            'Due Dates': deposit.due_dates.count(),
            'Media': deposit.media.count(),
            'Transactions': deposit.transactions.count(),
            'Payments': deposit.payments.count(),
            'Loans': deposit.loans.count(),
            'Lotteries': deposit.deposit_winners.count(),
            'Voting Polls': deposit.voting_polls.count(),
            'Voting Reports': deposit.reports.count(),
            'Tickets': deposit.tickets.count(),
            'Issue Reports': deposit.issue_reports.count(),
            'Create Requests': deposit.create_requests.count(),
            'Join Requests': deposit.join_requests.count(),
            'Loan Requests': deposit.loan_requests.count(),
            'Withdrawal Requests': deposit.withdrawal_requests.count(),
        }

        # Calculate additional nested counts
        for loan in deposit.loans.all():
            related_counts['Loan Installments'] = related_counts.get('Loan Installments', 0) + loan.installments.count()
        
        for poll in deposit.voting_polls.all():
            related_counts['Voting Options'] = related_counts.get('Voting Options', 0) + poll.options.count()
            related_counts['Votes'] = related_counts.get('Votes', 0) + poll.votes.count()
        
        for report in deposit.reports.all():
            related_counts['Voting Report Images'] = related_counts.get('Voting Report Images', 0) + report.images.count()
        
        for ticket in deposit.tickets.all():
            related_counts['Ticket Messages'] = related_counts.get('Ticket Messages', 0) + ticket.messages.count()
        
        for media in deposit.media.all():
            related_counts['Deposit Media Images'] = related_counts.get('Deposit Media Images', 0) + media.images.count()

        # Display non-zero counts
        for model_name, count in related_counts.items():
            if count > 0:
                self.stdout.write(f'    - {count} {model_name}')

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.deposit.models import Deposit, DepositMembership, DepositDueDate, DepositMedia, DepositMediaImage
from apps.loan.models import Loan, LoanInstallment
from apps.payment.models import Payment
from apps.request.models import RequestCreateDeposit, RequestJoinDeposit, LoanRequest, WithdrawalRequest


class Command(BaseCommand):
    help = 'حذف صندوق‌ها در محدوده مشخصی از ID ها به همراه تمام مدل‌های وابسته'

    def add_arguments(self, parser):
        parser.add_argument(
            '--start',
            type=int,
            required=True,
            help='ID شروع محدوده (شامل این ID)'
        )
        parser.add_argument(
            '--end',
            type=int,
            required=True,
            help='ID پایان محدوده (شامل این ID)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='اجرای آزمایشی بدون حذف واقعی داده‌ها'
        )

    def handle(self, *args, **options):
        start_id = options['start']
        end_id = options['end']
        dry_run = options['dry_run']

        if start_id > end_id:
            self.stdout.write(self.style.ERROR(f'خطا: ID شروع ({start_id}) باید کوچکتر یا مساوی ID پایان ({end_id}) باشد.'))
            return

        # یافتن صندوق‌های مورد نظر
        deposits = Deposit.objects.filter(id__gte=start_id, id__lte=end_id)
        deposit_count = deposits.count()

        if deposit_count == 0:
            self.stdout.write(self.style.WARNING(f'هیچ صندوقی با ID در محدوده {start_id} تا {end_id} یافت نشد.'))
            return

        self.stdout.write(f'تعداد {deposit_count} صندوق در محدوده ID {start_id} تا {end_id} یافت شد.')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('حالت اجرای آزمایشی فعال است. هیچ داده‌ای حذف نخواهد شد.'))

        # پردازش هر صندوق
        for deposit in deposits:
            self.stdout.write(f'در حال پردازش صندوق ID={deposit.id}, عنوان="{deposit.title}"')
            
            try:
                with transaction.atomic():
                    if not dry_run:
                        # حذف مدل‌های وابسته به صندوق
                        self._delete_related_models(deposit)
                        
                        # حذف خود صندوق
                        deposit.delete()
                        self.stdout.write(self.style.SUCCESS(f'صندوق ID={deposit.id} با موفقیت حذف شد.'))
                    else:
                        # نمایش اطلاعات در حالت اجرای آزمایشی
                        self._show_related_models_info(deposit)
                        self.stdout.write(self.style.SUCCESS(f'[اجرای آزمایشی] صندوق ID={deposit.id} حذف می‌شد.'))
            
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'خطا در حذف صندوق ID={deposit.id}: {str(e)}'))

        # نمایش خلاصه عملیات
        if not dry_run:
            self.stdout.write(self.style.SUCCESS(f'عملیات حذف {deposit_count} صندوق در محدوده ID {start_id} تا {end_id} با موفقیت انجام شد.'))
        else:
            self.stdout.write(self.style.SUCCESS(f'[اجرای آزمایشی] عملیات حذف {deposit_count} صندوق در محدوده ID {start_id} تا {end_id} با موفقیت شبیه‌سازی شد.'))

    def _delete_related_models(self, deposit):
        """حذف تمام مدل‌های وابسته به یک صندوق"""
        
        # 1. حذف درخواست‌های وام
        loan_requests_count = LoanRequest.objects.filter(deposit=deposit).count()
        if loan_requests_count > 0:
            self.stdout.write(f'  - حذف {loan_requests_count} درخواست وام')
            LoanRequest.objects.filter(deposit=deposit).delete()
        
        # 2. حذف وام‌ها و اقساط آن‌ها
        loans = Loan.objects.filter(deposit=deposit)
        loan_count = loans.count()
        if loan_count > 0:
            installment_count = LoanInstallment.objects.filter(loan__in=loans).count()
            self.stdout.write(f'  - حذف {installment_count} قسط وام مربوط به {loan_count} وام')
            LoanInstallment.objects.filter(loan__in=loans).delete()
            loans.delete()
        
        # 3. حذف پرداخت‌ها
        payment_count = Payment.objects.filter(deposit=deposit).count()
        if payment_count > 0:
            self.stdout.write(f'  - حذف {payment_count} پرداخت')
            Payment.objects.filter(deposit=deposit).delete()
        
        # 4. حذف درخواست‌های عضویت و ایجاد صندوق
        join_requests_count = RequestJoinDeposit.objects.filter(deposit=deposit).count() if hasattr(RequestJoinDeposit, 'objects') else 0
        if join_requests_count > 0:
            self.stdout.write(f'  - حذف {join_requests_count} درخواست عضویت')
            RequestJoinDeposit.objects.filter(deposit=deposit).delete()
        
        create_requests_count = RequestCreateDeposit.objects.filter(deposit=deposit).count() if hasattr(RequestCreateDeposit, 'objects') else 0
        if create_requests_count > 0:
            self.stdout.write(f'  - حذف {create_requests_count} درخواست ایجاد صندوق')
            RequestCreateDeposit.objects.filter(deposit=deposit).delete()
        
        # 5. حذف درخواست‌های برداشت
        withdrawal_requests_count = WithdrawalRequest.objects.filter(deposit=deposit).count() if hasattr(WithdrawalRequest, 'objects') else 0
        if withdrawal_requests_count > 0:
            self.stdout.write(f'  - حذف {withdrawal_requests_count} درخواست برداشت')
            WithdrawalRequest.objects.filter(deposit=deposit).delete()
        
        # 6. حذف رسانه‌های صندوق و تصاویر آن‌ها
        media_items = DepositMedia.objects.filter(deposit=deposit)
        media_count = media_items.count()
        if media_count > 0:
            image_count = DepositMediaImage.objects.filter(deposit_media__in=media_items).count()
            self.stdout.write(f'  - حذف {image_count} تصویر مربوط به {media_count} رسانه صندوق')
            DepositMediaImage.objects.filter(deposit_media__in=media_items).delete()
            media_items.delete()
        
        # 7. حذف سررسیدهای صندوق
        due_dates_count = DepositDueDate.objects.filter(deposit=deposit).count()
        if due_dates_count > 0:
            self.stdout.write(f'  - حذف {due_dates_count} سررسید صندوق')
            DepositDueDate.objects.filter(deposit=deposit).delete()
        
        # 8. حذف عضویت‌های صندوق
        membership_count = DepositMembership.objects.filter(deposit=deposit).count()
        if membership_count > 0:
            self.stdout.write(f'  - حذف {membership_count} عضویت صندوق')
            DepositMembership.objects.filter(deposit=deposit).delete()

    def _show_related_models_info(self, deposit):
        """نمایش اطلاعات مدل‌های وابسته به یک صندوق در حالت اجرای آزمایشی"""
        
        # 1. درخواست‌های وام
        loan_requests_count = LoanRequest.objects.filter(deposit=deposit).count()
        if loan_requests_count > 0:
            self.stdout.write(f'  - {loan_requests_count} درخواست وام حذف می‌شد')
        
        # 2. وام‌ها و اقساط آن‌ها
        loans = Loan.objects.filter(deposit=deposit)
        loan_count = loans.count()
        if loan_count > 0:
            installment_count = LoanInstallment.objects.filter(loan__in=loans).count()
            self.stdout.write(f'  - {installment_count} قسط وام مربوط به {loan_count} وام حذف می‌شد')
        
        # 3. پرداخت‌ها
        payment_count = Payment.objects.filter(deposit=deposit).count()
        if payment_count > 0:
            self.stdout.write(f'  - {payment_count} پرداخت حذف می‌شد')
        
        # 4. درخواست‌های عضویت و ایجاد صندوق
        join_requests_count = RequestJoinDeposit.objects.filter(deposit=deposit).count() if hasattr(RequestJoinDeposit, 'objects') else 0
        if join_requests_count > 0:
            self.stdout.write(f'  - {join_requests_count} درخواست عضویت حذف می‌شد')
        
        create_requests_count = RequestCreateDeposit.objects.filter(deposit=deposit).count() if hasattr(RequestCreateDeposit, 'objects') else 0
        if create_requests_count > 0:
            self.stdout.write(f'  - {create_requests_count} درخواست ایجاد صندوق حذف می‌شد')
        
        # 5. درخواست‌های برداشت
        withdrawal_requests_count = WithdrawalRequest.objects.filter(deposit=deposit).count() if hasattr(WithdrawalRequest, 'objects') else 0
        if withdrawal_requests_count > 0:
            self.stdout.write(f'  - {withdrawal_requests_count} درخواست برداشت حذف می‌شد')
        
        # 6. رسانه‌های صندوق و تصاویر آن‌ها
        media_items = DepositMedia.objects.filter(deposit=deposit)
        media_count = media_items.count()
        if media_count > 0:
            image_count = DepositMediaImage.objects.filter(deposit_media__in=media_items).count()
            self.stdout.write(f'  - {image_count} تصویر مربوط به {media_count} رسانه صندوق حذف می‌شد')
        
        # 7. سررسیدهای صندوق
        due_dates_count = DepositDueDate.objects.filter(deposit=deposit).count()
        if due_dates_count > 0:
            self.stdout.write(f'  - {due_dates_count} سررسید صندوق حذف می‌شد')
        
        # 8. عضویت‌های صندوق
        membership_count = DepositMembership.objects.filter(deposit=deposit).count()
        if membership_count > 0:
            self.stdout.write(f'  - {membership_count} عضویت صندوق حذف می‌شد')
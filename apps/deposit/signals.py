from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

from apps.deposit.models import PollDeposit, DepositDueDate, SavingDeposit, ReportingDeposit, Deposit
from apps.lottery.models import DepositLottery
from datetime import datetime, timedelta
import jdatetime
from utils.date_utils import gregorian_to_jalali, jalali_to_gregorian


def create_due_dates_for_deposit(instance, start_date, duration, deposit_type):
    """
    تابع کمکی برای ایجاد سررسیدهای صندوق بر اساس تقویم شمسی
    
    Args:
        instance: نمونه صندوق
        start_date: تاریخ شروع (میلادی)
        duration: تعداد ماه‌های سررسید
        deposit_type: نوع صندوق (برای نمایش در لاگ)
    """
    # تبدیل تاریخ میلادی به شمسی
    jalali_date = jdatetime.date.fromgregorian(date=start_date)
    print(f'--تاریخ شروع شمسی: {jalali_date.year}/{jalali_date.month}/{jalali_date.day}')
    
    # سررسید هر پرداخت (مثلاً سوم هر ماه)
    payment_day = instance.payment_cycle  # فرض می‌کنیم payment_cycle روز ماه را مشخص می‌کند
    print(f'--{deposit_type}-deposit-signal-> start_date:{start_date}/payment_day:{payment_day}/ duration:{duration}')
    
    # بررسی می‌کنیم آیا روز سررسید (payment_day) قبل از روز فعلی است یا نه
    current_jalali_day = jalali_date.day
    current_jalali_month = jalali_date.month
    current_jalali_year = jalali_date.year
    
    # اگر روز سررسید قبل از روز فعلی است، از ماه بعد شروع می‌کنیم
    if payment_day < current_jalali_day:
        print(f'--روز سررسید ({payment_day}) قبل از روز فعلی ({current_jalali_day}) است، از ماه بعد شروع می‌کنیم')
        if current_jalali_month == 12:  # اگر اسفند است
            current_jalali_month = 1
            current_jalali_year += 1
        else:
            current_jalali_month += 1
    
    for i in range(1, duration + 1):
        # محاسبه تاریخ سررسید شمسی
        if current_jalali_month + (i - 1) > 12:
            jalali_due_month = (current_jalali_month + (i - 1)) % 12
            if jalali_due_month == 0:
                jalali_due_month = 12
            jalali_due_year = current_jalali_year + ((current_jalali_month + (i - 1) - 1) // 12)
        else:
            jalali_due_month = current_jalali_month + (i - 1)
            jalali_due_year = current_jalali_year
        
        # اطمینان از اینکه روز سررسید در محدوده مجاز ماه شمسی است
        max_days_in_month = 31 if jalali_due_month <= 6 else (30 if jalali_due_month <= 11 else 29)
        jalali_due_day = min(payment_day, max_days_in_month)
        
        # ایجاد تاریخ شمسی
        jalali_due_date = jdatetime.date(jalali_due_year, jalali_due_month, jalali_due_day)
        
        # تبدیل به تاریخ میلادی برای ذخیره در دیتابیس
        gregorian_due_date = jalali_due_date.togregorian()
        
        print(f'===due_date=> {i}: شمسی: {jalali_due_date.year}/{jalali_due_date.month}/{jalali_due_date.day} - میلادی: {gregorian_due_date}')
        
        # ایجاد رکورد DepositDueDate
        DepositDueDate.objects.create(
            deposit=instance,
            due_date_number=i,
            due_date=gregorian_due_date,
            is_completed=False
        )


@receiver(post_save, sender=Deposit)
def create_deposit_due_dates(sender, instance, created, **kwargs):
    if created and instance.deposit_type == "Poll":
        print(f'--signal-poll-->: {instance.id} ')
        
        # تاریخ شروع را از initial_lottery_date می‌گیریم یا تاریخ امروز
        if instance.initial_lottery_date:
            start_date = instance.initial_lottery_date
        else:
            start_date = datetime.now().date()
        
        # ایجاد سررسیدها با استفاده از تابع کمکی
        create_due_dates_for_deposit(
            instance=instance,
            start_date=start_date,
            duration=instance.lottery_month_count,
            deposit_type="poll"
        )

@receiver(post_save, sender=Deposit)
def create_saving_deposit_due_dates(sender, instance, created, **kwargs):
    if created and instance.deposit_type == "Saving":
        print(f'--signal-saving-->: {instance.id} ')
        
        # تاریخ شروع از فیلد start_date
        start_date = instance.start_date
        
        # مدت اعتبار صندوق
        validity_duration = instance.validity_duration * 12
        
        # ایجاد سررسیدها با استفاده از تابع کمکی
        create_due_dates_for_deposit(
            instance=instance,
            start_date=start_date,
            duration=validity_duration,
            deposit_type="saving"
        )
            
@receiver(post_save, sender=Deposit)
def create_reporting_deposit_due_dates(sender, instance, created, **kwargs):
    if created and instance.deposit_type == "Reporting":
        print(f'--signal-reporting-->: {instance.id} ')
        
        # تاریخ شروع از امروز
        start_date = datetime.now().date()
        
        # مدت اعتبار صندوق
        validity_duration = instance.validity_duration * 12
        
        # ایجاد سررسیدها با استفاده از تابع کمکی
        create_due_dates_for_deposit(
            instance=instance,
            start_date=start_date,
            duration=validity_duration,
            deposit_type="reporting"
        )


@receiver(post_delete, sender=DepositLottery)
def update_lottery_numbers(sender, instance, **kwargs):
    # یافتن تمام قرعه کشی‌های مرتبط با همان due_date
    remaining_lotteries = DepositLottery.objects.filter(
        due_date=instance.due_date
    ).order_by('lottery_number')

    # دوباره شماره‌گذاری از ۱ شروع میشود
    for index, lottery in enumerate(remaining_lotteries, start=1):
        if lottery.lottery_number != index:
            lottery.lottery_number = index
            lottery.save(update_fields=['lottery_number'])
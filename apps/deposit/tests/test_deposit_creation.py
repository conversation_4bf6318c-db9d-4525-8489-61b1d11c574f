from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from datetime import datetime, timedelta
import json

from apps.deposit.models import Deposit, DepositDueDate, DepositMembership
from apps.request.models import RequestCreateDeposit
from apps.region.models import Region, UserRegion

User = get_user_model()

class DepositCreationTestCase(TestCase):
    """Test case for deposit creation functionality"""
    
    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123'
        )
        
        # Create a test region
        self.region = Region.objects.create(
            name='Test Region',
            description='Test Region Description'
        )
        
        # Associate user with region
        self.user_region = UserRegion.objects.create(
            user=self.user,
            region=self.region,
            is_active=True
        )
        
        # Set up the test client
        self.client = Client()
        self.client.login(username='testuser', password='testpassword123')
        
        # Common data for all deposit types
        self.common_data = {
            'title': 'Test Deposit',
            'description': 'Test Deposit Description',
            'unit_amount': 1000000.00,
            'payment_cycle': 5,  # 5th day of each month
            'max_unit_per_request': 5,
            'max_members_count': 20,
            'rules': [
                {'subject': 'Rule 1', 'description': 'Description for Rule 1'},
                {'subject': 'Rule 2', 'description': 'Description for Rule 2'}
            ]
        }
        
        # URLs for deposit creation
        self.poll_deposit_url = reverse('request-deposit-create-poll')
        self.saving_deposit_url = reverse('request-deposit-create-saving')
        self.reporting_deposit_url = reverse('request-deposit-create-reporting')
    
    def test_create_poll_deposit(self):
        """Test creating a poll deposit"""
        # Prepare poll deposit specific data
        initial_lottery_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        poll_data = self.common_data.copy()
        poll_data.update({
            'total_debt_amount': 10000000.00,
            'lottery_month_count': 10,
            'initial_lottery_date': initial_lottery_date
        })
        
        # Send request to create poll deposit
        response = self.client.post(
            self.poll_deposit_url,
            data=json.dumps(poll_data),
            content_type='application/json'
        )
        
        # Check response status
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check deposit was created
        deposit_id = response.json().get('id')
        deposit = Deposit.objects.get(id=deposit_id)
        self.assertEqual(deposit.deposit_type, Deposit.DepositType.POLL)
        self.assertEqual(deposit.title, poll_data['title'])
        
        # Check request was created
        request = RequestCreateDeposit.objects.get(deposit=deposit)
        self.assertEqual(request.status, RequestCreateDeposit.StatusChoices.PENDING)
        
        # Check due dates were created
        due_dates = DepositDueDate.objects.filter(deposit=deposit)
        self.assertEqual(due_dates.count(), poll_data['lottery_month_count'])
        
        # Check that no membership exists yet (it will be created when the request is approved)
        membership_exists = DepositMembership.objects.filter(deposit=deposit, user=self.user).exists()
        self.assertFalse(membership_exists)
        
        # Approve the request to create membership
        request.approve()
        
        # Now check that the user is the owner of the deposit
        membership = DepositMembership.objects.get(deposit=deposit, user=self.user)
        self.assertEqual(membership.role, DepositMembership.Role.OWNER)
        
        # Check that deposit is now active
        deposit.refresh_from_db()
        self.assertTrue(deposit.is_active)
    
    def test_create_saving_deposit(self):
        """Test creating a saving deposit"""
        # Prepare saving deposit specific data
        start_date = datetime.now().date().strftime('%Y-%m-%d')
        saving_data = self.common_data.copy()
        saving_data.update({
            'validity_duration': 24,  # 24 months
            'start_date': start_date
        })
        
        # Send request to create saving deposit
        response = self.client.post(
            self.saving_deposit_url,
            data=json.dumps(saving_data),
            content_type='application/json'
        )
        
        # Check response status
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check deposit was created
        deposit_id = response.json().get('id')
        deposit = Deposit.objects.get(id=deposit_id)
        self.assertEqual(deposit.deposit_type, Deposit.DepositType.SAVING)
        self.assertEqual(deposit.title, saving_data['title'])
        
        # Check request was created
        request = RequestCreateDeposit.objects.get(deposit=deposit)
        self.assertEqual(request.status, RequestCreateDeposit.StatusChoices.PENDING)
        
        # Check due dates were created
        due_dates = DepositDueDate.objects.filter(deposit=deposit)
        self.assertEqual(due_dates.count(), saving_data['validity_duration'])
        
        # Check that no membership exists yet (it will be created when the request is approved)
        membership_exists = DepositMembership.objects.filter(deposit=deposit, user=self.user).exists()
        self.assertFalse(membership_exists)
        
        # Approve the request to create membership
        request.approve()
        
        # Now check that the user is the owner of the deposit
        membership = DepositMembership.objects.get(deposit=deposit, user=self.user)
        self.assertEqual(membership.role, DepositMembership.Role.OWNER)
        
        # Check that deposit is now active
        deposit.refresh_from_db()
        self.assertTrue(deposit.is_active)
    
    def test_create_reporting_deposit(self):
        """Test creating a reporting deposit"""
        # Prepare reporting deposit specific data
        reporting_data = self.common_data.copy()
        reporting_data.update({
            'total_debt_amount': 5000000.00,
            'validity_duration': 12  # 12 months
        })
        
        # Send request to create reporting deposit
        response = self.client.post(
            self.reporting_deposit_url,
            data=json.dumps(reporting_data),
            content_type='application/json'
        )
        
        # Check response status
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check deposit was created
        deposit_id = response.json().get('id')
        deposit = Deposit.objects.get(id=deposit_id)
        self.assertEqual(deposit.deposit_type, Deposit.DepositType.REPORTING)
        self.assertEqual(deposit.title, reporting_data['title'])
        
        # Check request was created
        request = RequestCreateDeposit.objects.get(deposit=deposit)
        self.assertEqual(request.status, RequestCreateDeposit.StatusChoices.PENDING)
        
        # Check due dates were created
        due_dates = DepositDueDate.objects.filter(deposit=deposit)
        self.assertEqual(due_dates.count(), reporting_data['validity_duration'])
        
        # Check that no membership exists yet (it will be created when the request is approved)
        membership_exists = DepositMembership.objects.filter(deposit=deposit, user=self.user).exists()
        self.assertFalse(membership_exists)
        
        # Approve the request to create membership
        request.approve()
        
        # Now check that the user is the owner of the deposit
        membership = DepositMembership.objects.get(deposit=deposit, user=self.user)
        self.assertEqual(membership.role, DepositMembership.Role.OWNER)
        
        # Check that deposit is now active
        deposit.refresh_from_db()
        self.assertTrue(deposit.is_active)
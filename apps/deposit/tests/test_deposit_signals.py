from django.test import TestCase
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta

from apps.deposit.models import Deposit, DepositDueDate, PollDeposit, SavingDeposit, ReportingDeposit
from apps.region.models import Region

User = get_user_model()

class DepositSignalsTestCase(TestCase):
    """Test case for deposit signals"""
    
    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123'
        )
        
        # Create a test region
        self.region = Region.objects.create(
            name='Test Region',
            description='Test Region Description'
        )
    
    def test_poll_deposit_due_dates_creation(self):
        """Test that due dates are created for a poll deposit"""
        # Create a poll deposit
        initial_lottery_date = datetime.now().date()
        lottery_month_count = 10
        payment_cycle = 5  # 5th day of each month
        
        poll_deposit = PollDeposit.objects.create(
            owner=self.user,
            region=self.region,
            title='Test Poll Deposit',
            description='Test Poll Deposit Description',
            total_debt_amount=10000000.00,
            lottery_month_count=lottery_month_count,
            unit_amount=1000000.00,
            payment_cycle=payment_cycle,
            max_unit_per_request=5,
            max_members_count=20,
            initial_lottery_date=initial_lottery_date
        )
        
        # Check that due dates were created
        due_dates = DepositDueDate.objects.filter(deposit=poll_deposit)
        self.assertEqual(due_dates.count(), lottery_month_count)
        
        # Check the first due date
        first_due_date = due_dates.order_by('due_date_number').first()
        self.assertEqual(first_due_date.due_date_number, 1)
        self.assertEqual(first_due_date.due_date.day, payment_cycle)
        self.assertFalse(first_due_date.is_completed)
    
    def test_saving_deposit_due_dates_creation(self):
        """Test that due dates are created for a saving deposit"""
        # Create a saving deposit
        start_date = datetime.now().date()
        validity_duration = 24  # 24 months
        payment_cycle = 10  # 10th day of each month
        
        saving_deposit = SavingDeposit.objects.create(
            owner=self.user,
            region=self.region,
            title='Test Saving Deposit',
            description='Test Saving Deposit Description',
            unit_amount=1000000.00,
            payment_cycle=payment_cycle,
            max_unit_per_request=5,
            max_members_count=20,
            validity_duration=validity_duration,
            start_date=start_date
        )
        
        # Check that due dates were created
        due_dates = DepositDueDate.objects.filter(deposit=saving_deposit)
        self.assertEqual(due_dates.count(), validity_duration)
        
        # Check the first due date
        first_due_date = due_dates.order_by('due_date_number').first()
        self.assertEqual(first_due_date.due_date_number, 1)
        self.assertEqual(first_due_date.due_date.day, payment_cycle)
        self.assertFalse(first_due_date.is_completed)
    
    def test_reporting_deposit_due_dates_creation(self):
        """Test that due dates are created for a reporting deposit"""
        # Create a reporting deposit
        validity_duration = 12  # 12 months
        payment_cycle = 15  # 15th day of each month
        
        reporting_deposit = ReportingDeposit.objects.create(
            owner=self.user,
            region=self.region,
            title='Test Reporting Deposit',
            description='Test Reporting Deposit Description',
            total_debt_amount=5000000.00,
            unit_amount=500000.00,
            payment_cycle=payment_cycle,
            max_unit_per_request=5,
            max_members_count=20,
            validity_duration=validity_duration
        )
        
        # Check that due dates were created
        due_dates = DepositDueDate.objects.filter(deposit=reporting_deposit)
        self.assertEqual(due_dates.count(), validity_duration)
        
        # Check the first due date
        first_due_date = due_dates.order_by('due_date_number').first()
        self.assertEqual(first_due_date.due_date_number, 1)
        self.assertEqual(first_due_date.due_date.day, payment_cycle)
        self.assertFalse(first_due_date.is_completed)
    
    def test_disable_previous_month_due_dates(self):
        """Test disabling previous month due dates"""
        # Create a poll deposit with due dates in the past
        initial_lottery_date = datetime.now().date() - timedelta(days=60)  # 2 months ago
        lottery_month_count = 10
        payment_cycle = 5
        
        poll_deposit = PollDeposit.objects.create(
            owner=self.user,
            region=self.region,
            title='Test Poll Deposit',
            description='Test Poll Deposit Description',
            total_debt_amount=10000000.00,
            lottery_month_count=lottery_month_count,
            unit_amount=1000000.00,
            payment_cycle=payment_cycle,
            max_unit_per_request=5,
            max_members_count=20,
            initial_lottery_date=initial_lottery_date
        )
        
        # Check initial state of due dates
        due_dates = DepositDueDate.objects.filter(deposit=poll_deposit)
        self.assertEqual(due_dates.count(), lottery_month_count)
        
        # All due dates should be initially not completed
        for due_date in due_dates:
            self.assertFalse(due_date.is_completed)
        
        # Disable previous month due dates
        poll_deposit.disable_previous_month_due_dates()
        
        # Check that past due dates are now marked as completed
        today = datetime.now().date()
        past_due_dates = DepositDueDate.objects.filter(
            deposit=poll_deposit,
            due_date__lt=today
        )
        
        for due_date in past_due_dates:
            self.assertTrue(due_date.is_completed)
        
        # Future due dates should still be not completed
        future_due_dates = DepositDueDate.objects.filter(
            deposit=poll_deposit,
            due_date__gte=today
        )
        
        for due_date in future_due_dates:
            self.assertFalse(due_date.is_completed)
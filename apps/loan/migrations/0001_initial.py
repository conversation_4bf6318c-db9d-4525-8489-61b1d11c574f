# Generated by Django 3.2.4 on 2025-03-13 22:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('deposit', '0008_delete_depositlottery'),
    ]

    operations = [
        migrations.CreateModel(
            name='Loan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, help_text='The total amount of the loan.', max_digits=15, verbose_name='Loan Amount')),
                ('installment_count', models.PositiveIntegerField(help_text='The number of installments for this loan.', verbose_name='Installment Count')),
                ('installment_date', models.IntegerField(help_text='The day of the month when installments are due (1-31).', verbose_name='Installment Day of Month')),
                ('description', models.TextField(blank=True, help_text='Additional information about the loan.', verbose_name='Description')),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('overdue', 'Overdue')], default='active', help_text='The current status of the loan.', max_length=20, verbose_name='Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='The date and time when the loan was created.', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='The date and time when the loan was last updated.', verbose_name='Updated At')),
                ('deposit', models.ForeignKey(help_text='The deposit associated with this loan.', on_delete=django.db.models.deletion.CASCADE, related_name='loans', to='deposit.deposit', verbose_name='Deposit')),
                ('deposit_membership', models.ForeignKey(help_text='The membership associated with this loan.', on_delete=django.db.models.deletion.CASCADE, related_name='loans', to='deposit.depositmembership', verbose_name='Deposit Membership')),
            ],
            options={
                'verbose_name': 'Loan',
                'verbose_name_plural': 'Loans',
            },
        ),
        migrations.CreateModel(
            name='LoanInstallment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('installment_number', models.PositiveIntegerField(help_text='The sequence number of this installment.', verbose_name='Installment Number')),
                ('amount', models.DecimalField(decimal_places=2, help_text='The amount of this installment.', max_digits=15, verbose_name='Amount')),
                ('due_date', models.DateField(help_text='The date when this installment is due.', verbose_name='Due Date')),
                ('is_paid', models.BooleanField(default=False, help_text='Whether this installment has been paid.', verbose_name='Is Paid')),
                ('paid_date', models.DateField(blank=True, help_text='The date when this installment was paid.', null=True, verbose_name='Paid Date')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('loan', models.ForeignKey(help_text='The loan this installment belongs to.', on_delete=django.db.models.deletion.CASCADE, related_name='installments', to='loan.loan', verbose_name='Loan')),
            ],
            options={
                'verbose_name': 'Loan Installment',
                'verbose_name_plural': 'Loan Installments',
                'ordering': ['installment_number'],
            },
        ),
    ]

from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.deposit.models import Deposit, DepositMembership
from apps.account.models import User
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from django.db.models.signals import post_save


class Loan(models.Model):
    class LoanStatus(models.TextChoices):
        ACTIVE = 'active', _('Active')
        COMPLETED = 'completed', _('Completed')
        OVERDUE = 'overdue', _('Overdue')
        
    deposit = models.ForeignKey(
        Deposit, 
        on_delete=models.CASCADE, 
        related_name='loans', 
        verbose_name=_("Deposit"),
        help_text=_("The deposit associated with this loan.")
    )
    deposit_membership = models.ForeignKey(
        DepositMembership, 
        on_delete=models.CASCADE, 
        related_name='loans', 
        verbose_name=_("Deposit Membership"),
        help_text=_("The membership associated with this loan.")
    )
    amount = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        verbose_name=_("Loan Amount"),
        help_text=_("The total amount of the loan.")
    )
    installment_count = models.PositiveIntegerField(
        verbose_name=_('Installment Count'),
        help_text=_("The number of installments for this loan.")
    )
    installment_date = models.IntegerField(
        verbose_name=_('Installment Day of Month'),
        help_text=_("The day of the month when installments are due (1-31).")
    )
    description = models.TextField(
        verbose_name=_('Description'),
        blank=True,
        help_text=_("Additional information about the loan.")
    )
    status = models.CharField(
        max_length=20, 
        choices=LoanStatus.choices, 
        default=LoanStatus.ACTIVE,
        verbose_name=_("Status"),
        help_text=_("The current status of the loan.")
    )
    created_at = models.DateTimeField(
        auto_now_add=True, 
        verbose_name=_("Created At"),
        help_text=_("The date and time when the loan was created.")
    )
    updated_at = models.DateTimeField(
        auto_now=True, 
        verbose_name=_("Updated At"),
        help_text=_("The date and time when the loan was last updated.")
    )

    class Meta:
        verbose_name = _("Loan")
        verbose_name_plural = _("Loans")

    def __str__(self):
        return f"Loan {self.id} - {self.deposit_membership.user.fullname} - {self.amount}"
    
    def calculate_installment_amount(self):
        """Calculate the amount of each installment."""
        if self.installment_count > 0:
            return self.amount / self.installment_count
        return 0
    
    def update_status(self):
        """Update the loan status based on installment payments."""
        installments = self.installments.all()
        total_installments = installments.count()
        paid_installments = installments.filter(is_paid=True).count()
        
        # If all installments are paid, mark the loan as completed
        if paid_installments == total_installments and total_installments > 0:
            self.status = self.LoanStatus.COMPLETED
            self.save()
            return
        
        # Check for overdue installments
        today = datetime.now().date()
        overdue_installments = installments.filter(is_paid=False, due_date__lt=today).exists()
        
        if overdue_installments:
            self.status = self.LoanStatus.OVERDUE
        else:
            self.status = self.LoanStatus.ACTIVE
        
        self.save()
    
    def create_installments(self):
        """Create installment instances for this loan."""
        installment_amount = self.calculate_installment_amount()
        start_date = datetime.now().date()
        
        # Get the first installment date (current month or next month if day has passed)
        current_day = start_date.day
        if current_day > self.installment_date:
            # If current day is past the installment day, start next month
            first_installment_date = start_date.replace(day=1) + relativedelta(months=1, day=self.installment_date)
        else:
            # Otherwise use this month
            first_installment_date = start_date.replace(day=self.installment_date)
        
        # Create installments
        for i in range(1, self.installment_count + 1):
            installment_date = first_installment_date + relativedelta(months=i-1)
            LoanInstallment.objects.get_or_create(
                loan=self,
                installment_number=i,
                defaults={
                    'amount': installment_amount,
                    'due_date': installment_date,
                    'is_paid': False
                }
            )


class LoanInstallment(models.Model):
    loan = models.ForeignKey(
        Loan, 
        on_delete=models.CASCADE, 
        related_name='installments', 
        verbose_name=_("Loan"),
        help_text=_("The loan this installment belongs to.")
    )
    installment_number = models.PositiveIntegerField(
        verbose_name=_('Installment Number'),
        help_text=_("The sequence number of this installment.")
    )
    amount = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        verbose_name=_("Amount"),
        help_text=_("The amount of this installment.")
    )
    due_date = models.DateField(
        verbose_name=_('Due Date'),
        help_text=_("The date when this installment is due.")
    )
    is_paid = models.BooleanField(
        default=False, 
        verbose_name=_("Is Paid"),
        help_text=_("Whether this installment has been paid.")
    )
    paid_date = models.DateField(
        null=True, 
        blank=True, 
        verbose_name=_("Paid Date"),
        help_text=_("The date when this installment was paid.")
    )
    created_at = models.DateTimeField(
        auto_now_add=True, 
        verbose_name=_("Created At")
    )
    updated_at = models.DateTimeField(
        auto_now=True, 
        verbose_name=_("Updated At")
    )

    class Meta:
        verbose_name = _("Loan Installment")
        verbose_name_plural = _("Loan Installments")
        ordering = ['installment_number']

    def __str__(self):
        return f"Installment {self.installment_number} of Loan {self.loan.id}"
    
    def mark_as_paid(self):
        """Mark this installment as paid."""
        self.is_paid = True
        self.paid_date = datetime.now().date()
        self.save()



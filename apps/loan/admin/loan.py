from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django import forms
from django.utils.html import format_html

from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    ChoicesRadioFilter
)
from unfold.decorators import display, action

from apps.loan.models import Loan
from .installment import LoanInstallmentInline
from utils.admin import project_admin_site, RegionFilteredAdmin

class LoanForm(forms.ModelForm):
    class Meta:
        model = Loan
        exclude = ()

class LoanAdmin(RegionFilteredAdmin):
    form = LoanForm
    list_display = (
        'loan_info', 'loan_status', 'loan_amount', 'user_info', 'created_at'
    )
    list_filter = (
        ('status', ChoicesRadioFilter),
        ('amount', RangeNumericFilter),
        ('created_at', RangeDateFilter),
        'deposit',
    )
    search_fields = ('deposit__title', 'deposit_membership__user__fullname', 'description')
    # autocomplete_fields = ('deposit', 'deposit_membership')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [LoanInstallmentInline]
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    
    fieldsets = (
        (None, {
            'fields': ('deposit', 'deposit_membership', 'amount', 'status')
        }),
        (_('Installment Details'), {
            'fields': ('installment_count', 'installment_date', 'description'),
            'classes': ['tab'],
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ['tab'],
        }),
    )
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    
    def get_queryset(self, request):
        """Optimize queries by prefetching related objects"""
        return super().get_queryset(request).select_related(
            'deposit', 'deposit_membership', 'deposit_membership__user'
        )
    
    @display(description='Loan', header=True)
    def loan_info(self, obj):
        deposit_name = obj.deposit.title if obj.deposit else _('No Deposit')
        installment_info = _("Installments: {}/{}").format(
            obj.installments.filter(is_paid=True).count(),
            obj.installment_count
        )
        
        return [
            f"Loan #{obj.id}",
            installment_info,
            None,
        ]
    
    @display(description='Status', label={
        'active': 'info',     # Active loans in blue
        'completed': 'success',  # Completed loans in green
        'overdue': 'danger',   # Overdue loans in red
    })
    def loan_status(self, obj):
        status_map = {
            'active': _('Active'),
            'completed': _('Completed'),
            'overdue': _('Overdue')
        }
        return obj.status, status_map.get(obj.status, obj.status)
    
    @display(description='Amount')
    def loan_amount(self, obj):
        formatted_amount = f"{obj.amount:,}" if obj.amount else "0"
        installment_amount = obj.calculate_installment_amount()
        formatted_installment = f"{installment_amount:,}" if installment_amount else "0"
        
        return f"{formatted_amount} {_('Toman')} ({_('Installment')}: {formatted_installment})"
    
    @display(description='User', header=True)
    def user_info(self, obj):
        user = obj.deposit_membership.user if obj.deposit_membership else None
        user_name = user.fullname if user else _('No User')
        
        return [
            user_name,
            str(user.phone_number) if user and user.phone_number else "-",
            None,
        ]
    
    def save_model(self, request, obj, form, change):
        """Override save_model to create installments if this is a new loan"""
        is_new = obj.pk is None
        super().save_model(request, obj, form, change)
        
        # If this is a new loan, create the installments
        if is_new:
            obj.create_installments()
    
    @action(description=_("Update Status"))
    def update_loan_status(self, request, queryset):
        """Update the status of selected loans"""
        count = 0
        for loan in queryset:
            loan.update_status()
            count += 1
        
        self.message_user(
            request,
            _("Successfully updated status for {} loans.").format(count),
        )

# Register with both admin sites
admin.site.register(Loan, LoanAdmin)
project_admin_site.register(Loan, LoanAdmin)
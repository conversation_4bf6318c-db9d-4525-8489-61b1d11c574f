# Generated by Django 3.2.4 on 2024-12-25 14:40

from django.db import migrations, models
import phonenumber_field.modelfields
import utils.validators


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='ref_link',
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(blank=True, help_text="Enter the user's email address.", max_length=254, null=True, unique=True, verbose_name='Email Address'),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone_number',
            field=phonenumber_field.modelfields.PhoneNumberField(default='2', max_length=128, region=None, unique=True, validators=[utils.validators.validate_possible_number], verbose_name='phone'),
            preserve_default=False,
        ),
    ]

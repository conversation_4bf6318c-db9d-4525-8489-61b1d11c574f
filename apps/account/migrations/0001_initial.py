# Generated by Django 3.2.4 on 2024-12-23 14:58

import dj_language.field
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields
import utils.validators


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('dj_language', '0002_auto_20220120_1344'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('email', models.EmailField(help_text="Enter the user's email address.", max_length=254, unique=True, verbose_name='Email Address')),
                ('fullname', models.CharField(help_text='Enter the full name of the user.', max_length=255, verbose_name='Full Name')),
                ('birthdate', models.DateField(blank=True, null=True, verbose_name='birthdate')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='users/avatars/%Y/%m/')),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, null=True, region=None, unique=True, validators=[utils.validators.validate_possible_number], verbose_name='phone')),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female')], help_text="Select the user's gender.", max_length=20, null=True, verbose_name='Gender')),
                ('membership_type', models.CharField(choices=[('guest', 'Guest'), ('member', 'Member')], default='guest', max_length=20, verbose_name='MemberShip Type')),
                ('city', models.CharField(blank=True, max_length=255, null=True, verbose_name='City')),
                ('shba_number', models.CharField(blank=True, max_length=255, null=True, verbose_name='SHBA Number')),
                ('ref_link', models.CharField(blank=True, max_length=255, null=True, verbose_name='Referral Link')),
                ('device_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='device id')),
                ('fcm', models.CharField(blank=True, max_length=512, null=True)),
                ('date_joined', models.DateTimeField(auto_now_add=True, help_text='The date and time the user registered.', verbose_name='Date Joined')),
                ('is_admin', models.BooleanField(default=False)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='Active')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'All Users',
                'verbose_name_plural': 'All Users',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='LoginHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mobile_device_id', models.CharField(blank=True, max_length=254, null=True, verbose_name='mobile device id')),
                ('lat', models.FloatField(blank=True, null=True, verbose_name='lat')),
                ('lon', models.FloatField(blank=True, null=True, verbose_name='lon')),
                ('country', models.CharField(blank=True, max_length=255, null=True, verbose_name='country')),
                ('city', models.CharField(blank=True, max_length=255, null=True, verbose_name='city')),
                ('ip', models.CharField(max_length=255, verbose_name='IP')),
                ('api_v', models.CharField(blank=True, max_length=64, null=True, verbose_name='API Version')),
                ('timezone', models.CharField(blank=True, max_length=60, null=True)),
                ('last_login_at', models.DateTimeField(auto_now_add=True, null=True, verbose_name='last login at')),
                ('location_method', models.CharField(blank=True, choices=[('DEFAULT', 'DEFAULT'), ('MANUAL', 'MANUAL'), ('AUTO', 'AUTO'), ('IP_DETECTION', 'IP_DETECTION')], max_length=19, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='login_history', to=settings.AUTH_USER_MODEL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'login history',
                'verbose_name_plural': 'user login histories',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='AdminUser',
            fields=[
            ],
            options={
                'verbose_name': 'Admin User',
                'verbose_name_plural': 'Admin Users',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('account.user',),
        ),
    ]

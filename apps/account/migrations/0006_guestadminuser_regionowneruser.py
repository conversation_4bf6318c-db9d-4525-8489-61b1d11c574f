# Generated by Django 5.2.1 on 2025-06-09 15:58

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0005_guestadminuser_regionowneruser_notification_data_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GuestAdminUser',
            fields=[
            ],
            options={
                'verbose_name': 'Guest Admin User',
                'verbose_name_plural': 'Guest Admin Users',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('account.user',),
        ),
        migrations.CreateModel(
            name='RegionOwnerUser',
            fields=[
            ],
            options={
                'verbose_name': 'Region Owner',
                'verbose_name_plural': 'Region Owners',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('account.user',),
        ),
    ]

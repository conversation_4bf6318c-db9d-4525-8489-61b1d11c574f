
{% load unfold i18n %}


<div class="grid gap-4 mb-4 md:grid-cols-2 lg:grid-cols-4 ">
    {% component "unfold/components/card.html" %}
        {% component "unfold/components/text.html" %}
            {% trans "Total Actice Users" %}
        {% endcomponent %}

        {% component "unfold/components/title.html" with component_class="AllUserComponent" %}{% endcomponent %}
    {% endcomponent %}

    {% component "unfold/components/card.html" %}
        {% component "unfold/components/text.html" %}
            {% trans "Total Guest Users" %}
        {% endcomponent %}
        {% component "unfold/components/title.html" with component_class="GuestUserComponent" %}{% endcomponent %}
    {% endcomponent %}
    {% component "unfold/components/card.html" %}
        {% component "unfold/components/text.html" %}
            {% trans "Total Students" %}
        {% endcomponent %}
        {% component "unfold/components/title.html" with component_class="StudentUserComponent" %}{% endcomponent %}
    {% endcomponent %}
    {% component "unfold/components/card.html" %}
        {% component "unfold/components/text.html" %}
            {% trans "Total Professors" %}
        {% endcomponent %}
        {% component "unfold/components/title.html" with component_class="ProfessorUserComponent" %}{% endcomponent %}
    {% endcomponent %}

</div>

import random
import string
from dj_language.field import LanguageField
from django.contrib.auth.models import AbstractUser, Group
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from phonenumber_field.modelfields import PhoneNumberField
from utils.validators import validate_possible_number
from apps.account.manager import UserManager, AdminUserManager, RegionOwnerUserManager



class User(AbstractUser):
    class MemberShipType(models.TextChoices):
        GUEST = 'guest', 'Guest'
        MEMBER = 'member', 'Member'
        
    class GenderChoices(models.TextChoices):
        MALE = 'male', 'Male'
        FEMALE = 'female', 'Female'

    email = models.EmailField(unique=True, verbose_name="Email Address", help_text="Enter the user's email address.", null=True, blank=True)
    fullname = models.CharField(max_length=255, verbose_name="Full Name", help_text="Enter the full name of the user.")
    birthdate = models.DateField(verbose_name=_('birthdate'), null=True, blank=True)

    avatar = models.ImageField(null=True, blank=True, upload_to='users/avatars/%Y/%m/')
    phone_number = PhoneNumberField(unique=True, validators=[validate_possible_number], verbose_name=_('phone'))
    language = LanguageField(null=True)
    username = None
    last_name = models.CharField(max_length=150, verbose_name="Last Name", help_text="Enter the user's last name.", null=True, blank=True)
    first_name = models.CharField(max_length=150, verbose_name="First Name", help_text="Enter the user's first name.", null=True, blank=True)
    gender = models.CharField(
        max_length=20, choices=GenderChoices.choices, null=True, blank=True, verbose_name=_('Gender'), help_text="Select the user's gender."
    )
    membership_type = models.CharField(
        max_length=20,
        choices=MemberShipType.choices,
        default=MemberShipType.GUEST,
        verbose_name="MemberShip Type",
    )
    city =  models.CharField(verbose_name=_('City'), max_length=255, null=True, blank=True)
    shba_number = models.CharField(verbose_name=_('SHBA Number'), max_length=255, null=True, blank=True)
    
    device_id = models.CharField(verbose_name=_('device id'), max_length=255, null=True, blank=True)
    fcm = models.CharField(max_length=512, null=True, blank=True)
    date_joined = models.DateTimeField(auto_now_add=True, verbose_name="Date Joined", help_text="The date and time the user registered.")
    is_admin = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True, verbose_name="Active", help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.")
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    objects = UserManager()
     
     
    EMAIL_FIELD = "email"
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["fullname"]

    def soft_delete(self):
        self.deleted_at = timezone.now()
        self.is_deleted = True
        self.is_active = False
        number = str(random.randint(**********, **********))  # ایجاد یک عدد رندوم 10 رقمی
        self.phone_number = f'{self.phone_number}:deleted{number}'
        self.email = f'{self.email}:deleted{number}' if self.email else None
        self.save()
                    
    def __str__(self):
        return self.get_full_name()
    
        
    def clean(self):
        super().clean()
        if self.email == "":
            # fix db uniqueness error bcz of django charfield null to empty string conversion
            self.email = None
        

    def get_full_name(self):
        return self.fullname
        
    def get_initials(self):
        if not self.fullname:
            return ""
            
        # Split the fullname by spaces and get the first letter of each part
        parts = self.fullname.split()
        initials = ""
        
        # Get up to 2 initials
        for part in parts[:2]:
            if part:
                initials += part[0].upper()
                
        return initials

    def generate_ref_link(self):        
        # Generate 7 random alphanumeric characters (letters and digits)
        random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=7))
        
        # Return the fixed prefix "HAMJEEB-" followed by the random characters
        return f"HAMJEEB-{random_chars}"
    
    @property
    def user_region(self):
        """Get the user's current active region"""
        try:
            current_membership = self.region_memberships.filter(is_current=True).first()
            return current_membership.region if current_membership else None
        except:
            return None

    @property
    def current_region_membership(self):
        """Get the user's current region membership object"""
        try:
            return self.region_memberships.filter(is_current=True).first()
        except:
            return None

    def get_all_regions(self):
        """Get all regions the user is a member of"""
        return [membership.region for membership in self.region_memberships.all()]

    def is_member_of_region(self, region):
        """Check if user is a member of a specific region"""
        return self.region_memberships.filter(region=region).exists()

    def set_current_region(self, region):
        """Set a region as the user's current active region"""
        if not self.is_member_of_region(region):
            return False

        # Set all regions as non-current
        self.region_memberships.update(is_current=False)

        # Set the specified region as current
        membership = self.region_memberships.get(region=region)
        membership.is_current = True
        membership.save()
        return True
    
    def count_unread_notifications(self):
        """
        Returns the count of unread notifications for this user filtered by current region.
        """
        # Get user's current active region
        current_region = self.user_region
        if not current_region:
            # If user has no active region, return 0
            return 0
        
        # Filter notifications to count only those related to current region
        # This includes notifications with region_id in data field matching current region
        # OR notifications without region_id (general notifications)
        from django.db.models import Q
        return self.notifications.filter(
            is_read=False
        ).filter(
            Q(data__region_id=current_region.id) | 
            Q(data__isnull=True) | 
            Q(data__region_id__isnull=True)
        ).count()
        
    class Meta:
        ordering = ("-id",)
        verbose_name = "All Users"
        verbose_name_plural = "All Users"



class AdminUser(User):
    objects = AdminUserManager()

    def save(self, *args, **kwargs):
        self.is_admin = True
        super().save(*args, **kwargs)

        group, _ = Group.objects.get_or_create(name="admin_group")
        self.groups.add(group)

    class Meta:
        proxy = True
        verbose_name = _("Admin User")
        verbose_name_plural = _("Admin Users")


class GuestAdminUser(User):
    """
    Proxy model for users who don't have any region memberships or have inactive region memberships
    """
    class Meta:
        proxy = True
        verbose_name = _("Guest Admin User")
        verbose_name_plural = _("Guest Admin Users")


class RegionOwnerUser(User):
    """
    Proxy model for users who are region owners
    """
    objects = RegionOwnerUserManager()
    
    class Meta:
        proxy = True
        verbose_name = _("Region Owner")
        verbose_name_plural = _("Region Owners")
        
        
class LoginHistory(models.Model):
    class LocalizationModels(models.TextChoices):
        default = 'DEFAULT', 'DEFAULT'
        manual = 'MANUAL', 'MANUAL'
        auto = 'AUTO', 'AUTO'
        ip_detection = 'IP_DETECTION', 'IP_DETECTION'

    mobile_device_id = models.CharField(verbose_name=_('mobile device id'), max_length=254, null=True, blank=True)
    user = models.ForeignKey("User", verbose_name=_('user'), on_delete=models.CASCADE, related_name='login_history')
    lat = models.FloatField(verbose_name=_('lat'), null=True, blank=True)
    lon = models.FloatField(verbose_name=_('lon'), null=True, blank=True)
    country = models.CharField(max_length=255, verbose_name=_('country'), null=True, blank=True)
    city = models.CharField(max_length=255, verbose_name=_('city'), null=True, blank=True)
    ip = models.CharField(max_length=255, verbose_name=_('IP'))
    api_v = models.CharField(max_length=64, verbose_name=_('API Version'), null=True, blank=True)
    timezone = models.CharField(null=True, blank=True, max_length=60)
    last_login_at = models.DateTimeField(null=True, blank=True, verbose_name=_('last login at'), auto_now_add=True)
    location_method = models.CharField(max_length=19, choices=LocalizationModels.choices, null=True, blank=True)

    def __str__(self):
        return self.ip

    class Meta:
        verbose_name = _("login history")
        verbose_name_plural = _('user login histories')
        ordering = ('-id',)

from django.db import models
from django.utils.translation import gettext as _


class Notification(models.Model):
    title = models.CharField(max_length=255, verbose_name=_('title'))
    message = models.TextField(max_length=512, verbose_name=_('message'))
    user = models.ForeignKey("account.User", on_delete=models.CASCADE, verbose_name=_('user'), related_name='notifications')
    is_read = models.BooleanField(default=False, verbose_name=_('is read'))
    data = models.JSONField(verbose_name=_('data'), null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'), null=True)
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'), null=True)
    
    def __str__(self):
        return self.title


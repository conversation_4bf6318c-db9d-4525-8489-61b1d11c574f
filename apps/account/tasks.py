import asyncio
from config.settings import base as settings
import requests
from celery import shared_task
import logging
import json
from async_firebase import AsyncFirebaseClient
from async_firebase.messages import Message
from kavenegar import KavenegarAPI, APIException, HTTPException

# ===================================================================
# Firebase Cloud Messaging Implementation using async-firebase
#
# This module has been updated to use the lightweight async-firebase
# library instead of the heavy firebase_admin and google libraries.
#
# Benefits:
# - Much lighter weight (no heavy dependencies)
# - Better async performance
# - Simpler API
# - Direct API calls without complex authentication flows
# ===================================================================



logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# تنظیمات Firebase
FIREBASE_CREDENTIALS = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


async def _send_notification_async(ids: list, title: str = None, body: str = None, data=None) -> list:
    """
    Internal async function to send notifications using async-firebase
    """
    if not ids:
        return []

    # Initialize Firebase client
    client = AsyncFirebaseClient()
    client.creds_from_service_account_info(FIREBASE_CREDENTIALS)

    # Split IDs into chunks of 500 (Firebase limit)
    chunked_ids = [ids[i:i + 500] for i in range(0, len(ids), 500)]
    responses = []

    for chunk in chunked_ids:
        chunk_responses = []

        # Send notification to each token in the chunk
        for token in chunk:
            try:
                # Build Android configuration
                android_config = client.build_android_config(
                    priority="high",
                    ttl=2419200,  # 28 days
                    collapse_key="push",
                    data=data or {},
                    title=title,
                    body=body,
                    color='#06EEBD',
                    visibility='public'
                )

                # Create message
                message = Message(android=android_config, token=token)

                # Send the message
                response = await client.send(message)

                if response.success:
                    logger.info(f'Successfully sent notification to token: {token[:10]}...')
                    chunk_responses.append({
                        'status': 'success',
                        'message_id': response.message_id,
                        'token': token
                    })
                else:
                    logger.error(f'Failed to send notification to token: {token[:10]}...')
                    chunk_responses.append({
                        'status': 'error',
                        'message': 'Failed to send notification',
                        'token': token
                    })

            except Exception as e:
                logger.error(f'Exception sending notification to token {token[:10]}...: {str(e)}')
                chunk_responses.append({
                    'status': 'error',
                    'message': str(e),
                    'token': token
                })

        responses.extend(chunk_responses)

    return responses


@shared_task
def send_notification(ids: list, title: str = None, body: str = None, data=None,
                            extra_notification_kwargs: dict = None) -> list:
    """
    Celery task wrapper for sending notifications
    """
    # Run the async function in a new event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(_send_notification_async(ids, title, body, data))
        return result
    finally:
        loop.close()


async def _send_bulk_notification_async(ids: list, title: str = None, body: str = None, data=None) -> dict:
    """
    Internal async function to send bulk notifications to all users simultaneously using async-firebase
    Uses concurrent async sends for better performance than individual API calls
    """
    if not ids:
        return {'status': 'error', 'message': 'No device tokens provided', 'sent_count': 0}

    # Initialize Firebase client
    client = AsyncFirebaseClient()
    client.creds_from_service_account_info(FIREBASE_CREDENTIALS)

    total_sent = 0
    total_failed = 0
    failed_tokens = []

    # Build Android configuration once for all messages
    android_config = client.build_android_config(
        priority="high",
        ttl=2419200,  # 28 days
        collapse_key="push",
        data=data or {},
        title=title,
        body=body,
        color='#06EEBD',
        visibility='public'
    )

    async def send_single_notification(token):
        """Send notification to a single token"""
        try:
            message = Message(android=android_config, token=token)
            response = await client.send(message)

            if response.success:
                return {'status': 'success', 'token': token, 'message_id': response.message_id}
            else:
                return {'status': 'failed', 'token': token, 'error': 'Send failed'}
        except Exception as e:
            return {'status': 'failed', 'token': token, 'error': str(e)}

    # Split IDs into chunks of 100 for concurrent processing (to avoid overwhelming the API)
    chunked_ids = [ids[i:i + 100] for i in range(0, len(ids), 100)]

    for chunk in chunked_ids:
        try:
            # Send notifications concurrently for this chunk
            tasks = [send_single_notification(token) for token in chunk]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for result in results:
                if isinstance(result, dict):
                    if result['status'] == 'success':
                        total_sent += 1
                    else:
                        total_failed += 1
                        failed_tokens.append(result['token'])
                else:
                    # Exception occurred
                    total_failed += 1
                    logger.error(f'Exception in bulk send: {str(result)}')

            logger.info(f'Processed chunk of {len(chunk)} tokens: {total_sent} sent, {total_failed} failed so far')

        except Exception as e:
            logger.error(f'Exception processing chunk: {str(e)}')
            total_failed += len(chunk)
            failed_tokens.extend(chunk)

    return {
        'status': 'completed',
        'total_tokens': len(ids),
        'sent_count': total_sent,
        'failed_count': total_failed,
        'failed_tokens': failed_tokens[:10] if failed_tokens else []  # Return first 10 failed tokens
    }


@shared_task
def send_bulk_notification(ids: list, title: str = None, body: str = None, data=None) -> dict:
    """
    Celery task for sending bulk notifications to all users simultaneously
    This is preferred over individual API calls for better performance
    """
    # Run the async function in a new event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(_send_bulk_notification_async(ids, title, body, data))
        return result
    finally:
        loop.close()

@shared_task
def send_otp_code_sms(phone_number, code):
    from utils.exceptions import AppAPIException
    from rest_framework import status

    BASE_URL_SERVICE = "https://console.melipayamak.com/api/send/simple/"

    phone_number = str(phone_number)
    code = str(code)
    print(code)
    data = {'from': '50004001410202', 'to': phone_number, 'text': code}
    try:
        response = requests.post(f'{BASE_URL_SERVICE}{settings.OTP_SERIVCE_KEY}',
                                json=data)

        response_data = response.json()
        print(response_data)

    except Exception as e:
        print(f"Error sending SMS: {str(e)}")
        pass

class ManageWhatsappApi(object):

    def __init__(self, chat_id, text):
        self.chat_id: str = chat_id
        self.text: str = text
        self.reply_to: str =  False
        self.session: str = 'default'


    def get_api_sendtext(self):
        return f"http://*************:3005/api/sendText"


    def sendtext(self):
        data = {
            "chatId": self.chat_id,
            "reply_to": self.reply_to,
            "text": self.text,
            "session": self.session
        }

        # تنظیم هدرهای درخواست
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        try:
            response = requests.post(self.get_api_sendtext(), json=data, headers=headers)

            # Try to parse JSON response, but handle the case when it's not valid JSON
            try:
                response_json = response.json()
                print(f'WhatsApp API response---> {response_json}')
                if response.status_code == 200:
                    return response_json
                else:
                    return {"error": response.status_code, "message": response.text}
            except json.JSONDecodeError:
                print(f"Non-JSON response received from WhatsApp API: {response.text}")
                return {"error": "JSONDecodeError", "message": response.text}
        except Exception as e:
            print(f"Error in WhatsApp API request: {str(e)}")
            return {"error": "RequestError", "message": str(e)}



@shared_task
def send_otp_code_whatsapp(phone_number, code, fullname: str = ''):
    try:
        phone = phone_number

        if phone.startswith('0') or phone.startswith('+'):
            phone = phone[1:]

        if not fullname:
            fullname = ''

        payload = {
            "chatId": f"{phone}@c.us",
            "message": f"Hello {fullname}! Welcome to the Qatreh App. Your verification code is: {code}."
        }
        headers = {
            'Content-Type': 'application/json'
        }

        whatsapp_api = ManageWhatsappApi(chat_id=payload['chatId'], text=payload['message'])
        response = whatsapp_api.sendtext()
        print(f'send---> {response}')
        return True
    except Exception as e:
        print(f"Error sending WhatsApp message: {str(e)}")
        # Return True to allow the registration process to continue
        return True




def _normalize_phone_number(phone_number):
    """
    تبدیل شماره تلفن به فرمت استاندارد

    Args:
        phone_number (str): شماره تلفن

    Returns:
        str: شماره تلفن استاندارد شده
    """
    phone_number = str(phone_number)

    # حذف صفر یا +98 از ابتدای شماره تلفن اگر وجود داشته باشد
    if phone_number.startswith('0'):
        phone_number = phone_number[1:]
    elif phone_number.startswith('+98'):
        phone_number = phone_number[3:]
    elif phone_number.startswith('98'):
        phone_number = phone_number[2:]

    return phone_number


@shared_task
def send_otp_code_kavenegar(phone_number, code, template=None):
    """
    ارسال کد تایید از طریق سرویس کاونگار

    Args:
        phone_number (str): شماره تلفن گیرنده
        code (str): کد تایید
        template (str, optional): قالب پیام. اگر None باشد، از تنظیمات پروژه استفاده می‌شود

    Returns:
        dict: نتیجه ارسال پیام
    """
    try:
        # تبدیل کد به رشته
        code = str(code)

        # استاندارد کردن شماره تلفن
        phone_number = _normalize_phone_number(phone_number)

        # اگر قالب مشخص نشده باشد، از تنظیمات پروژه استفاده می‌کنیم
        if template is None:
            template = settings.KAVENEGAR_OTP_TEMPLATE

        # ایجاد نمونه از API کاونگار
        api = KavenegarAPI(settings.KAVENEGAR_API_KEY)

        # ارسال پیام با استفاده از قالب
        try:
            # ابتدا سعی می‌کنیم از قالب استفاده کنیم
            response = api.verify_lookup({
                'receptor': phone_number,
                'token': code,
                'template': template
            })
            logger.info(f"Kavenegar verify SMS sent to {phone_number} using template {template}")
        except Exception as template_error:
            # اگر ارسال با قالب با خطا مواجه شد، از روش معمولی استفاده می‌کنیم
            logger.warning(f"Failed to send SMS using template: {str(template_error)}. Trying regular SMS...")
            params = {
                'sender': settings.KAVENEGAR_SENDER,
                'receptor': phone_number,
                'message': f'کد تایید شما در قطره: {code}'
            }
            response = api.sms_send(params)
            logger.info(f"Kavenegar regular SMS sent to {phone_number}")

        print(f"Kavenegar SMS response: {response}")
        return {'status': 'success', 'response': response}

    except APIException as e:
        # خطای API کاونگار
        error_msg = f"Kavenegar API Error: {str(e)}"
        logger.error(error_msg)
        return {'status': 'error', 'message': error_msg}

    except HTTPException as e:
        # خطای HTTP
        error_msg = f"Kavenegar HTTP Error: {str(e)}"
        logger.error(error_msg)
        return {'status': 'error', 'message': error_msg}

    except Exception as e:
        # سایر خطاها
        error_msg = f"Error sending SMS via Kavenegar: {str(e)}"
        logger.error(error_msg)
        return {'status': 'error', 'message': error_msg}


@shared_task
def send_sms_kavenegar(phone_number, message):
    """
    ارسال پیام متنی از طریق سرویس کاونگار

    Args:
        phone_number (str): شماره تلفن گیرنده
        message (str): متن پیام

    Returns:
        dict: نتیجه ارسال پیام
    """
    try:
        # استاندارد کردن شماره تلفن
        phone_number = _normalize_phone_number(phone_number)

        # ایجاد نمونه از API کاونگار
        api = KavenegarAPI(settings.KAVENEGAR_API_KEY)

        # ارسال پیام
        params = {
            'sender': settings.KAVENEGAR_SENDER,
            'receptor': phone_number,
            'message': message
        }
        response = api.sms_send(params)
        logger.info(f"Kavenegar SMS sent to {phone_number}: {message[:30]}...")

        print(f"Kavenegar SMS response: {response}")
        return {'status': 'success', 'response': response}

    except APIException as e:
        # خطای API کاونگار
        error_msg = f"Kavenegar API Error: {str(e)}"
        logger.error(error_msg)
        return {'status': 'error', 'message': error_msg}

    except HTTPException as e:
        # خطای HTTP
        error_msg = f"Kavenegar HTTP Error: {str(e)}"
        logger.error(error_msg)
        return {'status': 'error', 'message': error_msg}

    except Exception as e:
        # سایر خطاها
        error_msg = f"Error sending SMS via Kavenegar: {str(e)}"
        logger.error(error_msg)
        return {'status': 'error', 'message': error_msg}


@shared_task
def send_bulk_sms_kavenegar(phone_numbers, message):
    """
    ارسال پیام متنی به چندین شماره تلفن از طریق سرویس کاونگار

    Args:
        phone_numbers (list): لیست شماره تلفن‌های گیرندگان
        message (str): متن پیام

    Returns:
        dict: نتیجه ارسال پیام
    """
    try:
        if not phone_numbers:
            return {'status': 'error', 'message': 'No phone numbers provided'}

        # استاندارد کردن شماره تلفن‌ها
        normalized_numbers = [_normalize_phone_number(number) for number in phone_numbers]

        # تبدیل لیست شماره‌ها به رشته با جداکننده کاما
        receptors = ','.join(normalized_numbers)

        # ایجاد نمونه از API کاونگار
        api = KavenegarAPI(settings.KAVENEGAR_API_KEY)

        # ارسال پیام
        params = {
            'sender': settings.KAVENEGAR_SENDER,
            'receptor': receptors,
            'message': message
        }
        response = api.sms_send(params)
        logger.info(f"Kavenegar bulk SMS sent to {len(normalized_numbers)} recipients: {message[:30]}...")

        print(f"Kavenegar bulk SMS response: {response}")
        return {'status': 'success', 'response': response}

    except APIException as e:
        # خطای API کاونگار
        error_msg = f"Kavenegar API Error: {str(e)}"
        logger.error(error_msg)
        return {'status': 'error', 'message': error_msg}

    except HTTPException as e:
        # خطای HTTP
        error_msg = f"Kavenegar HTTP Error: {str(e)}"
        logger.error(error_msg)
        return {'status': 'error', 'message': error_msg}

    except Exception as e:
        # سایر خطاها
        error_msg = f"Error sending bulk SMS via Kavenegar: {str(e)}"
        logger.error(error_msg)
        return {'status': 'error', 'message': error_msg}


if __name__ == "__main__":
    # مثال استفاده از متد ارسال پیام واتساپ
    # send_otp_code_whatsapp('9012045375', 'ss222', 'ali')

    # مثال استفاده از متد ارسال کد تایید با کاونگار
    # response = send_otp_code_kavenegar('09123456789', '12345')
    # print(f"Kavenegar OTP response: {response}")

    # مثال استفاده از متد ارسال کد تایید با کاونگار با قالب سفارشی
    # response = send_otp_code_kavenegar('09123456789', '12345', template='verify')
    # print(f"Kavenegar OTP response with template: {response}")

    # مثال استفاده از متد ارسال پیام متنی با کاونگار
    # response = send_sms_kavenegar('09123456789', 'این یک پیام تست از سرویس قطره است.')
    # print(f"Kavenegar SMS response: {response}")

    # مثال استفاده از متد ارسال پیام به چندین شماره تلفن
    # phone_numbers = ['09123456789', '09123456788', '09123456787']
    # response = send_bulk_sms_kavenegar(phone_numbers, 'این یک پیام گروهی تست از سرویس قطره است.')
    # print(f"Kavenegar bulk SMS response: {response}")

    # مثال استفاده با کلید API و شماره فرستنده واقعی
    # response = send_otp_code_kavenegar('09123456789', '12345')  # با استفاده از کلید API و شماره فرستنده تنظیم شده

    # ===================================================================
    # مثال‌های استفاده از Firebase Cloud Messaging با async-firebase
    # ===================================================================

    # مثال ارسال نوتیفیکیشن به چند کاربر (روش قدیمی - سازگار با کد قبلی)
    # device_tokens = ['token1', 'token2', 'token3']
    # response = send_notification(
    #     ids=device_tokens,
    #     title='عنوان نوتیفیکیشن',
    #     body='متن نوتیفیکیشن',
    #     data={'key1': 'value1', 'key2': 'value2'}
    # )
    # print(f"Firebase notification response: {response}")

    # مثال ارسال نوتیفیکیشن گروهی (روش جدید - بهتر برای ارسال به همه کاربران)
    # device_tokens = ['token1', 'token2', 'token3', ...]  # لیست همه توکن‌های کاربران
    # response = send_bulk_notification(
    #     ids=device_tokens,
    #     title='اطلاعیه مهم',
    #     body='این پیام برای همه کاربران ارسال شده است',
    #     data={'type': 'announcement', 'priority': 'high'}
    # )
    # print(f"Firebase bulk notification response: {response}")

    print("برای تست متدهای ارسال پیام، کامنت‌های بالا را از حالت کامنت خارج کنید.")
    print("توجه: برای استفاده از Firebase، ابتدا کتابخانه async-firebase را نصب کنید:")
    print("pip install async-firebase==4.0.0")

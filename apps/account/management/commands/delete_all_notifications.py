from django.core.management.base import BaseCommand
from django.db import transaction
from apps.account.models import Notification


class Command(BaseCommand):
    help = 'حذف همه نوتیفیکیشن‌ها از دیتابیس'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='اجبار در حذف بدون درخواست تایید',
        )

    def handle(self, *args, **options):
        force = options['force']
        user_id = None
        
        if user_id:
            # حذف نوتیفیکیشن‌های کاربر خاص
            notifications = Notification.objects.filter(user_id=user_id)
            count = notifications.count()
            
            if count == 0:
                self.stdout.write(
                    self.style.WARNING(f'هیچ نوتیفیکیشنی برای کاربر با ID {user_id} یافت نشد.')
                )
                return
            
            if not force:
                confirm = input(
                    f'آیا مطمئن هستید که می‌خواهید {count} نوتیفیکیشن کاربر با ID {user_id} را حذف کنید؟ (y/N): '
                )
                if confirm.lower() != 'y':
                    self.stdout.write(self.style.WARNING('عملیات لغو شد.'))
                    return
            
            with transaction.atomic():
                notifications.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f'{count} نوتیفیکیشن کاربر با ID {user_id} با موفقیت حذف شد.')
            )
        else:
            # حذف همه نوتیفیکیشن‌ها
            count = Notification.objects.count()
            
            if count == 0:
                self.stdout.write(
                    self.style.WARNING('هیچ نوتیفیکیشنی در دیتابیس وجود ندارد.')
                )
                return
            
            if not force:
                confirm = input(
                    f'آیا مطمئن هستید که می‌خواهید همه {count} نوتیفیکیشن را حذف کنید؟ (y/N): '
                )
                if confirm.lower() != 'y':
                    self.stdout.write(self.style.WARNING('عملیات لغو شد.'))
                    return
            
            with transaction.atomic():
                Notification.objects.all().delete()
            
            self.stdout.write(
                self.style.SUCCESS(f'{count} نوتیفیکیشن با موفقیت حذف شد.')
            )
            
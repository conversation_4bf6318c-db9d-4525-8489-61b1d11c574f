from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils.translation import gettext as _
from rest_framework.authtoken.models import Token
import logging

# Import all models that have relationships with User
from apps.account.models import User, LoginHistory, Notification
from apps.region.models import UserRegion
from apps.deposit.models import (
    Deposit, DepositMembership, DepositDueDate, DepositMedia, DepositMediaImage
)
from apps.loan.models import Loan, LoanInstallment
from apps.payment.models import Payment
from apps.transaction.models import Transaction
from apps.request.models import (
    RequestCreateDeposit, RequestJoinDeposit, LoanRequest, WithdrawalRequest
)
from apps.voting.models import VotingPoll, VotingOption, Vote
from apps.voting.models.reporting import VotingReport, VotingReportImage
from apps.lottery.models import DepositLottery
from apps.ticket.models import Ticket, TicketMessage
from apps.issues.models import IssueReport


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Delete all User instances and their related data (except <EMAIL>)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting anything',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Skip confirmation prompts',
        )
        parser.add_argument(
            '--admin-email',
            type=str,
            default='<EMAIL>',
            help='Email of the admin user to preserve (default: <EMAIL>)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        admin_email = options['admin_email']

        self.stdout.write(
            self.style.WARNING('=' * 80)
        )
        self.stdout.write(
            self.style.WARNING('DELETE ALL USERS AND RELATED DATA (EXCEPT ADMIN)')
        )
        self.stdout.write(
            self.style.WARNING('=' * 80)
        )

        if dry_run:
            self.stdout.write(
                self.style.NOTICE('🔍 DRY RUN MODE - No data will be deleted')
            )
        else:
            self.stdout.write(
                self.style.ERROR('⚠️  LIVE MODE - Data will be permanently deleted!')
            )

        # Find admin user
        try:
            admin_user = User.objects.get(email=admin_email, is_staff=True)
            self.stdout.write(
                self.style.SUCCESS(f'✅ Found admin user: {admin_user.fullname} ({admin_email})')
            )
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(
                    f'❌ Admin user with email {admin_email} and is_staff=True not found!'
                )
            )
            return
        except User.MultipleObjectsReturned:
            self.stdout.write(
                self.style.ERROR(
                    f'❌ Multiple admin users found with email {admin_email}!'
                )
            )
            return

        # Get all users except admin
        users_to_delete = User.objects.exclude(id=admin_user.id)
        total_users = users_to_delete.count()

        if total_users == 0:
            self.stdout.write(
                self.style.SUCCESS('✅ No users found to delete (only admin exists).')
            )
            return

        self.stdout.write(f'\n📊 Found {total_users} users to delete (preserving admin)\n')

        # Show summary of what will be deleted
        self._show_deletion_summary(users_to_delete, admin_user, dry_run)

        if not dry_run and not force:
            # Ask for confirmation
            confirm = input(
                f'\n⚠️  Are you sure you want to delete ALL {total_users} users '
                f'and their related data? Admin user ({admin_email}) will be preserved.\n'
                'This action cannot be undone!\n'
                'Type "DELETE ALL USERS" to confirm: '
            )
            if confirm != "DELETE ALL USERS":
                self.stdout.write(
                    self.style.ERROR('❌ Operation cancelled.')
                )
                return

        # Process deletion
        if dry_run:
            self._dry_run_deletion(users_to_delete, admin_user)
        else:
            self._execute_deletion(users_to_delete, admin_user)

    def _show_deletion_summary(self, users_to_delete, admin_user, dry_run):
        """Show summary of what will be deleted"""
        self.stdout.write('📋 DELETION SUMMARY:')
        self.stdout.write('-' * 40)

        total_counts = {
            'users': users_to_delete.count(),
            'user_regions': 0,
            'login_history': 0,
            'notifications': 0,
            'user_preferences': 0,
            'tokens': 0,
            'deposits_owned': 0,
            'deposit_memberships': 0,
            'transactions': 0,
            'payments': 0,
            'loans': 0,
            'loan_installments': 0,
            'lotteries': 0,
            'voting_polls': 0,
            'votes': 0,
            'voting_reports': 0,
            'tickets': 0,
            'ticket_messages': 0,
            'issue_reports': 0,
            'create_requests': 0,
            'join_requests': 0,
            'loan_requests': 0,
            'withdrawal_requests': 0,
        }

        # Calculate totals for users to delete
        for user in users_to_delete:
            # Direct user relationships
            try:
                user_regions_count = user.region_memberships.count()
                total_counts['user_regions'] += user_regions_count
                total_counts['login_history'] += user.login_history.count()
                total_counts['notifications'] += user.notifications.count()
                total_counts['tokens'] += Token.objects.filter(user=user).count()
            except Exception as e:
                logger.warning(f"Error counting direct user relationships: {e}")
            
            # if UserPreferenceModel:
            #     try:
            #         total_counts['user_preferences'] += UserPreferenceModel.objects.filter(instance=user).count()
            #     except Exception as e:
            #         # Handle case where the table doesn't exist
            #         logger.warning(f"Could not count user preferences: {e}")
            #         # Make sure the key exists in the dictionary
            #         if 'user_preferences' not in total_counts:
            #             total_counts['user_preferences'] = 0

            # Deposit-related counts
            try:
                total_counts['deposits_owned'] += user.deposits.count()
                total_counts['deposit_memberships'] += user.deposit_memberships.count()
                total_counts['transactions'] += user.deposit_transactions.count()
                total_counts['payments'] += user.payments.count()
            except Exception as e:
                logger.warning(f"Error counting deposit-related data: {e}")
            
            # Loan-related counts
            try:
                for membership in user.deposit_memberships.all():
                    try:
                        loans = membership.loans.all()
                        total_counts['loans'] += loans.count()
                        for loan in loans:
                            total_counts['loan_installments'] += loan.installments.count()
                        total_counts['lotteries'] += membership.winners.count()
                        total_counts['votes'] += membership.votes.count()
                        total_counts['payments'] += membership.payments.count()
                        total_counts['withdrawal_requests'] += membership.withdrawal_requests.count()
                    except Exception as e:
                        logger.warning(f"Error counting membership-related data: {e}")
            except Exception as e:
                logger.warning(f"Error accessing user deposit memberships: {e}")

            # Other user-related counts
            try:
                total_counts['voting_polls'] += VotingPoll.objects.filter(deposit__owner=user).count()
                total_counts['voting_reports'] += VotingReport.objects.filter(deposit__owner=user).count()
                total_counts['tickets'] += user.tickets.count()
                total_counts['ticket_messages'] += user.ticket_messages.count()
                total_counts['issue_reports'] += user.issue_reports.count()
                total_counts['create_requests'] += user.request_create_deposits.count()
                total_counts['join_requests'] += user.request_join_deposits.count()
                total_counts['loan_requests'] += user.loan_requests.count()
            except Exception as e:
                logger.warning(f"Error counting user-related models: {e}")

        # Calculate admin user's related data that will be cleaned
        admin_counts = {
            'admin_deposits_owned': 0,
            'admin_deposit_memberships': 0,
            'admin_transactions': 0,
            'admin_payments': 0,
            'admin_tickets': 0,
            'admin_ticket_messages': 0,
            'admin_issue_reports': 0,
            'admin_create_requests': 0,
            'admin_join_requests': 0,
            'admin_loan_requests': 0,
        }
        
        try:
            admin_counts['admin_deposits_owned'] = admin_user.deposits.count()
            admin_counts['admin_deposit_memberships'] = admin_user.deposit_memberships.count()
            admin_counts['admin_transactions'] = admin_user.deposit_transactions.count()
            admin_counts['admin_payments'] = admin_user.payments.count()
            admin_counts['admin_tickets'] = admin_user.tickets.count()
            admin_counts['admin_ticket_messages'] = admin_user.ticket_messages.count()
            admin_counts['admin_issue_reports'] = admin_user.issue_reports.count()
            admin_counts['admin_create_requests'] = admin_user.request_create_deposits.count()
            admin_counts['admin_join_requests'] = admin_user.request_join_deposits.count()
            admin_counts['admin_loan_requests'] = admin_user.loan_requests.count()
        except Exception as e:
            logger.warning(f"Error counting admin-related data: {e}")

        # Display counts for users to be deleted
        self.stdout.write('\n🗑️  Items to be deleted:')
        for key, count in total_counts.items():
            if count > 0:
                self.stdout.write(f'  • {key.replace("_", " ").title()}: {count:,}')

        # Display admin cleanup counts
        admin_cleanup_total = sum(admin_counts.values())
        if admin_cleanup_total > 0:
            self.stdout.write('\n🧹 Admin user related data to be cleaned:')
            for key, count in admin_counts.items():
                if count > 0:
                    display_name = key.replace('admin_', '').replace('_', ' ').title()
                    self.stdout.write(f'  • {display_name}: {count:,}')

        action = "would be deleted" if dry_run else "will be deleted"
        total_items = sum(total_counts.values()) + admin_cleanup_total
        self.stdout.write(f'\n📊 Total items that {action}: {total_items:,}')
        self.stdout.write(f'👤 Admin user ({admin_user.email}) will be preserved')

    def _dry_run_deletion(self, users_to_delete, admin_user):
        """Show what would be deleted in dry run mode"""
        self.stdout.write('\n🔍 DRY RUN - Showing deletion process:')
        self.stdout.write('-' * 50)

        # Show admin cleanup first
        self.stdout.write(f'\n1. Cleaning admin user related data: {admin_user.fullname} ({admin_user.email})')
        self._show_admin_cleanup_info(admin_user)

        # Show user deletions
        self.stdout.write(f'\n2. Deleting {users_to_delete.count()} users and their data:')
        for i, user in enumerate(users_to_delete[:5], 1):  # Show first 5 as example
            self.stdout.write(f'\n  {i}. User: {user.fullname} ({user.email})')
            self._show_user_related_info(user)

        if users_to_delete.count() > 5:
            self.stdout.write(f'\n  ... and {users_to_delete.count() - 5} more users')

        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Dry run completed. {users_to_delete.count()} users would be deleted.'
            )
        )

    def _execute_deletion(self, users_to_delete, admin_user):
        """Execute the actual deletion"""
        self.stdout.write('\n🗑️  Starting deletion process...')
        self.stdout.write('-' * 50)

        deleted_count = 0
        
        try:
            with transaction.atomic():
                # First, clean admin user's related data
                self.stdout.write(f'\n1. Cleaning admin user related data: {admin_user.fullname}')
                self._clean_admin_related_data(admin_user)

                # Then delete all other users
                self.stdout.write(f'\n2. Deleting {users_to_delete.count()} users...')
                for i, user in enumerate(users_to_delete, 1):
                    self.stdout.write(
                        f'\n{i}/{users_to_delete.count()} Deleting user: {user.fullname} ({user.email})'
                    )
                    
                    # Delete user-related models in correct order
                    self._delete_user_related_models(user)
                    
                    # Finally delete the user
                    user.delete()
                    deleted_count += 1
                    
                    if i % 10 == 0:  # Progress update every 10 users
                        self.stdout.write(f'  ✅ Deleted {i} users so far...')

            self.stdout.write(
                self.style.SUCCESS(
                    f'\n🎉 Successfully deleted {deleted_count} users and cleaned admin data!\n'
                    f'👤 Admin user ({admin_user.email}) preserved.'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f'\n❌ Error during deletion: {str(e)}\n'
                    'Transaction rolled back. No data was deleted.'
                )
            )
            logger.error(f"User deletion failed: {str(e)}", exc_info=True)

    def _clean_admin_related_data(self, admin_user):
        """Clean admin user's related data but keep the user account"""
        
        # Delete admin's deposit memberships (but keep deposits they own)
        membership_count = admin_user.deposit_memberships.count()
        if membership_count > 0:
            self.stdout.write(f'  - Cleaning {membership_count} admin deposit memberships')
            # For each membership, clean related data first
            for membership in admin_user.deposit_memberships.all():
                # Delete loans and installments
                loans = membership.loans.all()
                for loan in loans:
                    loan.installments.all().delete()
                loans.delete()
                
                # Delete lottery wins
                membership.winners.all().delete()
                
                # Delete votes
                membership.votes.all().delete()
                
                # Delete payments
                membership.payments.all().delete()
                
                # Delete withdrawal requests
                membership.withdrawal_requests.all().delete()
            
            # Now delete the memberships
            admin_user.deposit_memberships.all().delete()

        # Delete admin's transactions
        transaction_count = admin_user.deposit_transactions.count()
        if transaction_count > 0:
            self.stdout.write(f'  - Cleaning {transaction_count} admin transactions')
            admin_user.deposit_transactions.all().delete()

        # Delete admin's payments
        payment_count = admin_user.payments.count()
        if payment_count > 0:
            self.stdout.write(f'  - Cleaning {payment_count} admin payments')
            admin_user.payments.all().delete()

        # Delete admin's tickets and messages
        tickets = admin_user.tickets.all()
        for ticket in tickets:
            ticket.messages.all().delete()
        ticket_count = tickets.count()
        if ticket_count > 0:
            self.stdout.write(f'  - Cleaning {ticket_count} admin tickets')
            tickets.delete()

        # Delete admin's ticket messages
        message_count = admin_user.ticket_messages.count()
        if message_count > 0:
            self.stdout.write(f'  - Cleaning {message_count} admin ticket messages')
            admin_user.ticket_messages.all().delete()

        # Delete admin's issue reports
        issue_count = admin_user.issue_reports.count()
        if issue_count > 0:
            self.stdout.write(f'  - Cleaning {issue_count} admin issue reports')
            admin_user.issue_reports.all().delete()

        # Delete admin's requests
        create_req_count = admin_user.request_create_deposits.count()
        if create_req_count > 0:
            self.stdout.write(f'  - Cleaning {create_req_count} admin create requests')
            admin_user.request_create_deposits.all().delete()

        join_req_count = admin_user.request_join_deposits.count()
        if join_req_count > 0:
            self.stdout.write(f'  - Cleaning {join_req_count} admin join requests')
            admin_user.request_join_deposits.all().delete()

        loan_req_count = admin_user.loan_requests.count()
        if loan_req_count > 0:
            self.stdout.write(f'  - Cleaning {loan_req_count} admin loan requests')
            admin_user.loan_requests.all().delete()

        # Keep admin's deposits but clean their related data
        deposits_count = admin_user.deposits.count()
        if deposits_count > 0:
            self.stdout.write(f'  - Keeping {deposits_count} admin-owned deposits (cleaning their data)')
            for deposit in admin_user.deposits.all():
                # Clean deposit data but keep the deposit itself
                self._clean_deposit_data_for_admin(deposit)

    def _clean_deposit_data_for_admin(self, deposit):
        """Clean deposit data but keep the deposit for admin"""
        # This will clean all user-related data from deposits owned by admin
        # but keep the deposit structure intact
        
        # Delete all memberships except admin's (if any)
        deposit.members.all().delete()
        
        # Delete all transactions
        deposit.transactions.all().delete()
        
        # Delete all payments
        deposit.payments.all().delete()
        
        # Delete all requests
        deposit.create_requests.all().delete()
        deposit.join_requests.all().delete()
        deposit.loan_requests.all().delete()
        deposit.withdrawal_requests.all().delete()
        
        # Delete voting data
        for poll in deposit.voting_polls.all():
            poll.votes.all().delete()
            poll.options.all().delete()
        deposit.voting_polls.all().delete()
        
        # Delete reports
        for report in deposit.reports.all():
            report.images.all().delete()
        deposit.reports.all().delete()
        
        # Delete tickets
        for ticket in deposit.tickets.all():
            ticket.messages.all().delete()
        deposit.tickets.all().delete()
        
        # Delete issue reports
        deposit.issue_reports.all().delete()
        
        # Delete lotteries
        deposit.deposit_winners.all().delete()
        
        # Delete loans (already handled in membership cleanup)
        for loan in deposit.loans.all():
            loan.installments.all().delete()
        deposit.loans.all().delete()

    def _delete_user_related_models(self, user):
        """Delete all models related to a user in the correct order"""
        


        # 2. Delete authentication tokens
        token_count = Token.objects.filter(user=user).count()
        if token_count > 0:
            self.stdout.write(f'    - Deleting {token_count} auth tokens')
            Token.objects.filter(user=user).delete()

        # 3. Delete login history
        login_count = user.login_history.count()
        if login_count > 0:
            self.stdout.write(f'    - Deleting {login_count} login history records')
            user.login_history.all().delete()

        # 4. Delete notifications
        notification_count = user.notifications.count()
        if notification_count > 0:
            self.stdout.write(f'    - Deleting {notification_count} notifications')
            user.notifications.all().delete()

        # 5. Delete user region memberships
        region_memberships_count = user.region_memberships.count()
        if region_memberships_count > 0:
            self.stdout.write(f'    - Deleting {region_memberships_count} user region memberships')
            user.region_memberships.all().delete()

        # 6. Delete deposits owned by user (this will cascade to related data)
        deposit_count = user.deposits.count()
        if deposit_count > 0:
            self.stdout.write(f'    - Deleting {deposit_count} owned deposits')
            # Note: This will cascade delete all related deposit data
            user.deposits.all().delete()

        # 7. Delete deposit memberships and related data
        for membership in user.deposit_memberships.all():
            # Delete loans and installments
            loans = membership.loans.all()
            for loan in loans:
                loan.installments.all().delete()
            loans.delete()
            
            # Delete lottery wins
            membership.winners.all().delete()
            
            # Delete votes
            membership.votes.all().delete()
            
            # Delete payments
            membership.payments.all().delete()
            
            # Delete withdrawal requests
            membership.withdrawal_requests.all().delete()

        membership_count = user.deposit_memberships.count()
        if membership_count > 0:
            self.stdout.write(f'    - Deleting {membership_count} deposit memberships')
            user.deposit_memberships.all().delete()

        # 8. Delete remaining user transactions
        transaction_count = user.deposit_transactions.count()
        if transaction_count > 0:
            self.stdout.write(f'    - Deleting {transaction_count} transactions')
            user.deposit_transactions.all().delete()

        # 9. Delete remaining user payments
        payment_count = user.payments.count()
        if payment_count > 0:
            self.stdout.write(f'    - Deleting {payment_count} payments')
            user.payments.all().delete()

        # 10. Delete tickets and messages
        tickets = user.tickets.all()
        for ticket in tickets:
            ticket.messages.all().delete()
        ticket_count = tickets.count()
        if ticket_count > 0:
            self.stdout.write(f'    - Deleting {ticket_count} tickets')
            tickets.delete()

        # 11. Delete ticket messages
        message_count = user.ticket_messages.count()
        if message_count > 0:
            self.stdout.write(f'    - Deleting {message_count} ticket messages')
            user.ticket_messages.all().delete()

        # 12. Delete issue reports
        issue_count = user.issue_reports.count()
        if issue_count > 0:
            self.stdout.write(f'    - Deleting {issue_count} issue reports')
            user.issue_reports.all().delete()

        # 13. Delete request models
        self._delete_user_request_models(user)

    def _delete_user_request_models(self, user):
        """Delete all request models related to a user"""
        
        # Delete create requests
        create_req_count = user.request_create_deposits.count()
        if create_req_count > 0:
            self.stdout.write(f'    - Deleting {create_req_count} create requests')
            user.request_create_deposits.all().delete()

        # Delete join requests
        join_req_count = user.request_join_deposits.count()
        if join_req_count > 0:
            self.stdout.write(f'    - Deleting {join_req_count} join requests')
            user.request_join_deposits.all().delete()

        # Delete loan requests
        loan_req_count = user.loan_requests.count()
        if loan_req_count > 0:
            self.stdout.write(f'    - Deleting {loan_req_count} loan requests')
            user.loan_requests.all().delete()

    def _show_admin_cleanup_info(self, admin_user):
        """Show information about admin cleanup in dry run mode"""
        cleanup_counts = {
            'Deposit Memberships': admin_user.deposit_memberships.count(),
            'Transactions': admin_user.deposit_transactions.count(),
            'Payments': admin_user.payments.count(),
            'Tickets': admin_user.tickets.count(),
            'Ticket Messages': admin_user.ticket_messages.count(),
            'Issue Reports': admin_user.issue_reports.count(),
            'Create Requests': admin_user.request_create_deposits.count(),
            'Join Requests': admin_user.request_join_deposits.count(),
            'Loan Requests': admin_user.loan_requests.count(),
            'Owned Deposits': admin_user.deposits.count(),
        }

        for model_name, count in cleanup_counts.items():
            if count > 0:
                action = "would be cleaned" if model_name == "Owned Deposits" else "would be deleted"
                self.stdout.write(f'    - {count} {model_name} {action}')

    def _show_user_related_info(self, user):
        """Show information about user-related models in dry run mode"""
        related_counts = {
            'Region Memberships': user.region_memberships.count(),
            'Login History': user.login_history.count(),
            'Notifications': user.notifications.count(),
            'Auth Tokens': Token.objects.filter(user=user).count(),
            'Owned Deposits': user.deposits.count(),
            'Deposit Memberships': user.deposit_memberships.count(),
            'Transactions': user.deposit_transactions.count(),
            'Payments': user.payments.count(),
            'Tickets': user.tickets.count(),
            'Ticket Messages': user.ticket_messages.count(),
            'Issue Reports': user.issue_reports.count(),
            'Create Requests': user.request_create_deposits.count(),
            'Join Requests': user.request_join_deposits.count(),
            'Loan Requests': user.loan_requests.count(),
        }

        # if UserPreferenceModel:
        #     try:
        #         related_counts['User Preferences'] = UserPreferenceModel.objects.filter(instance=user).count()
        #     except Exception as e:
        #         logger.warning(f"Could not count user preferences: {e}")
        #         related_counts['User Preferences'] = 0

        # Calculate nested counts
        for membership in user.deposit_memberships.all():
            related_counts['Loans'] = related_counts.get('Loans', 0) + membership.loans.count()
            related_counts['Lottery Wins'] = related_counts.get('Lottery Wins', 0) + membership.winners.count()
            related_counts['Votes'] = related_counts.get('Votes', 0) + membership.votes.count()

        # Display non-zero counts
        for model_name, count in related_counts.items():
            if count > 0:
                self.stdout.write(f'      - {count} {model_name}')

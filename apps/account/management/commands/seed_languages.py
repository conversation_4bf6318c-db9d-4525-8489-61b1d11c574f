from django.core.management.base import BaseCommand
from django.db import transaction
from dj_language.models import Language


class Command(BaseCommand):
    help = 'Seed default languages for the application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear all existing languages before seeding',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating records',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        clear_existing = options['clear']
        
        # Default languages to seed
        default_languages = [
            {
                'id': 69,
                'code': 'fa',
                'name': 'Persian',
                'native_name': 'فارسی',
                'status': True,
                'is_default': True,
            },
            {
                'id': 70,
                'code': 'en',
                'name': 'English',
                'native_name': 'English',
                'status': True,
                'is_default': False,
            },
            {
                'id': 71,
                'code': 'ar',
                'name': 'Arabic',
                'native_name': 'العربية',
                'status': True,
                'is_default': False,
            },
            {
                'id': 72,
                'code': 'tr',
                'name': 'Turkish',
                'native_name': 'Türkçe',
                'status': True,
                'is_default': False,
            },
            {
                'id': 73,
                'code': 'az',
                'name': 'Azerbaijani',
                'native_name': 'Azərbaycan dili',
                'status': True,
                'is_default': False,
            },
            {
                'id': 74,
                'code': 'ru',
                'name': 'Russian',
                'native_name': 'Русский',
                'status': True,
                'is_default': False,
            },
            {
                'id': 75,
                'code': 'ur',
                'name': 'Urdu',
                'native_name': 'اردو',
                'status': True,
                'is_default': False,
            },
            {
                'id': 76,
                'code': 'de',
                'name': 'German',
                'native_name': 'Deutsch',
                'status': True,
                'is_default': False,
            },
            {
                'id': 77,
                'code': 'fr',
                'name': 'French',
                'native_name': 'Français',
                'status': True,
                'is_default': False,
            },
            {
                'id': 78,
                'code': 'es',
                'name': 'Spanish',
                'native_name': 'Español',
                'status': True,
                'is_default': False,
            },
            {
                'id': 79,
                'code': 'id',
                'name': 'Indonesian',
                'native_name': 'Bahasa Indonesia',
                'status': True,
                'is_default': False,
            },
            {
                'id': 80,
                'code': 'sw',
                'name': 'Swahili',
                'native_name': 'Kiswahili',
                'status': True,
                'is_default': False,
            },
        ]
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('🧪 DRY RUN MODE - No records will be created')
            )
        
        # Clear existing languages if requested
        if clear_existing and not dry_run:
            existing_count = Language.objects.count()
            if existing_count > 0:
                confirm = input(f'Are you sure you want to delete {existing_count} existing languages? (y/N): ')
                if confirm.lower() == 'y':
                    Language.objects.all().delete()
                    self.stdout.write(
                        self.style.WARNING(f'🗑️  {existing_count} existing languages deleted')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR('❌ Operation cancelled')
                    )
                    return
        
        created_count = 0
        updated_count = 0
        skipped_count = 0
        
        with transaction.atomic():
            for language_data in default_languages:
                language_id = language_data['id']
                code = language_data['code']
                
                # Check if language already exists
                existing_language = Language.objects.filter(id=language_id).first()
                
                if existing_language:
                    if not dry_run:
                        # Update existing language
                        for key, value in language_data.items():
                            setattr(existing_language, key, value)
                        existing_language.save()
                    
                    self.stdout.write(f'   🔄 Language "{code}" (ID: {language_id}) updated')
                    updated_count += 1
                else:
                    if not dry_run:
                        # Create new language
                        Language.objects.create(**language_data)
                    
                    self.stdout.write(f'   ✅ Language "{code}" (ID: {language_id}) created')
                    created_count += 1
        
        # Display summary
        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS(f'📊 Summary:')
        )
        self.stdout.write(f'   • Languages created: {created_count}')
        self.stdout.write(f'   • Languages updated: {updated_count}')
        self.stdout.write(f'   • Languages skipped: {skipped_count}')
        
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n✅ Successfully seeded {created_count + updated_count} languages. '
                    f'Language ID 69 (Persian) is now available for User model.'
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    f'\n🧪 Dry run completed. Would create/update {created_count + updated_count} languages.'
                )
            ) 
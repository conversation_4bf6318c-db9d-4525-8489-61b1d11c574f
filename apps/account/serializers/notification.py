

from rest_framework import serializers
from apps.account.models import Notification
from apps.account.models import User




class NotificationSerializer(serializers.ModelSerializer):

    class Meta:
        model = Notification
        fields = ['id', 'title', 'message', 'is_read', 'data','created_at']



class NotificationSendSerializer(serializers.Serializer):
    title = serializers.CharField()
    body = serializers.CharField()
    data = serializers.DictField(required=False)
    account_id = serializers.CharField(required=True)


from django.urls import path, include

from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from apps.account import views



urlpatterns = [
    # URL for user registration, accepts POST requests for creating new user instances.

    path('register/', views.UserRegisterView.as_view(), name='user-register'),
    path('verify/', views.UserVerifyView.as_view(), name='user-verify'),
    path('login/', views.UserLoginView.as_view(), name='user-login'),

    path('invitaion/', views.RegisterWithInvitationAPIView.as_view(), name='user-invitaion'),

    path('notif/', views.NotificationListView.as_view(), name='user-notif'),
    path('notif/read', views.NotificationReadAllView.as_view(), name='user-notif-read-all'),
    path('notif/send/', views.SendNotificationView.as_view(), name='user-send-notif'),
    path('notif/send/bulk/', views.SendBulkNotificationView.as_view(), name='user-send-notif-bulk'),


    # # URL to get user details, supports GET for fetching user profile based on the provided token.
    path('profile/', views.UserProfileView.as_view(), name='user-profile'),

    path('recover/', views.UserRecoverPassword.as_view(), name='user-recover'),
    path('reset/', views.UserResetPassword.as_view(), name='user-reset'),

    
    # # URL to update user details, supports PUT to update user fields like phone or email given a token.
    path('profile/update/', views.UserUpdateView.as_view(), name='user-update'),

    # # delete user account
    path('profile/delete/', views.UserDeleteView.as_view(), name='user-delete'),
    path('update-fcm/', views.UpdateFCMView.as_view(), name='update-fcm'),

]
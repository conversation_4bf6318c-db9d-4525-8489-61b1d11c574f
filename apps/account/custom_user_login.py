from django.contrib.auth.backends import BaseBackend
from django.db.models import Q

from apps.account.models import User
from utils.exceptions import UserNotFoundException
from rest_framework.exceptions import AuthenticationFailed


class CustomLoginBackend(BaseBackend):
    """
    Authenticate with username email and phone_number.
    """

    def authenticate(self, request, username=None, password=None):
        print(f'----> {username}/{password}')
        if user := self.get_user(username):
            if user.check_password(password):
                return user

        return None

    def get_user(self, username):
        try:
            print(f'---> {username}')
            user = User.objects.filter(Q(email=username) | Q(phone_number=username)).first()
            return user
        except Exception as e:
            
            print(f'Error in get_user: {e}')
            return None

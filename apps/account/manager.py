
from django.contrib.auth.models import BaseUserManager
from django.contrib.auth.models import Group


from django.db.models import Manager



class UserManager(BaseUserManager):

    def create_user(
        self,
        email: str = None,
        phone_number: str = None,
        fullname: str = None,
        password: str = None,
    ):
        user = self.model(
            email=email,
            fullname=fullname,
            phone_number=phone_number,
        )
        user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_superuser(self, email, phone_number, fullname, password):
        user = self.create_user(
            email=email,
            phone_number=phone_number,
            fullname=fullname,
            password=password,
        )
        user.is_admin = True
        user.is_staff = True
        user.is_superuser = True
        user.is_active = True
        user.save(using=self._db)
        return user

        
    

class AdminUserManager(UserManager):
    def get_queryset(self):
        return super().get_queryset().filter(is_admin=True)


class RegionOwnerUserManager(UserManager):
    def get_queryset(self):
        return super().get_queryset().filter(regions__isnull=False)



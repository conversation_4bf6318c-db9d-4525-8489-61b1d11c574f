from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status

# ============================================================================
# STRUCTURED SWAGGER DOCUMENTATION FOR ACCOUNT APP
# ============================================================================

# User Registration Swagger Documentation
user_register_swagger = swagger_auto_schema(
    operation_description="Register a new user account with phone number verification",
    operation_summary="User Registration",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['phone_number', 'email', 'password', 'fullname'],
        properties={
            'phone_number': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User phone number with country code',
                example='+************'
            ),
            'email': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_EMAIL,
                description='User email address',
                example='<EMAIL>'
            ),
            'password': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User password (minimum 8 characters)',
                example='securepassword123'
            ),
            'fullname': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User full name',
                example='John Doe'
            )
        }
    ),
    responses={
        status.HTTP_202_ACCEPTED: openapi.Response(
            description="Registration successful, OTP code sent",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'user': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'phone_number': openapi.Schema(type=openapi.TYPE_STRING, example='+************'),
                            'email': openapi.Schema(type=openapi.TYPE_STRING, example='<EMAIL>'),
                            'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='John Doe')
                        }
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example="The otp code was sent to the user's phone"
                    )
                }
            ),
            examples={
                'application/json': {
                    "user": {
                        "phone_number": "+************",
                        "email": "<EMAIL>",
                        "fullname": "John Doe"
                    },
                    "message": "The otp code was sent to the user's phone"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'phone_number': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['This phone number is already registered.']
                    ),
                    'email': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['Enter a valid email address.']
                    ),
                    'password': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['This password is too short. It must contain at least 8 characters.']
                    )
                }
            ),
            examples={
                'application/json': {
                    "phone_number": ["This phone number is already registered."]
                }
            }
        )
    }
)

# User Verification Swagger Documentation
user_verify_swagger = swagger_auto_schema(
    operation_description="Verify user account with OTP code and optionally set name and family",
    operation_summary="User Account Verification",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['phone_number', 'code'],
        properties={
            'phone_number': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User phone number with country code',
                example='+************'
            ),
            'code': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='OTP verification code',
                example='12345'
            ),
            'name': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User first name (optional)',
                example='John'
            ),
            'family': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User last name (optional)',
                example='Doe'
            ),
            'fcm': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='FCM token for push notifications (optional)',
                example='fcm_token_example'
            ),
            'mobile_device_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Mobile device identifier (optional)',
                example='device_123'
            ),
            'timezone': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User timezone (optional)',
                example='Asia/Tehran'
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Account verified successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'token': openapi.Schema(type=openapi.TYPE_STRING, example='xyz987uvw654'),
                    'user_id': openapi.Schema(type=openapi.TYPE_INTEGER, example=2),
                    'phone_number': openapi.Schema(type=openapi.TYPE_STRING, example='+************'),
                    'email': openapi.Schema(type=openapi.TYPE_STRING, example='<EMAIL>'),
                    'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='John Doe'),
                    'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                    'avatar': openapi.Schema(type=openapi.TYPE_STRING, example='http://example.com/media/avatars/user.jpg')
                }
            ),
            examples={
                'application/json': {
                    "token": "xyz987uvw654",
                    "user_id": 2,
                    "phone_number": "+************",
                    "email": "<EMAIL>",
                    "fullname": "John Doe",
                    "is_active": True,
                    "avatar": "http://example.com/media/avatars/user.jpg"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Invalid or expired verification code",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'code': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['code notfound']
                    )
                }
            ),
            examples={
                'application/json': {
                    "code": ["The verification code has expired."]
                }
            }
        )
    }
)

# User Login Swagger Documentation
user_login_swagger = swagger_auto_schema(
    operation_description="Authenticate user with email and password",
    operation_summary="User Login",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['email', 'password'],
        properties={
            'email': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_EMAIL,
                description='User email address',
                example='<EMAIL>'
            ),
            'password': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User password',
                example='securepassword123'
            ),
            'fcm': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='FCM token for push notifications (optional)',
                example='fcm_token_example'
            ),
            'device_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Device identifier (optional)',
                example='device_id_example'
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Login successful",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'token': openapi.Schema(type=openapi.TYPE_STRING, example='abc123def456'),
                    'user_id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'phone_number': openapi.Schema(type=openapi.TYPE_STRING, example='+************'),
                    'email': openapi.Schema(type=openapi.TYPE_STRING, example='<EMAIL>'),
                    'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='John Doe'),
                    'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                    'avatar': openapi.Schema(type=openapi.TYPE_STRING, example='http://example.com/media/avatars/user.jpg')
                }
            ),
            examples={
                'application/json': {
                    "token": "abc123def456",
                    "user_id": 1,
                    "phone_number": "+************",
                    "email": "<EMAIL>",
                    "fullname": "John Doe",
                    "is_active": True,
                    "avatar": "http://example.com/media/avatars/user.jpg"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Invalid credentials",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'non_field_errors': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['Unable to log in with provided credentials.']
                    )
                }
            ),
            examples={
                'application/json': {
                    "non_field_errors": ["Unable to log in with provided credentials."]
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication failed",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Invalid credentials'
                    )
                }
            )
        )
    }
)

# User Password Recovery Swagger Documentation
user_recover_password_swagger = swagger_auto_schema(
    operation_description="Send OTP code to user's phone for password recovery",
    operation_summary="Password Recovery",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['phone_number'],
        properties={
            'phone_number': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User phone number with country code',
                example='+************'
            )
        }
    ),
    responses={
        status.HTTP_202_ACCEPTED: openapi.Response(
            description="OTP code sent successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example="The otp code was sent to the user's phone"
                    )
                }
            ),
            examples={
                'application/json': {
                    "message": "The otp code was sent to the user's phone"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="User not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'phone_number': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['User with this phone number does not exist.']
                    )
                }
            ),
            examples={
                'application/json': {
                    "phone_number": ["User with this phone number does not exist."]
                }
            }
        )
    }
)

# User Password Reset Swagger Documentation
user_reset_password_swagger = swagger_auto_schema(
    operation_description="Reset user password with new password",
    operation_summary="Password Reset",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['new_password'],
        properties={
            'new_password': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='New password (minimum 8 characters)',
                example='newsecurepassword123'
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Password reset successful",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example="Your password has been changed successfully."
                    )
                }
            ),
            examples={
                'application/json': {
                    "message": "Your password has been changed successfully."
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'new_password': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['This password is too short. It must contain at least 8 characters.']
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Register with Invitation Swagger Documentation
register_with_invitation_swagger = swagger_auto_schema(
    operation_description="Join a region using invitation code. Returns is_active and is_member status.",
    operation_summary="Register with Invitation Code",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['invitation_code'],
        properties={
            'invitation_code': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Unique invitation code',
                example='ABC123DEF456'
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="User added to region successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='User added to the region successfully.'
                    ),
                    'is_active': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='User active status',
                        example=True
                    ),
                    'is_member': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='User membership status',
                        example=True
                    )
                }
            ),
            examples={
                'application/json': {
                    "message": "User added to the region successfully.",
                    "is_active": True,
                    "is_member": True
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Invalid invitation code or validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='error'),
                    'code': openapi.Schema(type=openapi.TYPE_STRING, example='validation_error'),
                    'status_code': openapi.Schema(type=openapi.TYPE_INTEGER, example=400),
                    'message': openapi.Schema(type=openapi.TYPE_STRING, example='Invalid invitation code.')
                }
            ),
            examples={
                'application/json': {
                    "status": "error",
                    "code": "validation_error",
                    "status_code": 400,
                    "message": "Invalid invitation code."
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Notification List Swagger Documentation
notification_list_swagger = swagger_auto_schema(
    operation_description="Retrieve a list of notifications for the authenticated user",
    operation_summary="List User Notifications",
    tags=['Account'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of notifications retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                        'title': openapi.Schema(type=openapi.TYPE_STRING, example='New Message'),
                        'body': openapi.Schema(type=openapi.TYPE_STRING, example='You have received a new message'),
                        'is_read': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
                        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-12-01T10:30:00Z'),
                        'data': openapi.Schema(type=openapi.TYPE_OBJECT, example={'type': 'message', 'id': 123})
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "id": 1,
                        "title": "New Message",
                        "body": "You have received a new message",
                        "is_read": False,
                        "created_at": "2023-12-01T10:30:00Z",
                        "data": {"type": "message", "id": 123}
                    }
                ]
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Notification Read All Swagger Documentation
notification_read_all_swagger = swagger_auto_schema(
    operation_description="Mark all notifications as read for the authenticated user",
    operation_summary="Mark All Notifications as Read",
    tags=['Account'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="All notifications marked as read",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example="all notifications marked as read"
                    )
                }
            ),
            examples={
                'application/json': {
                    "status": "all notifications marked as read"
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Send Notification Swagger Documentation
send_notification_swagger = swagger_auto_schema(
    operation_description="Send a notification to a user by user_id",
    operation_summary="Send Notification to User",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['user_id', 'title', 'body'],
        properties={
            'user_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='ID of the user to send notification to',
                example=123
            ),
            'title': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Notification title',
                example='Important Update'
            ),
            'body': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Notification body',
                example='Your account has been updated successfully'
            ),
            'data': openapi.Schema(
                type=openapi.TYPE_OBJECT,
                description='Additional data payload',
                example={'slam': 'qatreh', 'type': 'update'}
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Notification sent successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Notification sent successfully'
                    ),
                    'notification_id': openapi.Schema(
                        type=openapi.TYPE_INTEGER,
                        example=456
                    )
                }
            ),
            examples={
                'application/json': {
                    "message": "Notification sent successfully",
                    "notification_id": 456
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'user_id': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['User with this ID does not exist.']
                    )
                }
            )
        ),
        status.HTTP_500_INTERNAL_SERVER_ERROR: openapi.Response(
            description="Server error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'error': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Failed to send notification'
                    )
                }
            )
        )
    }
)

# Send Bulk Notification Swagger Documentation
send_bulk_notification_swagger = swagger_auto_schema(
    operation_description="Send a notification to multiple users",
    operation_summary="Send Bulk Notification",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['user_id'],
        properties={
            'user_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='ID of the user to send notification to',
                example=123
            ),
            'title': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Notification title',
                example='Bulk Update'
            ),
            'body': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Notification body',
                example='System maintenance scheduled'
            ),
            'data': openapi.Schema(
                type=openapi.TYPE_OBJECT,
                description='Additional data payload',
                example={'slam': 'qatreh', 'type': 'maintenance'}
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Bulk notification sent successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Bulk notification sent successfully'
                    ),
                    'sent_count': openapi.Schema(
                        type=openapi.TYPE_INTEGER,
                        example=150
                    )
                }
            ),
            examples={
                'application/json': {
                    "message": "Bulk notification sent successfully",
                    "sent_count": 150
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'user_id': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['This field is required.']
                    )
                }
            )
        ),
        status.HTTP_500_INTERNAL_SERVER_ERROR: openapi.Response(
            description="Server error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'error': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Failed to send bulk notification'
                    )
                }
            )
        )
    }
)

# User Profile Swagger Documentation
user_profile_swagger = swagger_auto_schema(
    operation_description="Get user profile information",
    operation_summary="Get User Profile",
    tags=['Account'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="User profile retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='John Doe'),
                    'name': openapi.Schema(type=openapi.TYPE_STRING, example='John'),
                    'family': openapi.Schema(type=openapi.TYPE_STRING, example='Doe'),
                    'avatar': openapi.Schema(type=openapi.TYPE_STRING, example='http://example.com/media/avatars/user.jpg'),
                    'email': openapi.Schema(type=openapi.TYPE_STRING, example='<EMAIL>'),
                    'phone_number': openapi.Schema(type=openapi.TYPE_STRING, example='+************'),
                    'city': openapi.Schema(type=openapi.TYPE_STRING, example='Tehran'),
                    'birthdate': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE, example='1990-01-01'),
                    'gender': openapi.Schema(type=openapi.TYPE_STRING, example='M'),
                    'shba_number': openapi.Schema(type=openapi.TYPE_STRING, example='**************************'),
                    'is_active_in_region': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                    'invitation_code': openapi.Schema(type=openapi.TYPE_STRING, example='ABC123DEF456'),
                    'region_name': openapi.Schema(type=openapi.TYPE_STRING, example='Tehran Region'),
                    'is_ticket': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
                    'unread_ticket_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=0)
                }
            ),
            examples={
                'application/json': {
                    "id": 1,
                    "fullname": "John Doe",
                    "name": "John",
                    "family": "Doe",
                    "avatar": "http://example.com/media/avatars/user.jpg",
                    "email": "<EMAIL>",
                    "phone_number": "+************",
                    "city": "Tehran",
                    "birthdate": "1990-01-01",
                    "gender": "M",
                    "shba_number": "**************************",
                    "is_active_in_region": True,
                    "invitation_code": "ABC123DEF456",
                    "region_name": "Tehran Region",
                    "is_ticket": False,
                    "unread_ticket_count": 0
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="User does not have region membership",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='User does not have any region membership.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# User Update Swagger Documentation
user_update_swagger = swagger_auto_schema(
    operation_description="Update user profile information",
    operation_summary="Update User Profile",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='John Doe Updated'),
            'avatar': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_BINARY, description='Profile picture'),
            'email': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL, example='<EMAIL>'),
            'city': openapi.Schema(type=openapi.TYPE_STRING, example='Isfahan'),
            'birthdate': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE, example='1990-01-01'),
            'gender': openapi.Schema(type=openapi.TYPE_STRING, enum=['M', 'F'], example='M'),
            'shba_number': openapi.Schema(type=openapi.TYPE_STRING, example='**************************'),
            'password': openapi.Schema(type=openapi.TYPE_STRING, description='New password (optional)', example='newpassword123')
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Profile updated successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='John Doe Updated'),
                    'email': openapi.Schema(type=openapi.TYPE_STRING, example='<EMAIL>'),
                    'phone_number': openapi.Schema(type=openapi.TYPE_STRING, example='+************')
                }
            )
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'field_name': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['This field is required.']
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# User Delete Swagger Documentation
user_delete_swagger = swagger_auto_schema(
    operation_description="Delete user account permanently",
    operation_summary="Delete User Account",
    tags=['Account'],
    responses={
        status.HTTP_204_NO_CONTENT: openapi.Response(
            description="User account deleted successfully"
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Cannot delete admin account",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Unable to log in with provided credentials.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="User not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='User does not exist.'
                    )
                }
            )
        )
    }
)

# Update FCM Token Swagger Documentation
update_fcm_swagger = swagger_auto_schema(
    operation_description="Update user's FCM token for push notifications",
    operation_summary="Update FCM Token",
    tags=['Account'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['fcm'],
        properties={
            'fcm': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='FCM token for push notifications',
                example='fcm_token_example_123456789'
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="FCM token updated successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='FCM token updated successfully'
                    ),
                    'fcm': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='fcm_token_example_123456789'
                    )
                }
            ),
            examples={
                'application/json': {
                    "message": "FCM token updated successfully",
                    "fcm": "fcm_token_example_123456789"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="FCM token is required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'error': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='FCM token is required'
                    )
                }
            ),
            examples={
                'application/json': {
                    "error": "FCM token is required"
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

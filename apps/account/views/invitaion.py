from datetime import timedelta
from django.utils.timezone import now
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import ValidationError

from apps.account.models import User
from apps.region.models import UserRegion, Region, InvitationLink
from apps.region.tasks import activate_user_region
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from apps.account.doc import register_with_invitation_swagger




class RegisterWithInvitationAPIView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated]

    @register_with_invitation_swagger
    def post(self, request, *args, **kwargs):
        invitation_code = request.data.get('invitation_code')
        print(f'--RegisterWithInvitationAPIView--> {invitation_code}')
        user = request.user

        if not invitation_code:
            raise ValidationError({"invitation_code": "Invitation code is required."})    
        
        if '/' in invitation_code:
            invitation_code = invitation_code.split('/')[-1]
        
        # Check if user is already a member of this specific region
        # (Allow users to join multiple regions)
        
        # First try to find an InvitationLink with this invitation code
        invitation_link = None
        try:
            invitation_link = InvitationLink.objects.get(invitation_code=invitation_code)
            # Check if the invitation link is already used
            if invitation_link.is_used:
                raise ValidationError({"invitation_code": "This invitation code has already been used."})

            # If found, use the InvitationLink's region and set the inviter
            region = invitation_link.user_region.region
            invited_by = invitation_link.user_region.user
        except InvitationLink.DoesNotExist:
            # If not found in InvitationLink, try to find a UserRegion with this invitation code (backward compatibility)
            try:
                invitation_user_region = UserRegion.objects.get(invitation_code=invitation_code)
                # If found, use the UserRegion's region and set the inviter
                region = invitation_user_region.region
                invited_by = invitation_user_region.user
            except UserRegion.DoesNotExist:
                # If not found in UserRegion, try to find a Region with this invitation code
                try:
                    region = Region.objects.get(invitation_code=invitation_code)
                    invited_by = None  # No specific user invited this person
                except Region.DoesNotExist:
                    # If not found in either, the code is invalid
                    raise ValidationError({"invitation_code": "Invalid invitation code."})
        
        # Check if user is already a member of this specific region
        existing_membership = UserRegion.objects.filter(user=user, region=region).first()

        if existing_membership:
            # User is already a member of this region - make it current
            # First, set all other regions as not current
            UserRegion.objects.filter(user=user).update(is_current=False)

            # Then set this region as current
            existing_membership.is_current = True
            existing_membership.save()

            inviter_fullname = existing_membership.invited_by.fullname if existing_membership.invited_by else None
            return Response(
                {
                    "message": "User is already a member of this region. Set as current region.",
                    "is_active": user.is_active,
                    "is_member": True if user.membership_type == User.MemberShipType.MEMBER else False,
                    "invited_by_fullname": inviter_fullname
                },
                status=status.HTTP_200_OK
            )

        # Check if this is the user's first region membership
        is_first_region = not user.region_memberships.exists()

        # If user has other regions, set them all as not current
        if not is_first_region:
            UserRegion.objects.filter(user=user).update(is_current=False)

        # Create a new UserRegion for this user
        user_region = UserRegion.objects.create(
            user=user,
            region=region,
            invited_by=invited_by,
            is_active=False,
            is_current=True  # Always set new region as current
        )
        
        # Schedule the task for 3 days later
        activation_time = now() + timedelta(days=3)
        activate_user_region.apply_async((user_region.id,), eta=activation_time)

        # If using an invitation link, mark it as used
        if invitation_link:
            invitation_link.mark_as_used(user)

        # Get inviter fullname for response
        inviter_fullname = None
        if invited_by:
            if region.owner == invited_by:
                inviter_fullname = f"مدیریت {region.name}"
            else:
                inviter_fullname = invited_by.fullname

        return Response(
            {
                "message": "User added to the region successfully.",
                "is_active": user.is_active,
                "is_member": True if user.membership_type == User.MemberShipType.MEMBER else False,
                "invited_by_fullname": inviter_fullname
            },
            status=status.HTTP_200_OK
        )





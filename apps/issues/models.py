
from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.deposit.models import Deposit
from apps.account.models import User


class IssueReport(models.Model):
    class ReportStatus(models.TextChoices):
        OPEN = "Open", _("Open")
        IN_PROGRESS = "In Progress", _("In Progress")
        CLOSED = "Closed", _("Closed")  # تیکت بسته شده است

    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        verbose_name=_("User"), 
        related_name="issue_reports"
    )
    deposit = models.ForeignKey(
        Deposit, 
        on_delete=models.CASCADE, 
        verbose_name=_("Deposit"), 
        related_name="issue_reports"
    )
    subject = models.CharField(max_length=255, verbose_name=_("Subject"))
    description = models.TextField(verbose_name=_("Description"))
    status = models.CharField(
        max_length=20, 
        choices=ReportStatus.choices, 
        default=ReportStatus.OPEN, 
        verbose_name=_("Status")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Issue Report")
        verbose_name_plural = _("Issue Reports")

    def __str__(self):
        return f"Issue Report #{self.id} - {self.subject} ({self.status})"


class ReportSubject(models.Model):
    """
    Model for report subjects/categories
    """
    title = models.CharField(max_length=255, verbose_name=_("Title"))
    description = models.TextField(blank=True, null=True, verbose_name=_("Description"))
    is_active = models.BooleanField(default=True, verbose_name=_("Is Active"))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))

    class Meta:
        verbose_name = _("Report Subject")
        verbose_name_plural = _("Report Subjects")
        ordering = ['title']

    def __str__(self):
        return self.title



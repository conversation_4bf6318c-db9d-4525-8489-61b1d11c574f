
from rest_framework import serializers
from apps.issues.models import IssueReport, ReportSubject



class IssueReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = IssueReport
        fields = ['deposit', 'subject', 'description']

    def validate(self, data):
        if 'description' not in data or not data['description']:
            data['description'] = ""
        return data


class ReportSubjectSerializer(serializers.ModelSerializer):
    """Serializer for ReportSubject model"""

    class Meta:
        model = ReportSubject
        fields = ['id', 'title', 'description']



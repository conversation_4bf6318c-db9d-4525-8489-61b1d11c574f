from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.issues.models import ReportSubject


class Command(BaseCommand):
    help = 'ایجاد موضوعات گزارش‌دهی پیش‌فرض برای سیستم'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='حذف تمام موضوعات موجود قبل از ایجاد موضوعات جدید',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='نمایش موضوعاتی که ایجاد خواهند شد بدون ذخیره در دیتابیس',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        clear_existing = options['clear']
        
        # موضوعات پیش‌فرض
        default_subjects = [
            {
                'title': 'مشکل در پرداخت',
                'description': 'مشکلات مربوط به پرداخت آنلاین، عدم تأیید پرداخت، یا خطاهای درگاه پرداخت',
                'is_active': True
            },
            {
                'title': 'مشکل در عضویت صندوق',
                'description': 'مشکلات مربوط به درخواست عضویت، تأیید عضویت، یا تغییر وضعیت عضویت',
                'is_active': True
            },
            {
                'title': 'مشکل در قرعه‌کشی',
                'description': 'مشکلات مربوط به فرایند قرعه‌کشی، نتایج قرعه‌کشی، یا عدم اطلاع‌رسانی',
                'is_active': True
            },
            {
                'title': 'مشکل در وام',
                'description': 'مشکلات مربوط به درخواست وام، تأیید وام، یا بازپرداخت اقساط',
                'is_active': True
            },
            {
                'title': 'مشکل در حساب کاربری',
                'description': 'مشکلات مربوط به ورود، ثبت‌نام، تغییر اطلاعات شخصی، یا بازیابی رمز عبور',
                'is_active': True
            },
            {
                'title': 'مشکل در اطلاع‌رسانی',
                'description': 'عدم دریافت پیامک، ایمیل، یا نوتیفیکیشن‌های مربوط به صندوق',
                'is_active': True
            },
            {
                'title': 'مشکل فنی در اپلیکیشن',
                'description': 'خطاهای فنی، کرش اپلیکیشن، یا مشکلات عملکردی',
                'is_active': True
            },
            {
                'title': 'درخواست تغییر اطلاعات صندوق',
                'description': 'درخواست تغییر قوانین، مبلغ، یا سایر اطلاعات صندوق',
                'is_active': True
            },
            {
                'title': 'شکایت از عضو صندوق',
                'description': 'شکایت از رفتار نامناسب، عدم پرداخت، یا تخلف از قوانین صندوق',
                'is_active': True
            },
            {
                'title': 'درخواست پشتیبانی عمومی',
                'description': 'سوالات عمومی، راهنمایی، یا درخواست کمک در استفاده از سیستم',
                'is_active': True
            },
            {
                'title': 'مشکل در گزارش‌گیری',
                'description': 'مشکلات مربوط به مشاهده گزارش‌ها، آمار، یا تاریخچه تراکنش‌ها',
                'is_active': True
            },
            {
                'title': 'پیشنهاد بهبود',
                'description': 'پیشنهادات برای بهبود عملکرد، اضافه کردن قابلیت جدید، یا تغییر رابط کاربری',
                'is_active': True
            }
        ]

        self.stdout.write(
            self.style.SUCCESS('🚀 شروع ایجاد موضوعات گزارش‌دهی...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('⚠️  حالت Dry Run - هیچ تغییری در دیتابیس اعمال نمی‌شود')
            )

        # حذف موضوعات موجود در صورت درخواست
        if clear_existing and not dry_run:
            existing_count = ReportSubject.objects.count()
            if existing_count > 0:
                confirm = input(f'آیا مطمئن هستید که می‌خواهید {existing_count} موضوع موجود را حذف کنید؟ (y/N): ')
                if confirm.lower() == 'y':
                    ReportSubject.objects.all().delete()
                    self.stdout.write(
                        self.style.WARNING(f'🗑️  {existing_count} موضوع موجود حذف شد')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR('❌ عملیات لغو شد')
                    )
                    return

        # ایجاد موضوعات جدید
        created_count = 0
        skipped_count = 0
        
        with transaction.atomic():
            for subject_data in default_subjects:
                title = subject_data['title']
                
                # بررسی وجود موضوع
                if ReportSubject.objects.filter(title=title).exists():
                    self.stdout.write(f'   ⏭️  موضوع "{title}" قبلاً وجود دارد - رد شد')
                    skipped_count += 1
                    continue
                
                if not dry_run:
                    # ایجاد موضوع جدید
                    ReportSubject.objects.create(**subject_data)
                
                self.stdout.write(f'   ✅ موضوع "{title}" ایجاد شد')
                created_count += 1

        # نمایش خلاصه نتایج
        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS(f'📊 خلاصه عملیات:')
        )
        self.stdout.write(f'   • موضوعات ایجاد شده: {created_count}')
        self.stdout.write(f'   • موضوعات رد شده: {skipped_count}')
        self.stdout.write(f'   • کل موضوعات پردازش شده: {len(default_subjects)}')
        
        if not dry_run:
            total_subjects = ReportSubject.objects.count()
            self.stdout.write(f'   • کل موضوعات در سیستم: {total_subjects}')
            
            self.stdout.write('\n' + self.style.SUCCESS('🎉 عملیات با موفقیت تکمیل شد!'))
        else:
            self.stdout.write('\n' + self.style.WARNING('💡 برای اعمال تغییرات، command را بدون --dry-run اجرا کنید'))

        # نمایش لیست موضوعات ایجاد شده
        if created_count > 0:
            self.stdout.write('\n📋 موضوعات ایجاد شده:')
            for i, subject_data in enumerate(default_subjects, 1):
                if not ReportSubject.objects.filter(title=subject_data['title']).exists() or dry_run:
                    self.stdout.write(f'   {i:2d}. {subject_data["title"]}')
                    self.stdout.write(f'       📝 {subject_data["description"][:80]}...')

# Generated by Django 5.2.1 on 2025-07-15 16:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('issues', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportSubject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
            ],
            options={
                'verbose_name': 'Report Subject',
                'verbose_name_plural': 'Report Subjects',
                'ordering': ['title'],
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('status', models.CharField(choices=[('Open', 'Open'), ('In Progress', 'In Progress'), ('Closed', 'Closed')], default='Open', max_length=20, verbose_name='Status')),
                ('is_viewed', models.BooleanField(default=False, verbose_name='Is Viewed')),
                ('viewed_at', models.DateTimeField(blank=True, null=True, verbose_name='Viewed At')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to=settings.AUTH_USER_MODEL, verbose_name='User')),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='issues.reportsubject', verbose_name='Subject')),
            ],
            options={
                'verbose_name': 'Report',
                'verbose_name_plural': 'Reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='Content')),
                ('message_type', models.CharField(choices=[('text', 'Text'), ('image', 'Image')], default='text', max_length=10, verbose_name='Message Type')),
                ('content_image', models.ImageField(blank=True, null=True, upload_to='report_messages/%Y/%m/', verbose_name='Content Image')),
                ('is_read', models.BooleanField(default=False, verbose_name='Is Read')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='issues.report', verbose_name='Report')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_messages', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Report Message',
                'verbose_name_plural': 'Report Messages',
                'ordering': ['created_at'],
            },
        ),
    ]

from rest_framework import generics, serializers
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from rest_framework.generics import ListAPIView, CreateAPIView, RetrieveAPIView
from django.utils.translation import gettext as _
from apps.issues.models import IssueReport, ReportSubject
from apps.issues.serializers import IssueReportSerializer, ReportSubjectSerializer
from apps.deposit.models import Deposit
from apps.issues.docs import report_subjects_list_swagger, create_report_swagger


class IssueReportCreateAPIView(generics.CreateAPIView):
    queryset = IssueReport.objects.all()
    serializer_class = IssueReportSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        user = self.request.user
        deposit_id = self.request.data.get('deposit')

        try:
            # بررسی وجود deposit با استفاده از آی دی
            deposit = Deposit.objects.get(id=deposit_id)
        except Deposit.DoesNotExist:
            return Response(
                {"error": _("Deposit with the given ID does not exist.")},
                status=status.HTTP_400_BAD_REQUEST
            )

        subject = self.request.data.get('subject', '')
        description = self.request.data.get('description', '')

        serializer.save(user=user, deposit=deposit, subject=subject, description=description)


class ReportSubjectListAPIView(ListAPIView):
    """
    API view to list all active report subjects
    """
    queryset = ReportSubject.objects.filter(is_active=True)
    serializer_class = ReportSubjectSerializer
    permission_classes = [IsAuthenticated]

    @report_subjects_list_swagger
    def get(self, request, *args, **kwargs):
        """Get list of active report subjects"""
        return super().get(request, *args, **kwargs)



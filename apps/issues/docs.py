from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status

# ============================================================================
# STRUCTURED SWAGGER DOCUMENTATION FOR ISSUES APP
# ============================================================================

# Report Subjects List Swagger Documentation
report_subjects_list_swagger = swagger_auto_schema(
    operation_description="Get list of all active report subjects/categories that users can choose from when creating a report.",
    operation_summary="List Report Subjects",
    tags=['Issues'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of report subjects retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Subject unique identifier',
                            example=1
                        ),
                        'title': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Subject title',
                            example='مشکل فنی'
                        ),
                        'description': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Subject description',
                            example='مشکلات مربوط به عملکرد سیستم'
                        )
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "id": 1,
                        "title": "مشکل فنی",
                        "description": "مشکلات مربوط به عملکرد سیستم"
                    },
                    {
                        "id": 2,
                        "title": "پیشنهاد",
                        "description": "پیشنهادات برای بهبود سیستم"
                    }
                ]
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Create Report Swagger Documentation
create_report_swagger = swagger_auto_schema(
    operation_description="Create a new report. The description will become the first message in the report.",
    operation_summary="Create New Report",
    tags=['Issues'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'subject_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='ID of the report subject/category',
                example=1
            ),
            'title': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Report title',
                example='مشکل در ورود به سیستم'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Report description (becomes first message)',
                example='من نمی‌توانم وارد حساب کاربری خود شوم'
            )
        },
        required=['subject_id', 'title', 'description']
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Report created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='success'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Report created successfully.'
                    ),
                    'report': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                            'title': openapi.Schema(type=openapi.TYPE_STRING, example='مشکل در ورود به سیستم'),
                            'status': openapi.Schema(type=openapi.TYPE_STRING, example='Open'),
                            'is_viewed': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
                            'viewed_at': openapi.Schema(type=openapi.TYPE_STRING, example=None),
                            'created_at': openapi.Schema(type=openapi.TYPE_STRING, example='2023-01-01T10:00:00Z'),
                            'updated_at': openapi.Schema(type=openapi.TYPE_STRING, example='2023-01-01T10:00:00Z'),
                            'subject_info': openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                                    'title': openapi.Schema(type=openapi.TYPE_STRING, example='مشکل فنی'),
                                    'description': openapi.Schema(type=openapi.TYPE_STRING, example='مشکلات مربوط به عملکرد سیستم')
                                }
                            ),
                            'user_info': openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=10),
                                    'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='علی احمدی')
                                }
                            ),
                            'messages_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                            'last_message': openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'content': openapi.Schema(type=openapi.TYPE_STRING, example='من نمی‌توانم وارد حساب کاربری خود شوم'),
                                    'created_at': openapi.Schema(type=openapi.TYPE_STRING, example='2023-01-01T10:00:00Z'),
                                    'user': openapi.Schema(type=openapi.TYPE_STRING, example='علی احمدی')
                                }
                            )
                        }
                    )
                }
            )
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation errors",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='error'),
                    'message': openapi.Schema(type=openapi.TYPE_STRING, example='Failed to create report.'),
                    'errors': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'subject_id': openapi.Schema(
                                type=openapi.TYPE_ARRAY,
                                items=openapi.Schema(type=openapi.TYPE_STRING),
                                example=['Invalid or inactive subject.']
                            )
                        }
                    )
                }
            )
        )
    }
)


def doc_report_subjects_list():
    return """
# 📋 Scenario
لیست موضوعات گزارش

دریافت لیست تمام موضوعات فعال گزارش که کاربران می‌توانند هنگام ایجاد گزارش انتخاب کنند.

---

## 🚀 درخواست API

### URL:
```
GET /api/issues/subjects/
```

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Token your_auth_token_here     |

---

## 📄 نمونه پاسخ:

```json
[
    {
        "id": 1,
        "title": "مشکل فنی",
        "description": "مشکلات مربوط به عملکرد سیستم"
    },
    {
        "id": 2,
        "title": "پیشنهاد",
        "description": "پیشنهادات برای بهبود سیستم"
    },
    {
        "id": 3,
        "title": "شکایت",
        "description": "شکایات و نارضایتی‌ها"
    }
]
```

---

## 💡 نکات مهم:
1. **احراز هویت:** کاربر باید توکن احراز هویت معتبر ارائه دهد
2. **فقط موضوعات فعال:** تنها موضوعاتی که is_active=True هستند برگردانده می‌شوند
3. **مرتب‌سازی:** موضوعات بر اساس عنوان مرتب می‌شوند

---

## 🔍 کاربردها:
- نمایش لیست موضوعات در فرم ایجاد گزارش
- انتخاب دسته‌بندی مناسب برای گزارش
"""


def doc_create_report():
    return """
# ➕ Scenario
ایجاد گزارش جدید

کاربر می‌تواند گزارش جدید ایجاد کند. توضیحات ارائه شده به عنوان اولین پیام در گزارش ثبت می‌شود.

---

## 🚀 درخواست API

### URL:
```
POST /api/issues/reports/create/
```

### Header:
| کلید          | مقدار                           |
|---------------|---------------------------------|
| Content-Type  | application/json               |
| Authorization | Token your_auth_token_here     |

### Body Parameters:

| فیلد           | نوع     | الزامی | توضیحات                                    |
|---------------|---------|--------|--------------------------------------------|
| `subject_id`  | integer | ✅     | شناسه موضوع گزارش                         |
| `title`       | string  | ✅     | عنوان گزارش                               |
| `description` | string  | ✅     | توضیحات (می‌شود اولین پیام)               |

---

## 📄 نمونه درخواست:

```json
{
    "subject_id": 1,
    "title": "مشکل در ورود به سیستم",
    "description": "من نمی‌توانم وارد حساب کاربری خود شوم و پیام خطا دریافت می‌کنم"
}
```

---

## 💡 نکات مهم:
1. **موضوع فعال:** subject_id باید مربوط به موضوع فعال باشد
2. **پیام اولیه:** description به عنوان اولین پیام در گزارش ثبت می‌شود
3. **وضعیت اولیه:** گزارش با وضعیت "Open" و is_viewed=False ایجاد می‌شود

---

## 🔍 کاربردها:
- ایجاد گزارش مشکل فنی
- ارسال پیشنهاد
- ثبت شکایت
"""

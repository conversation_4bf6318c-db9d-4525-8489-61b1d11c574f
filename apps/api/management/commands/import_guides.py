import json
import os
from django.core.management.base import BaseCommand
from django.conf import settings
from apps.api.models import Guide, GuideContent


class Command(BaseCommand):
    help = 'Import guides data from JSON file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='data/guides_data.json',
            help='Path to JSON file containing guides data'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in test mode without actually creating records'
        )

    def handle(self, *args, **options):
        file_path = options['file']
        dry_run = options['dry_run']
        
        # Build full path
        full_path = os.path.join(settings.BASE_DIR, file_path)
        
        if not os.path.exists(full_path):
            self.stdout.write(
                self.style.ERROR(f'File not found: {full_path}')
            )
            return
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                guides_data = json.load(f)
        except json.JSONDecodeError as e:
            self.stdout.write(
                self.style.ERROR(f'Invalid JSON file: {e}')
            )
            return
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error reading file: {e}')
            )
            return
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('🧪 DRY RUN MODE - No records will be created')
            )
        
        created_guides = 0
        created_contents = 0
        updated_guides = 0
        
        for guide_data in guides_data:
            page_name = guide_data.get('page_name')
            field_name = guide_data.get('field_name')
            is_active = guide_data.get('is_active', True)
            contents = guide_data.get('contents', [])
            
            if not page_name:
                self.stdout.write(
                    self.style.WARNING(f'Skipping guide without page_name')
                )
                continue
            
            self.stdout.write(f'Processing: {page_name}')
            if field_name:
                self.stdout.write(f'  Field: {field_name}')
            
            if not dry_run:
                # Create or get guide
                guide, created = Guide.objects.get_or_create(
                    page_name=page_name,
                    field_name=field_name,
                    defaults={'is_active': is_active}
                )
                
                if created:
                    created_guides += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'  ✅ Created guide: {guide}')
                    )
                else:
                    # Update is_active status
                    if guide.is_active != is_active:
                        guide.is_active = is_active
                        guide.save()
                        updated_guides += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'  🔄 Updated guide status: {guide}')
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING(f'  ⚠️ Guide already exists: {guide}')
                        )
                
                # Clear existing contents and create new ones
                guide.contents.all().delete()
                
                # Create contents
                for order, content_data in enumerate(contents):
                    title = content_data.get('title')
                    description = content_data.get('description')
                    
                    if not title or not description:
                        self.stdout.write(
                            self.style.WARNING(f'    Skipping content without title or description')
                        )
                        continue
                    
                    content = GuideContent.objects.create(
                        guide=guide,
                        title=title,
                        description=description,
                        order=order
                    )
                    created_contents += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'    ✅ Created content: {content.title}')
                    )
            else:
                # Dry run - just show what would be created
                self.stdout.write(f'  📝 Would create/update guide: {page_name}')
                if field_name:
                    self.stdout.write(f'     Field: {field_name}')
                self.stdout.write(f'     Status: {is_active}')
                
                for order, content_data in enumerate(contents):
                    title = content_data.get('title')
                    if title:
                        self.stdout.write(f'    📝 Would create content: {title}')
        
        # Summary
        self.stdout.write('\n' + '='*50)
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'🧪 DRY RUN COMPLETED')
            )
            self.stdout.write(f'📊 Would process {len(guides_data)} guide entries')
        else:
            self.stdout.write(
                self.style.SUCCESS(f'✅ IMPORT COMPLETED')
            )
            self.stdout.write(f'📊 Created guides: {created_guides}')
            self.stdout.write(f'📊 Updated guides: {updated_guides}')
            self.stdout.write(f'📊 Created contents: {created_contents}')
        self.stdout.write('='*50)

# Generated by Django 3.2.4 on 2025-07-24 14:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Guide',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page_name', models.CharField(help_text='نام صفحه به صورت slug', max_length=100, verbose_name='Page Name')),
                ('field_name', models.CharField(blank=True, help_text='نام فیلد به صورت slug (اختیاری)', max_length=100, null=True, verbose_name='Field Name')),
                ('is_active', models.BooleanField(default=True, help_text='وضعیت فعال/غیرفعال راهنما', verbose_name='Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Guide',
                'verbose_name_plural': 'Guides',
                'ordering': ['page_name', 'field_name'],
                'unique_together': {('page_name', 'field_name')},
            },
        ),
        migrations.CreateModel(
            name='GuideContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='عنوان محتوای راهنما', max_length=200, verbose_name='Title')),
                ('description', models.TextField(help_text='توضیحات کامل راهنما', verbose_name='Description')),
                ('order', models.PositiveIntegerField(default=0, help_text='ترتیب نمایش محتوا', verbose_name='Order')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('guide', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contents', to='api.guide', verbose_name='Guide')),
            ],
            options={
                'verbose_name': 'Guide Content',
                'verbose_name_plural': 'Guide Contents',
                'ordering': ['guide', 'order', 'id'],
            },
        ),
    ]

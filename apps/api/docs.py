from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status


# Guide List API Documentation
guide_list_swagger = swagger_auto_schema(
    operation_description="""دریافت لیست همه راهنماهای فعال سیستم.

این API لیست کاملی از راهنماهای موجود برای صفحات و فیلدهای مختلف سیستم را برمی‌گرداند.
هر راهنما شامل اطلاعات صفحه، فیلد مربوطه و محتوای آموزشی است.

**کاربردها:**
- نمایش راهنما در فرم‌های ایجاد صندوق
- ارائه کمک به کاربران هنگام پر کردن فیلدها
- نمایش توضیحات تکمیلی برای فیلدهای پیچیده

**ویژگی‌ها:**
- بدون نیاز به احراز هویت
- بدون pagination
- شامل محتوای کامل راهنماها
- فقط راهنماهای فعال نمایش داده می‌شوند""",
    operation_summary="دریافت لیست راهنماها",
    tags=['Guides'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="لیست راهنماها با موفقیت دریافت شد",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'page_name': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='نام صفحه (slug format)',
                            example='create-poll-deposit'
                        ),
                        'field_name': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='نام فیلد (slug format) - اختیاری',
                            example='lottery_month_count'
                        ),
                        'is_active': openapi.Schema(
                            type=openapi.TYPE_BOOLEAN,
                            description='وضعیت فعال/غیرفعال راهنما',
                            example=True
                        ),
                        'contents': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            description='لیست محتوای راهنما',
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'title': openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        description='عنوان محتوا',
                                        example='تعداد ماه‌های قرعه‌کشی'
                                    ),
                                    'description': openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        description='توضیحات کامل',
                                        example='این فیلد تعداد ماه‌هایی که صندوق قرعه‌کشی فعال خواهد بود را مشخص می‌کند.'
                                    ),
                                    'order': openapi.Schema(
                                        type=openapi.TYPE_INTEGER,
                                        description='ترتیب نمایش محتوا',
                                        example=0
                                    )
                                }
                            )
                        )
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "page_name": "create-poll-deposit",
                        "field_name": "lottery_month_count",
                        "is_active": True,
                        "contents": [
                            {
                                "title": "تعداد ماه‌های قرعه‌کشی",
                                "description": "این فیلد تعداد ماه‌هایی که صندوق قرعه‌کشی فعال خواهد بود را مشخص می‌کند. برای مثال اگر 12 وارد کنید، صندوق به مدت 12 ماه فعال خواهد بود.",
                                "order": 0
                            },
                            {
                                "title": "نکات مهم",
                                "description": "• حداقل مدت زمان: 6 ماه\n• حداکثر مدت زمان: 60 ماه\n• این مقدار بر محاسبه تعداد کل سهم‌ها تأثیر می‌گذارد",
                                "order": 1
                            }
                        ]
                    },
                    {
                        "page_name": "create-poll-deposit",
                        "field_name": "lottery_per_month_count",
                        "is_active": True,
                        "contents": [
                            {
                                "title": "تعداد قرعه‌کشی در ماه",
                                "description": "این فیلد مشخص می‌کند که در هر ماه چند بار قرعه‌کشی انجام شود.",
                                "order": 0
                            }
                        ]
                    }
                ]
            }
        )
    }
)

from django.shortcuts import render, redirect
from django.views import View
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model

User = get_user_model()

@method_decorator(staff_member_required, name='dispatch')
class CustomSwaggerView(View):
    """
    Custom Swagger UI view with authentication banner
    Requires admin login to access
    """
    def get(self, request):
        # Use token-authenticated endpoint if user has token, otherwise use admin endpoint
        current_token = request.session.get('swagger_token')
        if current_token:
            swagger_spec_url = '/en/api/swagger.json'  # Token-authenticated endpoint
        else:
            swagger_spec_url = '/en/swagger.json'  # Admin-only endpoint

        context = {
            'swagger_spec_url': swagger_spec_url,
            'request': request,
            'user_info': request.session.get('swagger_user_info'),
            'current_token': current_token,
        }
        return render(request, 'swagger/ui.html', context)

@method_decorator(staff_member_required, name='dispatch')
class SwaggerTokenAuthView(View):
    """
    Token authentication management for Swagger
    """
    def get(self, request):
        context = {
            'current_token': request.session.get('swagger_token'),
            'user_info': request.session.get('swagger_user_info'),
        }
        return render(request, 'swagger/auth.html', context)
    
    def post(self, request):
        token = request.POST.get('token', '').strip()
        
        if not token or len(token) != 40:
            messages.error(request, 'Token must be exactly 40 characters long')
            return redirect('swagger-token-auth')
        
        try:
            token_obj = Token.objects.get(key=token)
            user = token_obj.user
            
            if not user.is_active:
                messages.error(request, 'User account is not active')
                return redirect('swagger-token-auth')
            
            request.session['swagger_token'] = token
            request.session['swagger_user_info'] = {
                'id': user.id,
                'email': user.email if hasattr(user, 'email') else None,
                'fullname': getattr(user, 'fullname', str(user)),
                'phone_number': str(getattr(user, 'phone_number', '')),
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
                'user_type': 'Staff' if user.is_staff else 'User'
            }
            
            messages.success(request, f'Successfully authenticated as {getattr(user, "fullname", user.email or str(user))}')
            return redirect('schema-swagger-ui')
            
        except Token.DoesNotExist:
            messages.error(request, 'Invalid token')
            return redirect('swagger-token-auth')

@staff_member_required
def clear_swagger_auth(request):
    """Clear swagger authentication from session"""
    if 'swagger_token' in request.session:
        del request.session['swagger_token']
    if 'swagger_user_info' in request.session:
        del request.session['swagger_user_info']
    
    messages.success(request, 'Successfully logged out from Swagger')
    return redirect('swagger-token-auth')

@staff_member_required
def generate_user_token(request):
    """Generate or get existing token for current user"""
    if request.method == 'POST':
        try:
            # Delete existing token if any
            Token.objects.filter(user=request.user).delete()
            
            # Create new token
            token = Token.objects.create(user=request.user)
            
            messages.success(request, f'New token generated: {token.key}')
            return redirect('swagger-token-auth')
            
        except Exception as e:
            messages.error(request, f'Error generating token: {str(e)}')
            return redirect('swagger-token-auth')
    
    # GET request - show current token if exists
    try:
        token = Token.objects.get(user=request.user)
        messages.info(request, f'Your current token: {token.key}')
    except Token.DoesNotExist:
        messages.info(request, 'You do not have an active token. Generate one below.')
    
    return redirect('swagger-token-auth')

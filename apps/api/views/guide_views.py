from rest_framework.generics import ListAPIView
from rest_framework.permissions import AllowAny
from drf_yasg.utils import swagger_auto_schema
from ..models import Guide
from ..serializers import GuideSerializer
from ..docs import guide_list_swagger


class GuideListAPIView(ListAPIView):
    """
    لیست همه راهنماهای فعال
    """
    queryset = Guide.objects.filter(is_active=True).prefetch_related('contents')
    serializer_class = GuideSerializer
    permission_classes = [AllowAny]
    pagination_class = None  # بدون pagination

    @guide_list_swagger
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

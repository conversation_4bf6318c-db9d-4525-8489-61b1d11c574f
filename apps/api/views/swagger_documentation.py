import json
import requests
from django.shortcuts import render
from django.template.response import TemplateResponse
from django.views import View
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.conf import settings
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions


@method_decorator(staff_member_required, name='dispatch')
class SwaggerBasedDocumentationView(View):
    """
    API Documentation view that uses Swagger JSON as source
    """
    
    def get(self, request):
        try:
            # Get Swagger JSON (tries real endpoint first, then fallback)
            swagger_data = self.get_swagger_json(request)

            # Parse and organize the data
            api_structure = self.parse_swagger_data(swagger_data)

            context = {
                'api_structure': api_structure,
                'request': request,
                'title': 'Qatreh API Documentation',
                'description': 'Comprehensive API documentation for Qatreh financial platform with interactive examples',
                'data_source': 'swagger' if 'info' in swagger_data and swagger_data.get('paths') else 'generated'
            }
            return TemplateResponse(request, 'api/documentation.html', context)

        except Exception as e:
            print(f"❌ Documentation generation failed: {e}")
            # Return error page instead of fallback
            context = {
                'error': str(e),
                'title': 'API Documentation Error',
                'description': 'There was an error generating the API documentation.'
            }
            return render(request, 'api/documentation_error.html', context)
    
    def get_swagger_json(self, request):
        """Get Swagger JSON from the schema endpoint"""
        try:
            # First try to get from actual Swagger endpoint
            base_url = request.build_absolute_uri('/')[:-1]  # Remove trailing slash
            swagger_url = f"{base_url}/en/swagger.json"

            # Make request with session cookies for authentication
            response = requests.get(swagger_url, cookies=request.COOKIES, timeout=10)

            if response.status_code == 200:
                swagger_data = response.json()
                print(f"✅ Successfully fetched Swagger JSON with {len(swagger_data.get('paths', {}))} paths")
                return swagger_data
            else:
                print(f"❌ Swagger endpoint returned {response.status_code}, falling back to direct generation")
                return self.generate_schema_directly()

        except Exception as e:
            print(f"❌ Error fetching Swagger JSON: {e}, falling back to direct generation")
            return self.generate_schema_directly()
    
    def generate_schema_directly(self):
        """Generate schema directly from URL patterns"""
        try:
            from django.urls import get_resolver
            from django.urls.resolvers import URLPattern, URLResolver

            # Get URL patterns
            resolver = get_resolver()
            paths = {}

            # Extract API endpoints
            self._extract_api_paths(resolver, '', paths)

            return {
                "paths": paths,
                "info": {"title": "API Documentation", "version": "1.0.0"}
            }

        except Exception as e:
            print(f"Error generating schema from URLs: {e}")
            return {"paths": {}, "info": {"title": "API Documentation"}}

    def _extract_api_paths(self, resolver, prefix, paths):
        """Extract API paths from URL resolver"""
        from django.urls.resolvers import URLPattern, URLResolver

        for pattern in resolver.url_patterns:
            if isinstance(pattern, URLPattern):
                # This is a direct URL pattern
                path = prefix + str(pattern.pattern).replace('^', '').replace('$', '')

                # Only include API paths
                if '/api/' in path or path.startswith('api/'):
                    view = pattern.callback

                    # Get view class and methods
                    if hasattr(view, 'view_class'):
                        view_class = view.view_class
                        methods = self._get_view_methods(view_class)

                        path_info = {}
                        for method in methods:
                            # Get better parameters and responses based on view class and method
                            parameters = self._get_parameters_for_view(view_class, method, path)
                            responses = self._get_responses_for_view(view_class, method, path)

                            path_info[method.lower()] = {
                                'summary': self._get_summary_for_view(view_class, method, path),
                                'description': self._get_description_for_view(view_class, method, path),
                                'tags': [self._extract_app_from_path(path)],
                                'parameters': parameters,
                                'requestBody': self._get_request_body_for_view(view_class, method, path),
                                'responses': responses
                            }

                        if path_info:
                            paths[path] = path_info

            elif isinstance(pattern, URLResolver):
                # This is a nested URL resolver
                nested_prefix = prefix + str(pattern.pattern).replace('^', '').replace('$', '')
                self._extract_api_paths(pattern, nested_prefix, paths)

    def _get_view_methods(self, view_class):
        """Get HTTP methods supported by a view class"""
        methods = []

        # Check for DRF view methods
        if hasattr(view_class, 'http_method_names'):
            allowed_methods = view_class.http_method_names
            for method in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                method_lower = method.lower()
                if method_lower in allowed_methods:
                    # Check if method is actually implemented
                    if hasattr(view_class, method_lower):
                        methods.append(method)

        # Enhanced fallback based on class name and common patterns
        if not methods:
            class_name = view_class.__name__.lower()

            # Specific patterns for common views
            if 'register' in class_name or 'create' in class_name:
                methods.append('POST')
            elif 'login' in class_name or 'verify' in class_name:
                methods.append('POST')
            elif 'list' in class_name:
                methods.append('GET')
            elif 'detail' in class_name:
                methods.append('GET')
            elif 'update' in class_name:
                methods.extend(['PUT', 'PATCH'])
            elif 'delete' in class_name:
                methods.append('DELETE')
            else:
                # Check for common method patterns in class
                if hasattr(view_class, 'post'):
                    methods.append('POST')
                if hasattr(view_class, 'get'):
                    methods.append('GET')
                if hasattr(view_class, 'put'):
                    methods.append('PUT')
                if hasattr(view_class, 'delete'):
                    methods.append('DELETE')
                if hasattr(view_class, 'patch'):
                    methods.append('PATCH')

            # Default to GET if still nothing found
            if not methods:
                methods.append('GET')

        return methods

    def _get_summary_for_view(self, view_class, method, path):
        """Get better summary for view"""
        class_name = view_class.__name__

        # Create meaningful summaries based on class name and method
        if 'List' in class_name and method == 'GET':
            return f"List {self._extract_resource_name(path)}"
        elif 'Create' in class_name and method == 'POST':
            return f"Create {self._extract_resource_name(path)}"
        elif 'Detail' in class_name and method == 'GET':
            return f"Get {self._extract_resource_name(path)} details"
        elif 'Update' in class_name and method in ['PUT', 'PATCH']:
            return f"Update {self._extract_resource_name(path)}"
        elif 'Delete' in class_name and method == 'DELETE':
            return f"Delete {self._extract_resource_name(path)}"
        elif method == 'POST':
            return f"Create or process {self._extract_resource_name(path)}"
        else:
            return f"{method} {self._extract_resource_name(path)}"

    def _get_description_for_view(self, view_class, method, path):
        """Get description from view docstring or swagger decorator - NO HARDCODED DESCRIPTIONS"""
        # Try to get description from view's docstring
        if hasattr(view_class, '__doc__') and view_class.__doc__:
            return view_class.__doc__.strip()

        # Try to get description from method docstring
        method_func = getattr(view_class, method.lower(), None)
        if method_func and hasattr(method_func, '__doc__') and method_func.__doc__:
            return method_func.__doc__.strip()

        # Fallback to generic description based on method and resource
        resource = self._extract_resource_name(path)
        if method == 'GET' and 'List' in view_class.__name__:
            return f"Retrieve a list of {resource} items"
        elif method == 'POST':
            return f"Create or process {resource}"
        elif method == 'GET':
            return f"Retrieve {resource} information"
        elif method in ['PUT', 'PATCH']:
            return f"Update {resource} information"
        elif method == 'DELETE':
            return f"Delete {resource} item"
        else:
            return f"{method} operation for {resource}"

    def _extract_resource_name(self, path):
        """Extract resource name from path"""
        path_parts = [p for p in path.strip('/').split('/') if p and not p.startswith('<')]
        if len(path_parts) >= 2:
            return path_parts[-1] if path_parts[-1] != 'api' else path_parts[-2]
        elif path_parts:
            return path_parts[0]
        return 'resource'

    def _get_parameters_for_view(self, view_class, method, path):
        """Get parameters for view based on path and method"""
        parameters = []

        # Add path parameters
        import re
        path_params = re.findall(r'<(\w+):(\w+)>', path)
        for param_type, param_name in path_params:
            parameters.append({
                'name': param_name,
                'in': 'path',
                'required': True,
                'schema': {'type': 'integer' if param_type == 'int' else 'string'},
                'description': f'{param_name.replace("_", " ").title()} identifier'
            })

        # Add common query parameters for list views
        if 'List' in view_class.__name__ and method == 'GET':
            parameters.extend([
                {
                    'name': 'page',
                    'in': 'query',
                    'required': False,
                    'schema': {'type': 'integer'},
                    'description': 'Page number for pagination'
                },
                {
                    'name': 'search',
                    'in': 'query',
                    'required': False,
                    'schema': {'type': 'string'},
                    'description': 'Search query'
                }
            ])

        # Add Authorization header for authenticated endpoints
        if not ('login' in path.lower() or 'register' in path.lower()):
            parameters.append({
                'name': 'Authorization',
                'in': 'header',
                'required': True,
                'schema': {'type': 'string'},
                'description': 'Token {auth_token}'
            })

        return parameters

    def _get_request_body_for_view(self, view_class, method, path):
        """Get request body for view"""
        if method not in ['POST', 'PUT', 'PATCH']:
            return None

        # Generate request body based on path and view
        schema_properties = {}
        required_fields = []

        if 'register' in path.lower():
            schema_properties = {
                'phone_number': {'type': 'string', 'description': 'User phone number'},
                'fullname': {'type': 'string', 'description': 'User full name'},
                'name': {'type': 'string', 'description': 'User first name'},
                'family': {'type': 'string', 'description': 'User last name'},
                'password': {'type': 'string', 'description': 'User password (minimum 8 characters)'},
                'fcm': {'type': 'string', 'description': 'FCM token (optional)'},
                'device_id': {'type': 'string', 'description': 'Device ID (optional)'}
            }
            required_fields = ['phone_number', 'fullname', 'password']
        elif 'login' in path.lower():
            schema_properties = {
                'phone_number': {'type': 'string', 'description': 'User phone number'},
                'password': {'type': 'string', 'description': 'User password'}
            }
            required_fields = ['phone_number', 'password']
        elif 'verify' in path.lower():
            schema_properties = {
                'phone_number': {'type': 'string', 'description': 'User phone number'},
                'code': {'type': 'string', 'description': 'Verification code'}
            }
            required_fields = ['phone_number', 'code']
        elif 'deposit' in path.lower() and method == 'POST':
            if 'cancel' in path.lower():
                schema_properties = {
                    'deposit_id': {'type': 'integer', 'description': 'ID of the deposit to cancel'}
                }
                required_fields = ['deposit_id']
            else:
                schema_properties = {
                    'title': {'type': 'string', 'description': 'Deposit title'},
                    'description': {'type': 'string', 'description': 'Deposit description'},
                    'unit_amount': {'type': 'integer', 'description': 'Unit amount per share'}
                }
                required_fields = ['title', 'unit_amount']
        elif 'region' in path.lower() and 'change' in path.lower():
            schema_properties = {
                'region_id': {'type': 'integer', 'description': 'ID of the region to join'}
            }
            required_fields = ['region_id']
        elif 'invitaion' in path.lower() or 'invitation' in path.lower():
            schema_properties = {
                'invitation_code': {'type': 'string', 'description': 'Unique invitation code'}
            }
            required_fields = ['invitation_code']
        elif 'payment' in path.lower() and method == 'POST':
            if 'create' in path.lower():
                schema_properties = {
                    'amount': {'type': 'integer', 'description': 'Payment amount'},
                    'deposit_id': {'type': 'integer', 'description': 'Deposit ID'},
                    'description': {'type': 'string', 'description': 'Payment description'}
                }
                required_fields = ['amount', 'deposit_id']
            else:
                schema_properties = {
                    'Authority': {'type': 'string', 'description': 'Payment authority code'},
                    'Status': {'type': 'string', 'description': 'Payment status'}
                }
                required_fields = ['Authority', 'Status']
        elif 'ticket' in path.lower() and method == 'POST':
            if 'message' in path.lower():
                schema_properties = {
                    'message': {'type': 'string', 'description': 'Message content'}
                }
                required_fields = ['message']
            else:
                schema_properties = {
                    'subject': {'type': 'string', 'description': 'Ticket subject'},
                    'message': {'type': 'string', 'description': 'Ticket message'},
                    'priority': {'type': 'string', 'description': 'Ticket priority'}
                }
                required_fields = ['subject', 'message']
        elif 'voting' in path.lower() and method == 'POST':
            if 'join' in path.lower():
                schema_properties = {
                    'voting_id': {'type': 'integer', 'description': 'Voting poll ID'},
                    'choice': {'type': 'string', 'description': 'Vote choice'}
                }
                required_fields = ['voting_id', 'choice']
            else:
                schema_properties = {
                    'title': {'type': 'string', 'description': 'Voting title'},
                    'description': {'type': 'string', 'description': 'Voting description'},
                    'deposit_id': {'type': 'integer', 'description': 'Deposit ID'}
                }
                required_fields = ['title', 'deposit_id']
        else:
            # Try to infer from path
            if 'join' in path.lower():
                schema_properties = {
                    'deposit_id': {'type': 'integer', 'description': 'Deposit ID to join'}
                }
                required_fields = ['deposit_id']
            elif 'update' in path.lower():
                schema_properties = {
                    'status': {'type': 'string', 'description': 'New status'}
                }
                required_fields = ['status']
            else:
                # Generic request body
                resource = self._extract_resource_name(path)
                schema_properties = {
                    'data': {'type': 'object', 'description': f'{resource} data'}
                }
                required_fields = ['data']

        if schema_properties:
            return {
                'required': True,
                'content': {
                    'application/json': {
                        'schema': {
                            'type': 'object',
                            'properties': schema_properties,
                            'required': required_fields
                        }
                    }
                }
            }

        return None

    def _get_responses_for_view(self, view_class, method, path):
        """Get better responses for view"""
        responses = {}

        # Success response
        if method == 'GET' and 'List' in view_class.__name__:
            # List response
            responses['200'] = {
                'description': 'Success',
                'content': {
                    'application/json': {
                        'example': {
                            'count': 10,
                            'results': [
                                {'id': 1, 'name': 'Example item'}
                            ]
                        }
                    }
                }
            }
        elif 'login' in path.lower():
            responses['200'] = {
                'description': 'Login successful',
                'content': {
                    'application/json': {
                        'example': {
                            'token': 'auth_token_here',
                            'user': {
                                'id': 123,
                                'fullname': 'John Doe',
                                'phone_number': '+989123456789'
                            }
                        }
                    }
                }
            }
        elif 'register' in path.lower():
            responses['201'] = {
                'description': 'Registration successful',
                'content': {
                    'application/json': {
                        'example': {
                            'message': 'User registered successfully',
                            'user_id': 123
                        }
                    }
                }
            }
        elif 'invitaion' in path.lower() or 'invitation' in path.lower():
            responses['200'] = {
                'description': 'User added to region successfully',
                'content': {
                    'application/json': {
                        'example': {
                            'message': 'User added to the region successfully.',
                            'is_active': True,
                            'is_member': True
                        }
                    }
                }
            }
        elif 'verify' in path.lower():
            responses['200'] = {
                'description': 'Verification successful',
                'content': {
                    'application/json': {
                        'example': {
                            'token': 'auth_token_here',
                            'user': {
                                'id': 123,
                                'fullname': 'John Doe',
                                'phone_number': '+989123456789',
                                'is_active': True
                            }
                        }
                    }
                }
            }
        elif method == 'POST':
            responses['201'] = {
                'description': 'Created successfully',
                'content': {
                    'application/json': {
                        'example': {
                            'status': 'success',
                            'message': 'Created successfully',
                            'id': 123
                        }
                    }
                }
            }
        else:
            responses['200'] = {
                'description': 'Success',
                'content': {
                    'application/json': {
                        'example': {
                            'status': 'success',
                            'message': 'Operation completed successfully'
                        }
                    }
                }
            }

        # Common error responses
        if 'invitaion' in path.lower() or 'invitation' in path.lower():
            responses['400'] = {
                'description': 'Bad Request',
                'content': {
                    'application/json': {
                        'example': {
                            'status': 'error',
                            'code': 'validation_error',
                            'status_code': 400,
                            'message': 'Invalid invitation code.'
                        }
                    }
                }
            }
        else:
            responses['400'] = {
                'description': 'Bad Request',
                'content': {
                    'application/json': {
                        'example': {
                            'status': 'error',
                            'message': 'Validation failed',
                            'errors': {}
                        }
                    }
                }
            }

        if not ('login' in path.lower() or 'register' in path.lower()):
            responses['401'] = {
                'description': 'Unauthorized',
                'content': {
                    'application/json': {
                        'example': {
                            'detail': 'Authentication credentials were not provided.'
                        }
                    }
                }
            }

        return responses

    def _extract_app_from_path(self, path):
        """Extract app name from path"""
        path_parts = path.strip('/').split('/')

        # Handle specific lottery patterns first
        if ('lottery' in path or 'winners' in path) and 'deposit' in path:
            return 'lottery'

        # Handle API paths
        if len(path_parts) >= 2 and path_parts[0] == 'api':
            base_app = path_parts[1]

            # Special handling for lottery endpoints under deposit
            if base_app == 'deposit' and ('lottery' in path or 'winners' in path):
                return 'lottery'

            return base_app

        # Handle specific patterns for apps with empty prefix
        elif path.startswith('payments/') or 'payment' in path:
            return 'payment'
        elif len(path_parts) >= 1 and path_parts[0]:
            return path_parts[0]

        return 'general'
    
    def parse_swagger_data(self, swagger_data):
        """Parse Swagger JSON into our template structure"""
        api_structure = {}
        
        paths = swagger_data.get('paths', {})
        
        # Group endpoints by app/tag
        for path, methods in paths.items():
            # Filter methods: if both PUT and PATCH exist, only use PATCH
            filtered_methods = {}
            for method, details in methods.items():
                if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                    filtered_methods[method] = details

            # If both PUT and PATCH exist, remove PUT
            if 'put' in filtered_methods and 'patch' in filtered_methods:
                del filtered_methods['put']

            # Process filtered methods
            for method, details in filtered_methods.items():
                # Extract app name from path or tags
                app_name = self.extract_app_name(path, details)

                if app_name not in api_structure:
                    api_structure[app_name] = {
                        'name': self.format_app_name(app_name),
                        'description': self.get_app_description(app_name),
                        'endpoints': []
                    }

                # Create endpoint entry
                endpoint = {
                    'name': details.get('summary', f"{method.upper()} {path}"),
                    'method': method.upper(),
                    'url': path,
                    'description': details.get('description', ''),
                    'parameters': self.extract_parameters(details),
                    'response_examples': self.extract_response_examples(details),
                    'response_schemas': self.extract_response_schemas(details)
                }

                api_structure[app_name]['endpoints'].append(endpoint)
        
        return api_structure
    
    def extract_app_name(self, path, details):
        """Extract app name from path or tags"""
        # Try to get from tags first
        tags = details.get('tags', [])
        if tags:
            return tags[0].lower()
        
        # Extract from path
        path_parts = path.strip('/').split('/')
        if len(path_parts) >= 2 and path_parts[0] == 'api':
            return path_parts[1]
        elif len(path_parts) >= 1:
            return path_parts[0]
        
        return 'general'
    
    def format_app_name(self, app_name):
        """Format app name for display"""
        name_mapping = {
            'account': 'Account Management',
            'deposit': 'Deposit Management', 
            'region': 'Region Management',
            'request': 'Request Management',
            'transaction': 'Transaction Management',
            'payment': 'Payment Gateway',
            'lottery': 'Lottery System',
            'voting': 'Voting System',
            'issues': 'Issue Tracking',
            'ticket': 'Support Tickets',
            'preferences': 'App Preferences'
        }
        return name_mapping.get(app_name, app_name.title())
    
    def get_app_description(self, app_name):
        """Get description for app"""
        descriptions = {
            'account': 'User registration, authentication, and profile management',
            'deposit': 'Financial deposit operations and member management',
            'region': 'APIs for managing user regions and regional memberships',
            'request': 'Deposit creation requests, join requests, and loan management',
            'transaction': 'Financial transaction tracking and management',
            'payment': 'Payment processing and verification',
            'lottery': 'Lottery and random selection management',
            'voting': 'Community voting and decision making',
            'issues': 'Bug reports and issue management',
            'ticket': 'Customer support and ticket management',
            'preferences': 'Application configuration and preference settings'
        }
        return descriptions.get(app_name, f'{app_name.title()} related operations')
    
    def extract_parameters(self, details):
        """Extract parameters from Swagger endpoint details"""
        parameters = []

        # Get parameters from Swagger
        swagger_params = details.get('parameters', [])
        for param in swagger_params:
            param_in = param.get('in', 'query')
            param_name = param.get('name', '')

            # Handle body parameters (old Swagger 2.0 style)
            if param_in == 'body':
                schema = param.get('schema', {})
                if schema.get('type') == 'object':
                    properties = schema.get('properties', {})
                    required_fields = schema.get('required', [])

                    for field_name, field_info in properties.items():
                        parameters.append({
                            'name': field_name,
                            'type': 'body',
                            'data_type': field_info.get('type', 'string'),
                            'description': field_info.get('description', ''),
                            'required': field_name in required_fields,
                            'example': field_info.get('example', '')
                        })
                else:
                    # Single body parameter
                    parameters.append({
                        'name': param_name,
                        'type': 'body',
                        'data_type': schema.get('type', 'string'),
                        'description': param.get('description', ''),
                        'required': param.get('required', False),
                        'example': schema.get('example', '')
                    })
            else:
                # Regular parameters (query, path, header)
                param_info = {
                    'name': param_name,
                    'type': param_in,
                    'description': param.get('description', ''),
                    'required': param.get('required', False)
                }

                # Get type from schema if available
                schema = param.get('schema', {})
                if schema:
                    param_info['data_type'] = schema.get('type', 'string')
                    param_info['example'] = schema.get('example', '')
                else:
                    # Fallback for older Swagger format (Swagger 2.0)
                    param_info['data_type'] = param.get('type', 'string')
                    param_info['example'] = param.get('example', '')

                parameters.append(param_info)

        # Check for request body (OpenAPI 3.0 style)
        request_body = details.get('requestBody', {})
        if request_body:
            content = request_body.get('content', {})
            if 'application/json' in content:
                schema = content['application/json'].get('schema', {})
                properties = schema.get('properties', {})
                required_fields = schema.get('required', [])

                for field_name, field_info in properties.items():
                    parameters.append({
                        'name': field_name,
                        'type': 'body',
                        'data_type': field_info.get('type', 'string'),
                        'description': field_info.get('description', ''),
                        'required': field_name in required_fields,
                        'example': field_info.get('example', '')
                    })

        return parameters
    
    def extract_response_examples(self, details):
        """Extract response examples from Swagger endpoint details"""
        response_examples = {}

        responses = details.get('responses', {})
        for status_code, response_info in responses.items():
            # Handle OpenAPI 3.0 style responses
            content = response_info.get('content', {})
            if 'application/json' in content:
                example = content['application/json'].get('example')
                if example:
                    key = 'success' if status_code.startswith('2') else f'error_{status_code}'
                    response_examples[key] = json.dumps(example, indent=2)

            # Handle Swagger 2.0 style responses
            elif 'examples' in response_info:
                examples = response_info.get('examples', {})
                if 'application/json' in examples:
                    example = examples['application/json']
                    key = 'success' if status_code.startswith('2') else f'error_{status_code}'
                    response_examples[key] = json.dumps(example, indent=2)

            # Handle schema-based responses (generate example from schema)
            elif 'schema' in response_info:
                schema = response_info.get('schema', {})
                example = self._generate_example_from_schema(schema)
                if example:
                    key = 'success' if status_code.startswith('2') else f'error_{status_code}'
                    response_examples[key] = json.dumps(example, indent=2)

        # If no examples found, create basic ones
        if not response_examples:
            response_examples['success'] = json.dumps({"message": "Success"}, indent=2)

        return response_examples

    def _generate_example_from_schema(self, schema):
        """Generate example data from schema definition"""
        if not schema:
            return None

        schema_type = schema.get('type')

        if schema_type == 'object':
            properties = schema.get('properties', {})
            example = {}
            for prop_name, prop_schema in properties.items():
                if 'example' in prop_schema:
                    example[prop_name] = prop_schema['example']
                elif prop_schema.get('type') == 'string':
                    example[prop_name] = f"example_{prop_name}"
                elif prop_schema.get('type') == 'integer':
                    example[prop_name] = 123
                elif prop_schema.get('type') == 'number':
                    example[prop_name] = 123.45
                elif prop_schema.get('type') == 'boolean':
                    example[prop_name] = True
                elif prop_schema.get('type') == 'array':
                    example[prop_name] = []
            return example
        elif schema_type == 'array':
            items = schema.get('items', {})
            item_example = self._generate_example_from_schema(items)
            return [item_example] if item_example else []
        elif 'example' in schema:
            return schema['example']

        return None

    def extract_response_schemas(self, details):
        """Extract response schemas from Swagger endpoint details"""
        response_schemas = {}

        responses = details.get('responses', {})
        for status_code, response_info in responses.items():
            schema_info = {
                'status_code': status_code,
                'description': response_info.get('description', ''),
                'schema': None,
                'properties': []
            }

            # Handle OpenAPI 3.0 style responses
            content = response_info.get('content', {})
            if 'application/json' in content:
                schema = content['application/json'].get('schema', {})
                schema_info['schema'] = schema
                schema_info['properties'] = self._extract_schema_properties(schema)

            # Handle Swagger 2.0 style responses
            elif 'schema' in response_info:
                schema = response_info.get('schema', {})
                schema_info['schema'] = schema
                schema_info['properties'] = self._extract_schema_properties(schema)

            key = 'success' if status_code.startswith('2') else f'error_{status_code}'
            response_schemas[key] = schema_info

        return response_schemas

    def _extract_schema_properties(self, schema):
        """Extract properties from schema for documentation display"""
        if not schema or schema.get('type') != 'object':
            return []

        properties = schema.get('properties', {})
        required_fields = schema.get('required', [])
        property_list = []

        for prop_name, prop_schema in properties.items():
            property_info = {
                'name': prop_name,
                'type': prop_schema.get('type', 'string'),
                'description': prop_schema.get('description', ''),
                'required': prop_name in required_fields,
                'example': prop_schema.get('example', '')
            }

            # Handle array types
            if prop_schema.get('type') == 'array':
                items = prop_schema.get('items', {})
                property_info['items_type'] = items.get('type', 'object')
                if items.get('type') == 'object':
                    property_info['items_properties'] = self._extract_schema_properties(items)

            # Handle object types with nested properties
            elif prop_schema.get('type') == 'object' and 'properties' in prop_schema:
                property_info['nested_properties'] = self._extract_schema_properties(prop_schema)

            property_list.append(property_info)

        return property_list

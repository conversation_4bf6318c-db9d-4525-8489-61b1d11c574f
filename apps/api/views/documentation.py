import json
from django.shortcuts import render, redirect
from django.views import View
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator


@method_decorator(staff_member_required, name='dispatch')
class CustomAPIDocumentationView(View):
    """
    DEPRECATED: Custom API Documentation view with manual structure
    
    This view is kept for backward compatibility only.
    The main documentation now uses SwaggerBasedDocumentationView which
    automatically discovers endpoints from URL patterns.
    
    Access the new documentation at /docs/
    """
    
    def get(self, request):
        # Redirect to the new Swagger-based documentation
        return redirect('docs-index')
    
    def _get_api_structure(self):
        """
        DEPRECATED: Manual API structure definition
        
        This method is kept for backward compatibility only.
        Use SwaggerBasedDocumentationView instead which automatically
        discovers all endpoints from URL patterns.
        """
        # Return minimal structure for any code that might still reference this
        return {
            'deprecated': {
                'name': 'Deprecated Manual Documentation',
                'description': 'This documentation method is deprecated. Use SwaggerBasedDocumentationView instead.',
                'endpoints': [
                    {
                        'name': 'Deprecated Notice',
                        'method': 'GET',
                        'url': '/docs/',
                        'description': 'Please use the new automatic documentation at /docs/',
                        'parameters': [],
                        'response_examples': {
                            'success': json.dumps({
                                "message": "Please use the new automatic documentation at /docs/"
                            }, indent=2)
                        }
                    }
                ]
            }
        }

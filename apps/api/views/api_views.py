import random
from django.views.generic import TemplateView
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework import serializers

from rest_framework.authtoken.models import Token
from apps.account.models import User

class HomeSerializer(serializers.Serializer):
    token = serializers.CharField()


# test class generate token 
class HomeView(GenericAPIView):
    serializer_class = HomeSerializer

    def get(self, request):
        # emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        # phone_numbers = ["***********", "***********", "***********"]
        # fullnames = ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"]
        # # انتخاب رندوم از هر لیست
        # email = random.choice(emails)
        # phone_number = random.choice(phone_numbers)
        # fullname = random.choice(fullnames)
        # # ساخت کاربر جدید
        # user = User.objects.create(
        #     email=email,
        #     phone_number=phone_number,
        #     fullname=fullname,
        # )
        # # ایجاد توکن برای کاربر
        # token, created = Token.objects.get_or_create(user=user)
        
        return Response({'token': "token.key"})


class TranslateTemplateView(TemplateView):
    """
    Template view for Google Translate-like interface
    """
    template_name = 'api/translate.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add supported languages
        context['languages'] = {
            'en': 'English',
            'fa': 'فارسی',
            'ar': 'العربية',
            'fr': 'Français',
            'de': 'Deutsch',
            'es': 'Español',
            'it': 'Italiano',
            'ru': 'Русский',
            'zh': '中文',
            'ja': '日本語',
            'ko': '한국어',
            'tr': 'Türkçe',
        }
        return context


# حذف شد - دیگه نیازی به backend API نیست چون JavaScript مستقیم به one-api.ir درخواست میزنه

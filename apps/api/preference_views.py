from rest_framework.decorators import api_view
from rest_framework.response import Response
from dynamic_preferences.registries import global_preferences_registry


@api_view(http_method_names=['GET'])
def about_us(request):
    global_preferences = global_preferences_registry.manager()
    try:
        about_us_data = global_preferences['aboutus__aboutus']
    except Exception:
        about_us_data = ''

    return Response({
        'about_us': about_us_data
    })


@api_view(http_method_names=['GET'])
def report_config(request):
    global_preferences = global_preferences_registry.manager()
    try:
        report_config_data = global_preferences['contact_report__ReportConfig']
    except Exception:
        report_config_data = {}

    return Response({
        'report_config': report_config_data
    })


@api_view(http_method_names=['GET'])
def policy(request):
    global_preferences = global_preferences_registry.manager()
    try:
        policy_data = global_preferences['Policy__Policy']
    except Exception:
        policy_data = {}

    return Response({
        'policy': policy_data
    })


@api_view(http_method_names=['GET'])
def deposit_info(request):
    """Combined endpoint that returns all deposit information types."""
    global_preferences = global_preferences_registry.manager()

    # Get poll deposit info
    try:
        poll_deposit_data = global_preferences['poll_deposit_info__poll_deposit_info']
    except Exception:
        poll_deposit_data = {}

    # Get saving deposit info
    try:
        saving_deposit_data = global_preferences['saving_deposit_info__saving_deposit_info']
    except Exception:
        saving_deposit_data = {}

    # Get reporting deposit info
    try:
        reporting_deposit_data = global_preferences['reporting_deposit_info__saving_deposit_reporting_deposit_infoinfo']
    except Exception:
        reporting_deposit_data = {}

    # Return all deposit info in a single response
    return Response({
        'poll_deposit_info': poll_deposit_data,
        'saving_deposit_info': saving_deposit_data,
        'reporting_deposit_info': reporting_deposit_data
    })


@api_view(http_method_names=['GET'])
def poll_deposit_info(request):
    global_preferences = global_preferences_registry.manager()
    try:
        poll_deposit_data = global_preferences['poll_deposit_info__poll_deposit_info']
    except Exception:
        poll_deposit_data = {}

    return Response({
        'poll_deposit_info': poll_deposit_data
    })


@api_view(http_method_names=['GET'])
def saving_deposit_info(request):
    global_preferences = global_preferences_registry.manager()
    try:
        saving_deposit_data = global_preferences['saving_deposit_info__saving_deposit_info']
    except Exception:
        saving_deposit_data = {}

    return Response({
        'saving_deposit_info': saving_deposit_data
    })


@api_view(http_method_names=['GET'])
def reporting_deposit_info(request):
    global_preferences = global_preferences_registry.manager()
    try:
        reporting_deposit_data = global_preferences['reporting_deposit_info__saving_deposit_reporting_deposit_infoinfo']
    except Exception:
        reporting_deposit_data = {}

    return Response({
        'reporting_deposit_info': reporting_deposit_data
    })


@api_view(http_method_names=['GET'])
def rules(request):
    global_preferences = global_preferences_registry.manager()
    try:
        rules_data = global_preferences['Rules__Rules']
    except Exception:
        rules_data = {}

    return Response({
        'rules': rules_data
    })


@api_view(http_method_names=['GET'])
def faq(request):
    global_preferences = global_preferences_registry.manager()
    try:
        faq_data = global_preferences['FAQ__FAQ']
    except Exception:
        faq_data = {}

    return Response({
        'faq': faq_data
    })
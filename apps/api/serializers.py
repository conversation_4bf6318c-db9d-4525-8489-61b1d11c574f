from rest_framework import serializers
from .models import Guide, GuideContent


class GuideContentSerializer(serializers.ModelSerializer):
    """
    سریالایزر محتوای راهنما
    """
    class Meta:
        model = GuideContent
        fields = ['title', 'description', 'order']


class GuideSerializer(serializers.ModelSerializer):
    """
    سریالایزر راهنما
    """
    contents = GuideContentSerializer(many=True, read_only=True)
    
    class Meta:
        model = Guide
        fields = ['page_name', 'field_name', 'is_active', 'contents']

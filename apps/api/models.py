from django.db import models
from django.utils.translation import gettext_lazy as _


class Guide(models.Model):
    """
    مدل راهنما برای صفحات و فیلدهای مختلف سیستم
    """
    page_name = models.CharField(
        max_length=100,
        verbose_name=_("Page Name"),
        help_text=_("نام صفحه به صورت slug")
    )
    field_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("Field Name"),
        help_text=_("نام فیلد به صورت slug (اختیاری)")
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Status"),
        help_text=_("وضعیت فعال/غیرفعال راهنما")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Guide")
        verbose_name_plural = _("Guides")
        unique_together = ['page_name', 'field_name']
        ordering = ['page_name', 'field_name']

    def __str__(self):
        if self.field_name:
            return f"{self.page_name} - {self.field_name}"
        return self.page_name


class GuideContent(models.Model):
    """
    مدل محتوای راهنما
    """
    guide = models.ForeignKey(
        Guide,
        on_delete=models.CASCADE,
        related_name='contents',
        verbose_name=_("Guide")
    )
    title = models.CharField(
        max_length=200,
        verbose_name=_("Title"),
        help_text=_("عنوان محتوای راهنما")
    )
    description = models.TextField(
        verbose_name=_("Description"),
        help_text=_("توضیحات کامل راهنما")
    )
    order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Order"),
        help_text=_("ترتیب نمایش محتوا")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Guide Content")
        verbose_name_plural = _("Guide Contents")
        ordering = ['guide', 'order', 'id']

    def __str__(self):
        return f"{self.guide} - {self.title}"

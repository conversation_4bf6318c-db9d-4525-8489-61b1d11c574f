#!/bin/bash

# Production build script with static files management
# Usage: ./build_production.sh

echo "🏗️  Starting production build process..."

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down

# Remove old images (optional - uncomment if needed)
# echo "🗑️  Removing old images..."
# docker-compose -f docker-compose.prod.yml down --rmi all

# Build new images
echo "🔨 Building new Docker images..."
docker-compose -f docker-compose.prod.yml build --no-cache

# Start services
echo "🚀 Starting production services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to start
echo "⏳ Waiting for services to initialize..."
sleep 30

# Check if web service is running
echo "🔍 Checking service status..."
if docker-compose -f docker-compose.prod.yml ps | grep -q "qatreh_web.*Up"; then
    echo "✅ Web service is running!"
    
    # Show static files status
    echo "📊 Checking static files in container..."
    docker exec qatreh_web ls -la /usr/src/app/static/ || echo "⚠️  Could not access static files directory"

    echo "🎨 Checking landing page files..."
    docker exec qatreh_web ls -la /usr/src/app/static/staticfiles/landing_page/ 2>/dev/null || echo "⚠️  Landing page files not found"
    
    # Show container logs (last 20 lines)
    echo "📋 Recent container logs:"
    docker-compose -f docker-compose.prod.yml logs --tail=20 web
    
    echo "🎉 Production build completed successfully!"
    echo "🌐 Application should be available at: http://localhost:8019"
    
else
    echo "❌ Web service failed to start!"
    echo "📋 Container logs:"
    docker-compose -f docker-compose.prod.yml logs web
    exit 1
fi

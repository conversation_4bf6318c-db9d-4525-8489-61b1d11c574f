version: '3.8'


services:
  web:
    build: .
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/usr/src/app
      - ./volumes/static_data:/usr/src/app/static/
    ports:
      - "9000:8000"
    env_file:
      - .env.dev
    depends_on:
      - postgres
    networks:
      - qatreh
  
  postgres:
    ports:
      - "5441:5432"
    image: postgres:13.7

    volumes:
      - ./volumes/postgres_data:/var/lib/postgresql/data
    env_file:
      - .env.dev
    networks:
      - qatreh


volumes:
  postgres_data:
  staticfiles:
networks:
  qatreh:

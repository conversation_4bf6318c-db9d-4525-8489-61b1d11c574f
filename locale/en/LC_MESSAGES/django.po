# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#: config/settings/base.py:469 config/settings/base.py:481
#: config/settings/base.py:492 config/settings/base.py:578
#: config/settings/base.py:588
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-26 18:07+0330\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
#: apps/account/admin/notification.py:18
msgid "Notification Information"
msgstr ""

#: apps/account/admin/notification.py:21 apps/deposit/admin/media.py:51
#: apps/issues/admin/issue_report.py:48 apps/lottery/admin/admin_pre.py:34
#: apps/region/admin.py:24 apps/request/admin/deposit_request.py:113
#: apps/request/admin/deposit_request.py:142
#: apps/request/admin/deposit_request.py:186
#: apps/request/admin/deposit_request.py:233
#: apps/voting/admin/voting_report.py:65
msgid "Timestamps"
msgstr ""

#: apps/account/admin/user.py:78
msgid "Location"
msgstr ""

#: apps/account/admin/user.py:82
msgid "Password"
msgstr ""

#: apps/account/admin/user.py:86 apps/account/admin/user.py:119
msgid "Permissions"
msgstr ""

#: apps/account/admin/user.py:94 apps/deposit/admin/deposit.py:84
#: apps/deposit/admin/deposit.py:252 apps/deposit/admin/deposit.py:354
#: apps/deposit/admin/deposit.py:449 apps/transaction/admin/transaction.py:44
#: apps/voting/admin/voting_option.py:41 apps/voting/admin/voting_poll.py:54
msgid "Basic Information"
msgstr ""

#: apps/account/admin/user.py:101
msgid "Country & City"
msgstr ""

#: apps/account/admin/user.py:107
msgid "Device Information"
msgstr ""

#: apps/account/admin/user.py:113
msgid "Authentication"
msgstr ""

#: apps/account/admin/user.py:125
msgid "Important dates"
msgstr ""

#: apps/account/admin/user.py:134
msgid "Authentication Token"
msgstr ""

#: apps/account/admin/user.py:167 apps/deposit/admin/deposit.py:183
#: apps/deposit/admin/views.py:65 apps/loan/admin/loan.py:84
#: apps/loan/models.py:12 apps/voting/admin/voting_poll.py:104
msgid "Active"
msgstr ""

#: apps/account/admin/user.py:167 apps/deposit/admin/deposit.py:183
#: apps/deposit/admin/views.py:65 apps/voting/admin/voting_poll.py:104
msgid "Inactive"
msgstr ""

#: apps/account/models/notification.py:6
msgid "title"
msgstr ""

#: apps/account/models/notification.py:7
msgid "message"
msgstr ""

#: apps/account/models/notification.py:8 apps/account/models/user.py:154
msgid "user"
msgstr ""

#: apps/account/models/notification.py:9
msgid "is read"
msgstr ""

#: apps/account/models/notification.py:10
msgid "created at"
msgstr ""

#: apps/account/models/notification.py:11
msgid "updated at"
msgstr ""

#: apps/account/models/user.py:25
msgid "birthdate"
msgstr ""

#: apps/account/models/user.py:28
msgid "phone"
msgstr ""

#: apps/account/models/user.py:34
msgid "Gender"
msgstr ""

#: apps/account/models/user.py:42
msgid "City"
msgstr ""

#: apps/account/models/user.py:43
msgid "SHBA Number"
msgstr ""

#: apps/account/models/user.py:45
msgid "device id"
msgstr ""

#: apps/account/models/user.py:120
msgid "Admin User"
msgstr ""

#: apps/account/models/user.py:121
msgid "Admin Users"
msgstr ""

#: apps/account/models/user.py:130
msgid "Guest Admin User"
msgstr ""

#: apps/account/models/user.py:131
msgid "Guest Admin Users"
msgstr ""

#: apps/account/models/user.py:142
msgid "Region Owner"
msgstr ""

#: apps/account/models/user.py:143
msgid "Region Owners"
msgstr ""

#: apps/account/models/user.py:153
msgid "mobile device id"
msgstr ""

#: apps/account/models/user.py:155
msgid "lat"
msgstr ""

#: apps/account/models/user.py:156
msgid "lon"
msgstr ""

#: apps/account/models/user.py:157
msgid "country"
msgstr ""

#: apps/account/models/user.py:158
msgid "city"
msgstr ""

#: apps/account/models/user.py:159
msgid "IP"
msgstr ""

#: apps/account/models/user.py:160
msgid "API Version"
msgstr ""

#: apps/account/models/user.py:162
msgid "last login at"
msgstr ""

#: apps/account/models/user.py:169
msgid "login history"
msgstr ""

#: apps/account/models/user.py:170
msgid "user login histories"
msgstr ""

#: apps/account/templates/account/group_help_text.html:5
msgid "Driver before template"
msgstr ""

#: apps/account/templates/account/group_help_text.html:11
msgid "Active drivers"
msgstr ""

#: apps/account/templates/account/group_help_text.html:19
msgid "Inactive drivers"
msgstr ""

#: apps/account/templates/account/group_help_text.html:27
msgid "Total points"
msgstr ""

#: apps/account/templates/account/group_help_text.html:35
msgid "Total races"
msgstr ""

#: apps/account/templates/account/user_list_section.html:8
msgid "Total Actice Users"
msgstr ""

#: apps/account/templates/account/user_list_section.html:16
msgid "Total Guest Users"
msgstr ""

#: apps/account/templates/account/user_list_section.html:22
msgid "Total Students"
msgstr ""

#: apps/account/templates/account/user_list_section.html:28
msgid "Total Professors"
msgstr ""

#: apps/deposit/admin/deposit.py:91 apps/deposit/admin/deposit.py:259
#: apps/deposit/admin/deposit.py:361 apps/deposit/admin/deposit.py:456
#: apps/deposit/admin/deposit2.py:89 apps/deposit/admin/deposit2.py:91
#: apps/deposit/admin/deposit2.py:124 apps/deposit/admin/deposit2.py:159
#: apps/deposit/admin/deposit2.py:193 apps/transaction/admin/transaction.py:51
msgid "Financial Details"
msgstr ""

#: apps/deposit/admin/deposit.py:97 apps/deposit/admin/deposit.py:265
#: apps/deposit/admin/deposit.py:367 apps/deposit/admin/deposit.py:462
msgid "Membership Settings"
msgstr ""

#: apps/deposit/admin/deposit.py:103
msgid "Dates"
msgstr ""

#: apps/deposit/admin/deposit.py:109 apps/deposit/admin/deposit.py:277
#: apps/deposit/admin/deposit.py:373 apps/deposit/admin/deposit.py:468
#: apps/deposit/admin/deposit2.py:110 apps/deposit/admin/deposit2.py:112
#: apps/deposit/admin/deposit2.py:135 apps/deposit/admin/deposit2.py:170
#: apps/deposit/admin/deposit2.py:199
msgid "Rules"
msgstr ""

#: apps/deposit/admin/deposit.py:115 apps/deposit/admin/deposit.py:283
#: apps/deposit/admin/deposit.py:379 apps/deposit/admin/deposit.py:474
#: apps/lottery/admin/admin.py:53 apps/payment/admin/payment.py:78
#: apps/transaction/admin/transaction.py:63
msgid "System Information"
msgstr ""

#: apps/deposit/admin/deposit.py:132 apps/region/admin.py:32
msgid "Members Count"
msgstr ""

#: apps/deposit/admin/deposit.py:191
msgid "No Owner"
msgstr ""

#: apps/deposit/admin/deposit.py:204 apps/deposit/admin/media.py:73
#: apps/deposit/admin/views.py:372 apps/transaction/admin/transaction.py:122
msgid "Created Date"
msgstr ""

#: apps/deposit/admin/deposit.py:218
msgid "Deposit Members"
msgstr ""

#: apps/deposit/admin/deposit.py:227
msgid "User Full Name"
msgstr ""

#: apps/deposit/admin/deposit.py:231
msgid "Shares & Monthly Installment"
msgstr ""

#: apps/deposit/admin/deposit.py:271
msgid "Poll Specific"
msgstr ""

#: apps/deposit/admin/deposit2.py:20 apps/deposit/admin/views.py:57
#: apps/deposit/models/deposit.py:221
msgid "Member"
msgstr ""

#: apps/deposit/admin/deposit2.py:21 apps/deposit/admin/deposit2.py:116
#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:305
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:315
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:301
msgid "Members"
msgstr ""

#: apps/deposit/admin/deposit2.py:83 apps/ticket/admin.py:36
msgid "General Information"
msgstr ""

#: apps/deposit/admin/deposit2.py:100
msgid "Dates & Lottery"
msgstr ""

#: apps/deposit/admin/deposit2.py:102 apps/deposit/admin/deposit2.py:130
#: apps/deposit/admin/deposit2.py:165
msgid "Dates and Duration"
msgstr ""

#: apps/deposit/admin/deposit2.py:222
msgid "Financial Information"
msgstr ""

#: apps/deposit/admin/duedate.py:29 apps/lottery/admin/admin_pre.py:46
msgid "Deposit Title"
msgstr ""

#: apps/deposit/admin/media.py:82
msgid "Images Count"
msgstr ""

#: apps/deposit/admin/media.py:88
msgid "Complete"
msgstr ""

#: apps/deposit/admin/media.py:88
msgid "Incomplete"
msgstr ""

#: apps/deposit/admin/media.py:97
msgid "Media Images"
msgstr ""

#: apps/deposit/admin/media.py:105 apps/voting/admin/voting_report_image.py:40
msgid "Image"
msgstr ""

#: apps/deposit/admin/media.py:109
msgid "No Image"
msgstr ""

#: apps/deposit/admin/views.py:57 apps/deposit/admin/views.py:97
#: apps/deposit/admin/views.py:371 apps/issues/admin/issue_report.py:44
#: apps/issues/admin/issue_report.py:74 apps/issues/models.py:32
#: apps/loan/models.py:53 apps/request/admin/deposit_request.py:33
#: apps/request/admin/deposit_request.py:182
#: apps/request/admin/deposit_request.py:229 apps/request/models.py:245
msgid "Status"
msgstr ""

#: apps/deposit/admin/views.py:57 apps/deposit/models/deposit.py:226
msgid "Role"
msgstr ""

#: apps/deposit/admin/views.py:57
msgid "Shares"
msgstr ""

#: apps/deposit/admin/views.py:57 apps/request/admin/deposit_request.py:153
msgid "Monthly Installment"
msgstr ""

#: apps/deposit/admin/views.py:57
msgid "Actions"
msgstr ""

#: apps/deposit/admin/views.py:77
msgid "View"
msgstr ""

#: apps/deposit/admin/views.py:97 apps/deposit/models/duedate.py:12
msgid "Due Date Number"
msgstr ""

#: apps/deposit/admin/views.py:97 apps/deposit/models/duedate.py:13
#: apps/loan/models.py:150 apps/lottery/admin/admin.py:86
#: apps/lottery/models.py:21 apps/request/admin/deposit_request.py:204
#: apps/transaction/models.py:20
msgid "Due Date"
msgstr ""

#: apps/deposit/admin/views.py:97
msgid "Total Deposits"
msgstr ""

#: apps/deposit/admin/views.py:97
msgid "Paid Members"
msgstr ""

#: apps/deposit/admin/views.py:110
msgid "Current"
msgstr ""

#: apps/deposit/admin/views.py:112 apps/loan/admin/loan.py:85
#: apps/loan/models.py:13
msgid "Completed"
msgstr ""

#: apps/deposit/admin/views.py:114 apps/loan/admin/installment.py:101
#: apps/request/admin/deposit_request.py:40 apps/request/models.py:122
#: apps/request/models.py:202 apps/transaction/models.py:10
msgid "Pending"
msgstr ""

#: apps/deposit/admin/views.py:244
msgid "Poll Deposit Details"
msgstr ""

#: apps/deposit/admin/views.py:292
msgid "Reporting Deposit Details"
msgstr ""

#: apps/deposit/admin/views.py:347
msgid "Saving Deposit Details"
msgstr ""

#: apps/deposit/admin/views.py:366
msgid "Member Name"
msgstr ""

#: apps/deposit/admin/views.py:367 apps/loan/models.py:33
#: apps/request/admin/deposit_request.py:193
msgid "Loan Amount"
msgstr ""

#: apps/deposit/admin/views.py:368 apps/loan/models.py:37
#: apps/request/models.py:137
msgid "Installment Count"
msgstr ""

#: apps/deposit/admin/views.py:369
msgid "Paid Installments"
msgstr ""

#: apps/deposit/admin/views.py:370
msgid "Total Paid Amount"
msgstr ""

#: apps/deposit/models/deposit.py:12
#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:135
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:144
msgid "Poll Deposit"
msgstr ""

#: apps/deposit/models/deposit.py:13
msgid "Saving Account"
msgstr ""

#: apps/deposit/models/deposit.py:14
msgid "Reporting Account"
msgstr ""

#: apps/deposit/models/deposit.py:19
msgid "Deposit Type"
msgstr ""

#: apps/deposit/models/deposit.py:22 apps/deposit/models/deposit.py:220
msgid "Owner"
msgstr ""

#: apps/deposit/models/deposit.py:23
msgid "Region"
msgstr ""

#: apps/deposit/models/deposit.py:23
msgid "The region to which this deposit belongs."
msgstr ""

#: apps/deposit/models/deposit.py:25 utils/keyval_field.py:39
#: utils/keyval_field.py:59 utils/keyval_field.py:122 utils/keyval_field.py:150
msgid "Title"
msgstr ""

#: apps/deposit/models/deposit.py:26
msgid "Deposit Description"
msgstr ""

#: apps/deposit/models/deposit.py:28
msgid "Total Debt Amount"
msgstr ""

#: apps/deposit/models/deposit.py:31
msgid "Number of Lottery Months"
msgstr ""

#: apps/deposit/models/deposit.py:33
msgid "Unit Shares Amount"
msgstr ""

#: apps/deposit/models/deposit.py:35
msgid "Payment Cycle (in months)"
msgstr ""

#: apps/deposit/models/deposit.py:37
msgid "Maximum Unit Shares Per Request"
msgstr ""

#: apps/deposit/models/deposit.py:39
msgid "Maximum Number of Members"
msgstr ""

#: apps/deposit/models/deposit.py:41
msgid "Validity Duration (in months)"
msgstr ""

#: apps/deposit/models/deposit.py:43
msgid "Initial Lottery Date"
msgstr ""

#: apps/deposit/models/deposit.py:45
msgid "Start Date"
msgstr ""

#: apps/deposit/models/deposit.py:47
msgid "Deposit Rules"
msgstr ""

#: apps/deposit/models/deposit.py:49 apps/deposit/models/deposit.py:241
#: apps/voting/models/voting.py:36
msgid "Is Active"
msgstr ""

#: apps/deposit/models/deposit.py:222
msgid "Admin"
msgstr ""

#: apps/deposit/models/deposit.py:224 apps/issues/admin/issue_report.py:59
#: apps/issues/models.py:17 apps/payment/models.py:17
#: apps/request/admin/deposit_request.py:50 apps/ticket/admin.py:92
#: apps/ticket/models.py:16 apps/ticket/models.py:42
msgid "User"
msgstr ""

#: apps/deposit/models/deposit.py:225 apps/deposit/models/duedate.py:9
#: apps/deposit/models/media.py:13 apps/issues/admin/issue_report.py:68
#: apps/issues/models.py:23 apps/loan/models.py:20
#: apps/lottery/admin/admin.py:82 apps/lottery/models.py:9
#: apps/payment/models.py:20 apps/request/admin/deposit_request.py:64
#: apps/request/models.py:210 apps/ticket/models.py:10
#: apps/voting/models/reporting.py:12 apps/voting/models/voting.py:15
msgid "Deposit"
msgstr ""

#: apps/deposit/models/deposit.py:229 apps/request/models.py:71
msgid "Requested Unit Count"
msgstr ""

#: apps/deposit/models/deposit.py:230 apps/request/models.py:72
msgid "The number of unit shares requested by the user."
msgstr ""

#: apps/deposit/models/deposit.py:236 apps/request/models.py:77
msgid "Monthly Installment Amount"
msgstr ""

#: apps/deposit/models/deposit.py:237 apps/request/models.py:78
msgid "The monthly installment amount for the user."
msgstr ""

#: apps/deposit/models/deposit.py:242
msgid "Joined Date"
msgstr ""

#: apps/deposit/models/deposit.py:246
msgid "Membership"
msgstr ""

#: apps/deposit/models/deposit.py:247
msgid "Memberships"
msgstr ""

#: apps/deposit/models/duedate.py:14
msgid "Is Completed"
msgstr ""

#: apps/deposit/models/duedate.py:19
msgid "Deposit Due Date"
msgstr ""

#: apps/deposit/models/duedate.py:20
msgid "Deposit Due Dates"
msgstr ""

#: apps/deposit/models/media.py:14
msgid "The deposit associated with this media."
msgstr ""

#: apps/deposit/models/media.py:18 apps/issues/models.py:26
#: apps/ticket/models.py:19 apps/voting/models/reporting.py:17
#: utils/schema.py:16
msgid "Subject"
msgstr ""

#: apps/deposit/models/media.py:19
msgid "The subject or title of the media."
msgstr ""

#: apps/deposit/models/media.py:22 apps/issues/models.py:27
#: apps/loan/models.py:45 apps/request/models.py:145 apps/ticket/models.py:20
#: apps/voting/models/reporting.py:21 utils/keyval_field.py:145
#: utils/keyval_field.py:155 utils/schema.py:20
msgid "Description"
msgstr ""

#: apps/deposit/models/media.py:23
msgid "A detailed description of the media."
msgstr ""

#: apps/deposit/models/media.py:29 apps/issues/models.py:34
#: apps/loan/models.py:58 apps/loan/models.py:166
#: apps/lottery/admin/admin.py:94 apps/lottery/models.py:26
#: apps/payment/models.py:58 apps/request/models.py:256
#: apps/ticket/models.py:21 apps/ticket/models.py:47
#: apps/transaction/models.py:27 apps/voting/admin/vote.py:96
#: apps/voting/models/reporting.py:28 apps/voting/models/voting.py:32
msgid "Created At"
msgstr ""

#: apps/deposit/models/media.py:30
msgid "The date and time when the media was created."
msgstr ""

#: apps/deposit/models/media.py:34 apps/issues/models.py:35
#: apps/loan/models.py:63 apps/loan/models.py:170 apps/payment/models.py:60
#: apps/request/models.py:261 apps/ticket/models.py:22
#: apps/voting/models/reporting.py:33
msgid "Updated At"
msgstr ""

#: apps/deposit/models/media.py:35
msgid "The date and time when the media was last updated."
msgstr ""

#: apps/deposit/models/media.py:39 apps/deposit/models/media.py:40
#: apps/deposit/models/media.py:52
msgid "Deposit Media"
msgstr ""

#: apps/deposit/models/media.py:53
msgid "The media associated with this image."
msgstr ""

#: apps/deposit/models/media.py:57 apps/voting/models/reporting.py:55
msgid "image"
msgstr ""

#: apps/deposit/models/media.py:61 apps/voting/models/reporting.py:59
msgid "Priority"
msgstr ""

#: apps/deposit/models/media.py:62 apps/voting/models/reporting.py:60
msgid "Priority of the image, lower values mean higher priority."
msgstr ""

#: apps/deposit/models/media.py:66
msgid "Merchant Image"
msgstr ""

#: apps/deposit/models/media.py:67
msgid "Merchant Images"
msgstr ""

#: apps/issues/admin/issue_report.py:40
msgid "Relationship"
msgstr ""

#: apps/issues/admin/issue_report.py:55
msgid "Issue"
msgstr ""

#: apps/issues/admin/issue_report.py:81 apps/issues/models.py:10
msgid "Open"
msgstr ""

#: apps/issues/admin/issue_report.py:82 apps/issues/models.py:11
msgid "In Progress"
msgstr ""

#: apps/issues/admin/issue_report.py:83 apps/issues/models.py:12
msgid "Closed"
msgstr ""

#: apps/issues/admin/issue_report.py:87
#: apps/request/admin/deposit_request.py:46 apps/ticket/admin.py:45
msgid "Created"
msgstr ""

#: apps/issues/admin/issue_report.py:95
msgid "Mark as In Progress"
msgstr ""

#: apps/issues/admin/issue_report.py:98
msgid "Selected issues have been marked as in progress."
msgstr ""

#: apps/issues/admin/issue_report.py:100
msgid "Mark as Closed"
msgstr ""

#: apps/issues/admin/issue_report.py:103
msgid "Selected issues have been marked as closed."
msgstr ""

#: apps/issues/admin/issue_report.py:105
msgid "Reopen Issues"
msgstr ""

#: apps/issues/admin/issue_report.py:108
msgid "Selected issues have been reopened."
msgstr ""

#: apps/issues/models.py:38
msgid "Issue Report"
msgstr ""

#: apps/issues/models.py:39
msgid "Issue Reports"
msgstr ""

#: apps/issues/views.py:25
msgid "Deposit with the given ID does not exist."
msgstr ""

#: apps/loan/admin/installment.py:24 apps/loan/models.py:174
#: apps/payment/admin/payment.py:23
msgid "Loan Installment"
msgstr ""

#: apps/loan/admin/installment.py:25 apps/loan/models.py:175
msgid "Loan Installments"
msgstr ""

#: apps/loan/admin/installment.py:58 apps/payment/admin/payment.py:72
msgid "Payment Details"
msgstr ""

#: apps/loan/admin/installment.py:62 apps/loan/admin/loan.py:49
#: apps/ticket/admin.py:39
msgid "Metadata"
msgstr ""

#: apps/loan/admin/installment.py:83 apps/loan/admin/loan.py:65
#: apps/payment/admin/payment.py:131 apps/transaction/admin/transaction.py:88
#: apps/voting/admin/voting_poll.py:86 apps/voting/admin/voting_report.py:81
msgid "No Deposit"
msgstr ""

#: apps/loan/admin/installment.py:97
msgid "Paid"
msgstr ""

#: apps/loan/admin/installment.py:99 apps/loan/admin/loan.py:86
#: apps/loan/models.py:14
msgid "Overdue"
msgstr ""

#: apps/loan/admin/installment.py:111 apps/loan/admin/loan.py:101
#: apps/payment/admin/payment.py:134 apps/transaction/admin/transaction.py:117
#: apps/voting/admin/vote.py:68 apps/voting/admin/vote.py:104
msgid "No User"
msgstr ""

#: apps/loan/admin/installment.py:119
msgid "Mark as Paid"
msgstr ""

#: apps/loan/admin/installment.py:132
msgid "Successfully marked {} installments as paid."
msgstr ""

#: apps/loan/admin/installment.py:135
msgid "Mark as Unpaid"
msgstr ""

#: apps/loan/admin/installment.py:150
msgid "Successfully marked {} installments as unpaid."
msgstr ""

#: apps/loan/admin/loan.py:45
msgid "Installment Details"
msgstr ""

#: apps/loan/admin/loan.py:66
msgid "Installments: {}/{}"
msgstr ""

#: apps/loan/admin/loan.py:118
msgid "Update Status"
msgstr ""

#: apps/loan/admin/loan.py:128
msgid "Successfully updated status for {} loans."
msgstr ""

#: apps/loan/models.py:21
msgid "The deposit associated with this loan."
msgstr ""

#: apps/loan/models.py:27 apps/lottery/models.py:15 apps/payment/models.py:23
#: apps/request/models.py:217
msgid "Deposit Membership"
msgstr ""

#: apps/loan/models.py:28
msgid "The membership associated with this loan."
msgstr ""

#: apps/loan/models.py:34
msgid "The total amount of the loan."
msgstr ""

#: apps/loan/models.py:38
msgid "The number of installments for this loan."
msgstr ""

#: apps/loan/models.py:41 apps/request/models.py:140
msgid "Installment Day of Month"
msgstr ""

#: apps/loan/models.py:42
msgid "The day of the month when installments are due (1-31)."
msgstr ""

#: apps/loan/models.py:47
msgid "Additional information about the loan."
msgstr ""

#: apps/loan/models.py:54
msgid "The current status of the loan."
msgstr ""

#: apps/loan/models.py:59
msgid "The date and time when the loan was created."
msgstr ""

#: apps/loan/models.py:64
msgid "The date and time when the loan was last updated."
msgstr ""

#: apps/loan/models.py:68 apps/loan/models.py:136
msgid "Loan"
msgstr ""

#: apps/loan/models.py:69
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:281
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:303
msgid "Loans"
msgstr ""

#: apps/loan/models.py:137
msgid "The loan this installment belongs to."
msgstr ""

#: apps/loan/models.py:140
msgid "Installment Number"
msgstr ""

#: apps/loan/models.py:141
msgid "The sequence number of this installment."
msgstr ""

#: apps/loan/models.py:146 apps/payment/models.py:34 apps/request/models.py:135
#: apps/voting/models/reporting.py:14
msgid "Amount"
msgstr ""

#: apps/loan/models.py:147
msgid "The amount of this installment."
msgstr ""

#: apps/loan/models.py:151
msgid "The date when this installment is due."
msgstr ""

#: apps/loan/models.py:155
msgid "Is Paid"
msgstr ""

#: apps/loan/models.py:156
msgid "Whether this installment has been paid."
msgstr ""

#: apps/loan/models.py:161
msgid "Paid Date"
msgstr ""

#: apps/loan/models.py:162
msgid "The date when this installment was paid."
msgstr ""

#: apps/lottery/admin/admin.py:46
msgid "Lottery Information"
msgstr ""

#: apps/lottery/admin/admin.py:67
msgid "Lottery"
msgstr ""

#: apps/lottery/admin/admin.py:74
msgid "Winner"
msgstr ""

#: apps/lottery/admin/admin.py:90 apps/lottery/models.py:24
msgid "Lottery Number"
msgstr ""

#: apps/lottery/admin/admin_pre.py:42
msgid "Winner Name"
msgstr ""

#: apps/lottery/models.py:29
msgid "Deposit Lotttery"
msgstr ""

#: apps/lottery/models.py:30
msgid "Deposit Lotteries"
msgstr ""

#: apps/payment/admin/payment.py:28 apps/payment/models.py:38
msgid "Includes Loan Installment"
msgstr ""

#: apps/payment/admin/payment.py:29
msgid "Without Loan Installment"
msgstr ""

#: apps/payment/admin/payment.py:65
msgid "Relations"
msgstr ""

#: apps/payment/admin/payment.py:99
msgid "Includes Loan Installment: {}"
msgstr ""

#: apps/payment/admin/payment.py:101
msgid "No Gateway"
msgstr ""

#: apps/payment/admin/payment.py:117
msgid "With Loan Installment"
msgstr ""

#: apps/payment/admin/payment.py:118
msgid "Regular Payment"
msgstr ""

#: apps/payment/admin/payment.py:139 apps/payment/models.py:54
msgid "Payment Date"
msgstr ""

#: apps/payment/apps.py:8 apps/payment/models.py:64 config/settings/base.py:562
#: config/settings/base.py:566
#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:269
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:279
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:266
msgid "Payments"
msgstr ""

#: apps/payment/models.py:27 apps/transaction/models.py:36
msgid "Transaction"
msgstr ""

#: apps/payment/models.py:31
msgid "Payment Gateway"
msgstr ""

#: apps/payment/models.py:32
msgid "Name of the payment gateway used"
msgstr ""

#: apps/payment/models.py:36
msgid "Payment amount in IRR"
msgstr ""

#: apps/payment/models.py:39
msgid "Whether this payment includes a loan installment"
msgstr ""

#: apps/payment/models.py:42
msgid "Loan Installment Amount"
msgstr ""

#: apps/payment/models.py:43
msgid "Amount of loan installment included in this payment"
msgstr ""

#: apps/payment/models.py:45
msgid "Total Amount"
msgstr ""

#: apps/payment/models.py:46
msgid "Total payment amount including any loan installment"
msgstr ""

#: apps/payment/models.py:49
msgid "Reference Number"
msgstr ""

#: apps/payment/models.py:50
msgid "Payment reference or tracking number"
msgstr ""

#: apps/payment/models.py:55
msgid "Date and time when the payment was made"
msgstr ""

#: apps/payment/models.py:63
msgid "Payment"
msgstr ""

#: apps/payment/serializers.py:44
msgid "Ensure this value is greater than or equal to 0.01."
msgstr ""

#: apps/payment/serializers.py:51
msgid ""
"Loan installment amount must be greater than zero when "
"includes_loan_installment is True."
msgstr ""

#: apps/payment/serializers.py:70
msgid "You are not an active member of this deposit."
msgstr ""

#: apps/payment/views.py:159
msgid "Deposit not found"
msgstr ""

#: apps/request/admin/deposit_request.py:41 apps/request/models.py:123
#: apps/request/models.py:203
msgid "Approved"
msgstr ""

#: apps/request/admin/deposit_request.py:42 apps/request/models.py:124
#: apps/request/models.py:204
msgid "Rejected"
msgstr ""

#: apps/request/admin/deposit_request.py:70
msgid "Approve Request"
msgstr ""

#: apps/request/admin/deposit_request.py:74
msgid "Selected requests have been approved."
msgstr ""

#: apps/request/admin/deposit_request.py:76
msgid "Reject Request"
msgstr ""

#: apps/request/admin/deposit_request.py:80
msgid "Selected requests have been rejected."
msgstr ""

#: apps/request/admin/deposit_request.py:138
msgid "Request Details"
msgstr ""

#: apps/request/admin/deposit_request.py:149
msgid "Requested Units"
msgstr ""

#: apps/request/admin/deposit_request.py:178
msgid "Loan Details"
msgstr ""

#: apps/request/admin/deposit_request.py:200
msgid "Installments"
msgstr ""

#: apps/request/admin/deposit_request.py:226
msgid "Request Information"
msgstr ""

#: apps/request/admin/deposit_request.py:240 apps/request/models.py:228
msgid "Withdrawal Amount"
msgstr ""

#: apps/request/admin/deposit_request.py:247
msgid "Account Holder"
msgstr ""

#: apps/request/admin/deposit_request.py:251 apps/request/models.py:233
msgid "IBAN"
msgstr ""

#: apps/request/models.py:20 apps/request/models.py:65
#: apps/request/models.py:130 apps/request/models.py:249
msgid "Rejection Reason"
msgstr ""

#: apps/request/models.py:21 apps/request/models.py:66
#: apps/request/models.py:131 apps/request/models.py:250
msgid "The reason for rejecting the request."
msgstr ""

#: apps/request/models.py:135
msgid "The amount of the loan request."
msgstr ""

#: apps/request/models.py:141
msgid "The day of month (1-31) when installments are due."
msgstr ""

#: apps/request/models.py:211
msgid "The deposit associated with this withdrawal request."
msgstr ""

#: apps/request/models.py:218
msgid "The membership that requested the withdrawal."
msgstr ""

#: apps/request/models.py:222
msgid "Account Holder Name"
msgstr ""

#: apps/request/models.py:223
msgid "Full name of the account holder."
msgstr ""

#: apps/request/models.py:229
msgid "The amount to be withdrawn."
msgstr ""

#: apps/request/models.py:234
msgid "26-digit IBAN number starting with IR."
msgstr ""

#: apps/request/models.py:237
msgid "Withdrawal Reason"
msgstr ""

#: apps/request/models.py:238
msgid "Reason for the withdrawal request."
msgstr ""

#: apps/request/models.py:246
msgid "The current status of the withdrawal request."
msgstr ""

#: apps/request/models.py:257
msgid "The date and time when the request was created."
msgstr ""

#: apps/request/models.py:262
msgid "The date and time when the request was last updated."
msgstr ""

#: apps/request/models.py:266
msgid "Withdrawal Request"
msgstr ""

#: apps/request/models.py:267 config/settings/base.py:554
msgid "Withdrawal Requests"
msgstr ""

#: apps/request/models.py:278
msgid "IBAN must be 26 characters long and start with 'IR'."
msgstr ""

#: apps/ticket/admin.py:21 apps/ticket/models.py:55
msgid "Ticket Message"
msgstr ""

#: apps/ticket/admin.py:22 apps/ticket/models.py:56
msgid "Ticket Messages"
msgstr ""

#: apps/ticket/admin.py:55
msgid "Messages Count"
msgstr ""

#: apps/ticket/admin.py:63 apps/ticket/models.py:45
msgid "Is Read"
msgstr ""

#: apps/ticket/admin.py:71 apps/ticket/admin.py:79
msgid "Messages"
msgstr ""

#: apps/ticket/models.py:28 apps/ticket/models.py:36
msgid "Ticket"
msgstr ""

#: apps/ticket/models.py:29 config/settings/base.py:591
#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:290
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:170
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:300
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:183
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:286
msgid "Tickets"
msgstr ""

#: apps/ticket/models.py:46
msgid "Content"
msgstr ""

#: apps/transaction/admin/transaction.py:57
msgid "Additional Information"
msgstr ""

#: apps/transaction/admin/transaction.py:92
msgid "No description"
msgstr ""

#: apps/transaction/models.py:11
msgid "Success"
msgstr ""

#: apps/transaction/models.py:12
msgid "Failed"
msgstr ""

#: apps/transaction/models.py:15
msgid "Income"
msgstr ""

#: apps/transaction/models.py:16
msgid "Withdrawal"
msgstr ""

#: apps/transaction/models.py:24
msgid "Amount in IRR"
msgstr ""

#: apps/transaction/models.py:24
msgid "Amount in Iranian Rial (IRR)"
msgstr ""

#: apps/transaction/models.py:37
#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:274
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:284
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:271
msgid "Transactions"
msgstr ""

#: apps/transaction/models.py:51
msgid "Transaction amount must be greater than zero."
msgstr ""

#: apps/transaction/models.py:54
msgid "The due date must belong to the same deposit."
msgstr ""

#: apps/voting/admin/vote.py:43
msgid "Vote Information"
msgstr ""

#: apps/voting/admin/vote.py:50 apps/voting/admin/voting_poll.py:61
msgid "Timing"
msgstr ""

#: apps/voting/admin/vote.py:82 apps/voting/admin/voting_option.py:67
msgid "No Poll"
msgstr ""

#: apps/voting/admin/vote.py:83
msgid "No Option"
msgstr ""

#: apps/voting/admin/vote.py:105
msgid "Voter"
msgstr ""

#: apps/voting/admin/voting_option.py:48 apps/voting/admin/voting_poll.py:67
#: apps/voting/admin/voting_report.py:59
msgid "Statistics"
msgstr ""

#: apps/voting/admin/voting_option.py:101
#: apps/voting/admin/voting_option.py:126
msgid "votes"
msgstr ""

#: apps/voting/admin/voting_option.py:113
msgid "Color"
msgstr ""

#: apps/voting/admin/voting_option.py:129
msgid "Votes Percentage"
msgstr ""

#: apps/voting/admin/voting_option.py:133 apps/voting/admin/voting_poll.py:131
msgid "Votes Count"
msgstr ""

#: apps/voting/admin/voting_poll.py:24 apps/voting/models/voting.py:97
msgid "Voting Option"
msgstr ""

#: apps/voting/admin/voting_poll.py:25 apps/voting/models/voting.py:98
msgid "Voting Options"
msgstr ""

#: apps/voting/admin/voting_poll.py:103
msgid "Expired"
msgstr ""

#: apps/voting/admin/voting_poll.py:123
msgid "End Date"
msgstr ""

#: apps/voting/admin/voting_poll.py:127
msgid "Options Count"
msgstr ""

#: apps/voting/admin/voting_report.py:52
msgid "Report Information"
msgstr ""

#: apps/voting/admin/voting_report.py:93 apps/voting/admin/voting_report.py:107
msgid "images"
msgstr ""

#: apps/voting/admin/voting_report.py:108
msgid "Number of Images"
msgstr ""

#: apps/voting/admin/voting_report_image.py:55
#: apps/voting/admin/voting_report_image.py:80
msgid "No Report"
msgstr ""

#: apps/voting/admin/voting_report_image.py:81
msgid "Report Subject"
msgstr ""

#: apps/voting/admin/voting_report_image.py:87
msgid "Image Preview"
msgstr ""

#: apps/voting/models/reporting.py:18
msgid "The subject or title of the report."
msgstr ""

#: apps/voting/models/reporting.py:22
msgid "A detailed description of the report."
msgstr ""

#: apps/voting/models/reporting.py:29
msgid "The date and time when the report was created."
msgstr ""

#: apps/voting/models/reporting.py:34
msgid "The date and time when the report was last updated."
msgstr ""

#: apps/voting/models/reporting.py:38 apps/voting/models/reporting.py:50
msgid "Voting Report"
msgstr ""

#: apps/voting/models/reporting.py:39
msgid "Voting Reports"
msgstr ""

#: apps/voting/models/reporting.py:51
msgid "The report associated with this image."
msgstr ""

#: apps/voting/models/reporting.py:64
msgid "Report Image"
msgstr ""

#: apps/voting/models/reporting.py:65
msgid "Report Images"
msgstr ""

#: apps/voting/models/voting.py:19
msgid "Voting Topic"
msgstr ""

#: apps/voting/models/voting.py:22
msgid "Voting Description"
msgstr ""

#: apps/voting/models/voting.py:27
msgid "Voting Deadline (days)"
msgstr ""

#: apps/voting/models/voting.py:28
msgid "Number of days the voting will be open"
msgstr ""

#: apps/voting/models/voting.py:40
msgid "Is Deleted"
msgstr ""

#: apps/voting/models/voting.py:44 apps/voting/models/voting.py:77
#: apps/voting/models/voting.py:119
msgid "Voting Poll"
msgstr ""

#: apps/voting/models/voting.py:45
msgid "Voting Polls"
msgstr ""

#: apps/voting/models/voting.py:81
msgid "Option Title"
msgstr ""

#: apps/voting/models/voting.py:84
msgid "Option Description"
msgstr ""

#: apps/voting/models/voting.py:90
msgid "Color Name"
msgstr ""

#: apps/voting/models/voting.py:93
msgid "Name of the color representing this option"
msgstr ""

#: apps/voting/models/voting.py:125
msgid "Selected Option"
msgstr ""

#: apps/voting/models/voting.py:131
msgid "Deposit Member"
msgstr ""

#: apps/voting/models/voting.py:135
msgid "Voted At"
msgstr ""

#: apps/voting/models/voting.py:139
msgid "Vote"
msgstr ""

#: apps/voting/models/voting.py:140
msgid "Votes"
msgstr ""

#: apps/voting/models/voting.py:155
msgid "The selected option does not belong to this voting poll."
msgstr ""

#: apps/voting/models/voting.py:159
msgid "The voting deadline has passed."
msgstr ""

#: apps/voting/models/voting.py:163
msgid "This voting poll is not active."
msgstr ""

#: apps/voting/models/voting.py:167
msgid "The member does not belong to this deposit."
msgstr ""

#: apps/voting/models/voting.py:171
msgid "The membership is not active."
msgstr ""

#: config/settings/base.py:211
msgid "Persian"
msgstr ""

#: config/settings/base.py:212
msgid "English"
msgstr ""

#: config/settings/base.py:317 config/settings/base.py:318
msgid "Hamjib Admin"
msgstr ""

#: config/settings/base.py:319
msgid "Hamjib "
msgstr ""

#: config/settings/base.py:427
msgid "Users"
msgstr ""

#: config/settings/base.py:434
msgid "Guest Users"
msgstr ""

#: config/settings/base.py:474 templates/admin/index.html:8 utils/admin.py:107
msgid "Dashboard"
msgstr ""

#: config/settings/base.py:484
msgid "Accounts"
msgstr ""

#: config/settings/base.py:495
msgid "Regions"
msgstr ""

#: config/settings/base.py:506
msgid "Deposits"
msgstr ""

#: config/settings/base.py:511
msgid "Poll Deposits"
msgstr ""

#: config/settings/base.py:517
msgid "Reporting Deposits"
msgstr ""

#: config/settings/base.py:523
msgid "Saving Deposits"
msgstr ""

#: config/settings/base.py:531
msgid "Requests"
msgstr ""

#: config/settings/base.py:536
msgid "Create Deposit Requests"
msgstr ""

#: config/settings/base.py:542
msgid "Join Deposit Requests"
msgstr ""

#: config/settings/base.py:548
msgid "Loan Requests"
msgstr ""

#: config/settings/base.py:571
msgid "Transctions"
msgstr ""

#: config/settings/base.py:581
msgid "Issues"
msgstr ""

#: templates/admin/auth/user/change_password.html:10
#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:131
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:140
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:153
msgid "Home"
msgstr ""

#: templates/admin/auth/user/change_password.html:14
#: templates/admin/auth/user/change_password.html:52
msgid "Change password"
msgstr ""

#: templates/admin/auth/user/change_password.html:25
msgid "Please correct the error below."
msgstr ""

#: templates/admin/auth/user/change_password.html:25
msgid "Please correct the errors below."
msgstr ""

#: templates/admin/auth/user/change_password.html:29
#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""

#: templates/admin/base_site.html:3 templates/admin/index.html:8
#: templates/admin/login.html:32 templates/docs.html:5
msgid "Django site admin"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:139
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:148
msgid "Deposit Detail"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:161
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:173
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:186
msgid "View/Edit Details"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:173
msgid "Total Loan Amount:"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:184
msgid "Payment Due:"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:195
msgid "Number of Completed Lotteries:"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:203
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:207
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:209
msgid "Amount per Share:"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:211
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:265
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:242
msgid "Due Date:"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:220
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:254
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:251
msgid "Region Name:"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:279
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:289
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:276
msgid "Membership Requests"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:284
msgid "Winners"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:295
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:305
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:291
msgid "Media"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:306
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:316
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:302
msgid "DueDates"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:327
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:337
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:317
#: templates/unfold/components/table.html:44
msgid "No data"
msgstr ""

#: templates/admin/deposit/detail_views/poll_deposit_detail_view.html:339
#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:349
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:331
msgid "No due dates available"
msgstr ""

#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:185
msgid "Total Paid In:"
msgstr ""

#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:196
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:198
msgid "Deposit Balance:"
msgstr ""

#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:218
#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:220
msgid "Payment Cycle:"
msgstr ""

#: templates/admin/deposit/detail_views/reporting_deposit_detail_view.html:294
msgid "Reports"
msgstr ""

#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:157
msgid "Saving Deposit"
msgstr ""

#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:161
msgid "Saving Deposit Detail"
msgstr ""

#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:231
msgid "Validity Duration:"
msgstr ""

#: templates/admin/deposit/detail_views/saving_deposit_detail_view.html:345
msgid "No loans available"
msgstr ""

#: templates/admin/includes/object_delete_summary.html:2
msgid "Summary"
msgstr ""

#: templates/admin/login.html:31
msgid "Welcome back to"
msgstr ""

#: templates/admin/login.html:44
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""

#: templates/admin/login.html:67
msgid "Log in"
msgstr ""

#: templates/admin/login.html:76
msgid "Forgotten your password or username?"
msgstr ""

#: templates/deposit/deposit_poll_section.html:14
msgid "مبلغ کل وام"
msgstr ""

#: templates/deposit/deposit_poll_section.html:17
#: templates/deposit/deposit_poll_section.html:26
msgid "ریال"
msgstr ""

#: templates/deposit/deposit_poll_section.html:23
msgid "مبلغ هر سهم"
msgstr ""

#: templates/deposit/deposit_poll_section.html:34
msgid "دوره پرداخت"
msgstr ""

#: templates/deposit/deposit_poll_section.html:38
#: templates/deposit/deposit_poll_section.html:48
msgid "ماه"
msgstr ""

#: templates/deposit/deposit_poll_section.html:44
msgid "تعداد ماه‌های قرعه‌کشی"
msgstr ""

#: templates/deposit/deposit_poll_section.html:59
msgid "تکمیل شده"
msgstr ""

#: templates/deposit/deposit_poll_section.html:60
msgid "سهم‌های تکمیل شده:"
msgstr ""

#: templates/deposit/deposit_poll_section.html:67
msgid "تاریخ قرعه‌کشی اولیه:"
msgstr ""

#: utils/__init__.py:75
msgid "Development"
msgstr ""

#: utils/__init__.py:77
msgid "Production"
msgstr ""

#: utils/admin.py:108
msgid "Analytics"
msgstr ""

#: utils/admin.py:109
msgid "Settings"
msgstr ""

#: utils/admin.py:112
msgid "All"
msgstr ""

#: utils/admin.py:114
msgid "New"
msgstr ""

#: utils/admin.py:224
msgid "Last week revenue"
msgstr ""

#: utils/admin.py:242
msgid "Last week expenses"
msgstr ""

#: utils/keyval_field.py:16 utils/keyval_field.py:37 utils/keyval_field.py:57
#: utils/keyval_field.py:77 utils/keyval_field.py:99 utils/keyval_field.py:120
msgid "Translation"
msgstr ""

#: utils/keyval_field.py:18 utils/keyval_field.py:79
msgid "Detail"
msgstr ""

#: utils/keyval_field.py:23 utils/keyval_field.py:44 utils/keyval_field.py:64
#: utils/keyval_field.py:84 utils/keyval_field.py:106 utils/keyval_field.py:127
msgid "Language Code"
msgstr ""

#: utils/keyval_field.py:101
msgid "Name"
msgstr ""

#: utils/schema.py:14
msgid " "
msgstr ""

import json

from django.db import models

from utils.json_editor_field import JsonEditorWidget
from django.utils.translation import gettext_lazy as _
from dj_language.models import Language

def get_simcard_detail_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'detail': {'type': 'string', 'title': str(_('Detail'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }


def get_simcard_title_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'title': {'type': 'string', 'title': str(_('Title'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }

def get_product_title_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'title': {'type': 'string', 'title': str(_('Title'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }

def get_product_detail_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'detail': {'type': 'string', "format": "textarea",'title': str(_('Detail'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }



def get_name_translation_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'name': {'type': 'string', 'title': str(_('Name'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }


def get_translation_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'title': {'type': 'string', 'title': str(_('Title'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }


def get_travel_guide_schema():
    from dj_language.models import Language
    from django.utils.translation import gettext_lazy as _
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        "required_by_default": 1,
        'items': {
            'type': 'object',
            "required": ["title", "description"],  
            'title': str(_('Description')),
            'properties': {
                'title': {
                    'type': 'string',
                    "format": "textarea",
                    'title': str(_('Title'))
                },
                'description': {
                    'type': "string",
                    "format": "textarea",
                    'title': str(_('Description'))
                }
            }
        }
    }


class JsonKeyValueField(models.JSONField):
    description = "custom json key value field"

    def __init__(self, key_index='key', value_index='value', schema=None, *args, **kwargs):
        self.key_index = key_index
        self.value_index = value_index
        self.schema = schema or {
            'type': "array",
            'format': 'table',
            'title': ' ',
            'items': {
                'type': 'object',
                'title': str('Title'),
                'properties': {
                    self.key_index: {'type': 'string', 'title': self.key_index.title()},
                    self.value_index: {'type': 'string', 'title': self.value_index.title()},
                }
            }
        }
        kwargs.setdefault('default', dict)
        super().__init__(*args, **kwargs)

    def save_form_data(self, instance, data):
        _data = {}
        for i in data:
            key, value = i[self.key_index], i[self.value_index]
            _data[key] = value

        return super().save_form_data(instance, _data)

    def value_from_object(self, obj):
        _data = []
        field = getattr(obj, self.attname, {})
        for key, val in field.items():
            _data.append({
                self.key_index: key,
                self.value_index: val
            })
        return _data

    def formfield(self, **kwargs):
        schema = self.schema() if callable(self.schema) else self.schema
        if type(schema) is dict or type(schema) is list:
            schema = json.dumps(schema)

        return super().formfield(**{
            'widget': JsonEditorWidget(attrs={'schema': schema}),
            'encoder': self.encoder,
            'decoder': self.decoder,
            **kwargs,
        })

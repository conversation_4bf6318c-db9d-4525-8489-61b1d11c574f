import os

from filer.models.filemodels import File
import os
from typing import Union

from django.core.files.base import ContentFile
from filer.models import Image, File, Folder
from django.core.files import File as DjangoFile


def get_django_file(path):
    return DjangoFile(open(path, mode='rb'), name=os.path.basename(path))


def import_file(file_obj: Union[str, object], folder):
    if type(file_obj) is str:
        file_obj = get_django_file(file_obj)

    if type(folder) is str:
        folder, _ = Folder.objects.get_or_create(name=folder)

    try:
        iext = os.path.splitext(file_obj.name)[1].lower()
    except:  # noqa
        iext = ''

    if iext in ['.jpg', '.jpeg', '.png', '.gif']:
        obj, created = Image.objects.get_or_create(
            original_filename=file_obj.name,
            file=file_obj,
            folder=folder,
            is_public=True
        )
    else:
        obj, created = File.objects.get_or_create(
            original_filename=file_obj.name,
            file=file_obj,
            folder=folder,
            is_public=True
        )

    return obj



def save_to_filer(name: str, remove_dir_after_import: bool = False):
    if f := File.objects.filter(original_filename=name, owner=None).first():
        return f

    directory = os.path.dirname(name)
    os.system(f'python manage.py import_files --path="{directory}"')
    if remove_dir_after_import:
        os.system(f'rm -rf "{directory}"')

    return File.objects.filter(original_filename=os.path.basename(name), owner=None).first()


def save_thumb(url, save_folder):
    from filer.models.filemodels import File
    import os
    import secrets
    import requests

    name = secrets.token_urlsafe(8) + '.jpg'

    p = f'/tmp/{save_folder}'
    os.system(f'rm -rf {p} && mkdir {p}')

    with open(f'{p}/{name}.jpg', 'wb') as f:
        resp = requests.get(url)
        if resp and resp.status_code == 200:
            f.write(resp.content)
        else:
            return None

    os.system(f'python manage.py import_files --path={p} --folder=youtube')

    return File.objects.filter(original_filename=f'{name}.jpg', owner=None).first()


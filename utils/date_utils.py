"""
Utility functions for date conversions between <PERSON><PERSON> and <PERSON><PERSON><PERSON> (Persian/Solar) calendars.
"""
from datetime import datetime
import jdatetime


def gregorian_to_jalali(date_obj=None, date_format="%Y/%m/%d"):
    """
    Convert a Gregorian date to J<PERSON><PERSON> (Persian/Solar) date.
    
    Args:
        date_obj (datetime.datetime or datetime.date, optional): The Gregorian datetime or date object to convert. 
                                                               If None, current date and time will be used.
        date_format (str, optional): The format for the output Jalali date string.
                                    Default is "%Y/%m/%d" (e.g., 1402/12/25).
    
    Returns:
        str: Formatted Jalali date string.
        
    Example:
        >>> gregorian_to_jalali(datetime(2023, 3, 21))
        '1402/01/01'
        >>> gregorian_to_jalali(datetime(2023, 3, 21, 14, 30), "%Y/%m/%d %H:%M")
        '1402/01/01 14:30'
        >>> gregorian_to_jalali()  # Current date
        '1402/12/25'  # (example output)
    """
    from datetime import date
    
    if date_obj is None:
        date_obj = datetime.now()
    
    # Convert Gregorian date to Jalali
    # Check if date_obj is already a date object or a datetime object
    if isinstance(date_obj, date) and not isinstance(date_obj, datetime):
        # It's a date object, use it directly
        jalali_date = jdatetime.date.fromgregorian(date=date_obj)
        # Format the Jalali date according to the specified format
        return jalali_date.strftime(date_format)
    else:
        # It's a datetime object, preserve the time component
        jalali_datetime = jdatetime.datetime.fromgregorian(datetime=date_obj)
        # Format the Jalali datetime according to the specified format
        return jalali_datetime.strftime(date_format)


def jalali_to_gregorian(jalali_year, jalali_month, jalali_day):
    """
    Convert a Jalali (Persian/Solar) date to Gregorian date.
    
    Args:
        jalali_year (int): Year in Jalali calendar.
        jalali_month (int): Month in Jalali calendar (1-12).
        jalali_day (int): Day in Jalali calendar (1-31).
    
    Returns:
        datetime.date: Gregorian date object.
        
    Example:
        >>> jalali_to_gregorian(1402, 1, 1)
        datetime.date(2023, 3, 21)
    """
    # Create a Jalali date object
    jalali_date = jdatetime.date(jalali_year, jalali_month, jalali_day)
    
    # Convert to Gregorian
    gregorian_date = jalali_date.togregorian()
    
    return gregorian_date


def format_jalali_date(date_obj=None, date_format="%Y/%m/%d"):
    """
    Format a Gregorian date as a Jalali date string with month names.
    
    Args:
        date_obj (datetime.datetime or datetime.date, optional): The Gregorian datetime or date object to convert.
                                                               If None, current date and time will be used.
        date_format (str, optional): The format for the output Jalali date string.
                                    Default is "%Y/%m/%d".
                                    Use %B for full month name, %b for abbreviated month name.
    
    Returns:
        str: Formatted Jalali date string.
        
    Example:
        >>> format_jalali_date(datetime(2023, 3, 21), "%d %B %Y")
        '01 فروردین 1402'
    """
    from datetime import date
    
    if date_obj is None:
        date_obj = datetime.now()
    
    # Convert Gregorian date to Jalali
    # Check if date_obj is already a date object or a datetime object
    if isinstance(date_obj, date) and not isinstance(date_obj, datetime):
        # It's a date object, convert to datetime first
        # We need to use datetime for month names
        jalali_datetime = jdatetime.datetime.fromgregorian(
            year=date_obj.year, 
            month=date_obj.month, 
            day=date_obj.day
        )
    else:
        # It's a datetime object
        jalali_datetime = jdatetime.datetime.fromgregorian(datetime=date_obj)
    
    # Format the Jalali date according to the specified format
    return jalali_datetime.strftime(date_format)
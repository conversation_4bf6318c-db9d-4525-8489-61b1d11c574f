


cities = [
    {"id": 1, "name": "بغداد", "slug": "baghdad", "population": 7216000, "coordinates": (33.3152, 44.3661)},
    {"id": 2, "name": "بصره", "slug": "basra", "population": 2600000, "coordinates": (30.5080, 47.7835)},
    {"id": 3, "name": "موصل", "slug": "mosul", "population": 2065597, "coordinates": (36.3566, 43.1590)},
    {"id": 4, "name": "اربیل", "slug": "erbil", "population": 932800, "coordinates": (36.1900, 44.0092)},
    {"id": 5, "name": "کرکوک", "slug": "kirkuk", "population": 601433, "coordinates": (35.4690, 44.3890)},
    {"id": 6, "name": "نجف", "slug": "najaf", "population": 482576, "coordinates": (31.9950, 44.3148)},
    {"id": 7, "name": "کربلا", "slug": "karbala", "population": 434450, "coordinates": (32.6160, 44.0240)},
    {"id": 8, "name": "سلیمانیه", "slug": "sulaymaniyah", "population": 723170, "coordinates": (35.5610, 45.4300)},
    {"id": 9, "name": "دیوانیه", "slug": "diwaniyah", "population": 318801, "coordinates": (31.9876, 44.9240)},
    {"id": 10, "name": "رمادی", "slug": "ramadi", "population": 274539, "coordinates": (33.4250, 43.3000)},
    {"id": 11, "name": "سامرا", "slug": "samarra", "population": 158508, "coordinates": (34.1920, 43.8732)},
    {"id": 12, "name": "کوت", "slug": "kut", "population": 315162, "coordinates": (32.5128, 45.8204)},
    {"id": 13, "name": "حله", "slug": "hilla", "population": 289709, "coordinates": (32.4635, 44.4196)},
    {"id": 14, "name": "دهوک", "slug": "duhok", "population": 284000, "coordinates": (36.8610, 43.0000)},
    {"id": 15, "name": "فالوجه", "slug": "fallujah", "population": 190159, "coordinates": (33.3490, 43.7850)},
    {"id": 16, "name": "نصیریه", "slug": "nasiriyah", "population": 400249, "coordinates": (31.0420, 46.2570)},
    {"id": 17, "name": "عماره", "slug": "amarah", "population": 323302, "coordinates": (31.8354, 47.1449)},
    {"id": 18, "name": "بعقوبه", "slug": "baqubah", "population": 152550, "coordinates": (33.7458, 44.6437)},
    {"id": 19, "name": "زاخو", "slug": "zakho", "population": 210000, "coordinates": (37.1444, 42.6875)},
    {"id": 20, "name": "تل عفر", "slug": "tal-afar", "population": 180000, "coordinates": (36.3740, 42.4480)},
]

def get_city_name(slug: str):
    city_name = next((city['name'] for city in cities if city['slug'] == slug), None)
    return city_name if city_name else None

def get_city_slug(name: str):
    city_slug = next((city['slug'] for city in cities if city['name'] == name), None)
    return city_slug if city_slug else None

from django.utils.translation import gettext_lazy as _




def get_rules_deposit_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        "required_by_default": 1,
        'items': {
            'type': 'object',
            'title': str(_(' ')),
            'properties': {
                'subject': {'type': 'string', "format": "textarea",'title': str(_('Subject'))},
                'description': {
                    'type': "string",
                    "format": "textarea",
                    'title': str(_('Description'))
                }
            }
        }
    }


def get_notification_data_schema():
    return {
        'type': 'object',
        'title': str(_('Notification Data')),
        'properties': {
            'action': {
                'type': 'string', 
                'title': str(_('Action Type')),
                'description': 'Type of action that triggered the notification'
            },
            'user_id': {
                'type': 'integer', 
                'title': str(_('User ID')),
                'description': 'ID of the user involved'
            },
            'deposit_id': {
                'type': 'integer', 
                'title': str(_('Deposit ID')),
                'description': 'ID of the deposit involved'
            },
            'region_id': {
                'type': 'integer', 
                'title': str(_('Region ID')),
                'description': 'ID of the region involved'
            },
            'amount': {
                'type': 'number', 
                'title': str(_('Amount')),
                'description': 'Amount involved in the notification'
            },
            'status': {
                'type': 'string', 
                'title': str(_('Status')),
                'description': 'Current status'
            },
            'additional_info': {
                'type': 'object',
                'title': str(_('Additional Information')),
                'description': 'Any additional data related to the notification',
                'additionalProperties': True
            }
        },
        'additionalProperties': True
    }



import json
import random
from functools import lru_cache

from django.contrib.humanize.templatetags.humanize import intcomma
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from django.views.generic import RedirectView, TemplateView

# Import Filer admin classes
from filer.admin.fileadmin import FileAdmin
from filer.admin.folderadmin import FolderAdmin
from filer.admin.imageadmin import ImageAdmin
from filer.models import File, Folder, Image

def dashboard_callback(request, context):
    context.update(random_data())
    return context

import json
import random
from functools import lru_cache

from django.contrib.humanize.templatetags.humanize import intcomma
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from django.views.generic import RedirectView, TemplateView
from unfold.views import UnfoldModelAdminViewMixin


from unfold.sites import UnfoldAdminSite
from django import forms
from django.conf import settings
from unfold.forms import AuthenticationForm
from unfold.admin import ModelAdmin

from django.views.generic import FormView, RedirectView
from django.contrib import admin
from django.db import models
from django.core.exceptions import PermissionDenied


class LoginForm(AuthenticationForm):
    password = forms.CharField(widget=forms.PasswordInput(render_value=True))

    def __init__(self, request=None, *args, **kwargs):
        super().__init__(request, *args, **kwargs)
        # Change the label of the username field to "Email"
        self.fields["username"].label = "Email"



class FormulaAdminSite(UnfoldAdminSite):
    login_form = LoginForm
    site_url = None

project_admin_site = FormulaAdminSite()

# # Register Filer models with the admin site
# project_admin_site.register(Folder, FolderAdmin)
# project_admin_site.register(File, FileAdmin)
# project_admin_site.register(Image, ImageAdmin)


class RegionOwnerPermissionMixin:
    """
    Mixin for admin classes that should be accessible to region owners.
    Filters data based on the user's owned region.
    """

    def has_module_permission(self, request):
        """Allow region owners to see the module"""
        if request.user.is_superuser:
            return True
        # Check if user owns any region
        return hasattr(request.user, 'owned_regions') and request.user.owned_regions.exists()

    def has_view_permission(self, request, obj=None):
        """Allow region owners to view objects in their region"""
        if request.user.is_superuser:
            return True
        if not hasattr(request.user, 'owned_regions') or not request.user.owned_regions.exists():
            return False
        if obj is None:
            return True
        return self._user_can_access_object(request.user, obj)

    def has_change_permission(self, request, obj=None):
        """Allow region owners to change objects in their region"""
        if request.user.is_superuser:
            return True
        if not hasattr(request.user, 'owned_regions') or not request.user.owned_regions.exists():
            return False
        if obj is None:
            return True
        return self._user_can_access_object(request.user, obj)

    def has_add_permission(self, request):
        """Allow region owners to add objects"""
        if request.user.is_superuser:
            return True
        return hasattr(request.user, 'owned_regions') and request.user.owned_regions.exists()

    def has_delete_permission(self, request, obj=None):
        """Allow region owners to delete objects in their region"""
        if request.user.is_superuser:
            return True
        if not hasattr(request.user, 'owned_regions') or not request.user.owned_regions.exists():
            return False
        if obj is None:
            return True
        return self._user_can_access_object(request.user, obj)

    def _user_can_access_object(self, user, obj):
        """Check if user can access the given object based on region ownership"""
        user_regions = user.owned_regions.all()

        # Direct region relationship
        if hasattr(obj, 'region'):
            return obj.region in user_regions

        # Through deposit relationship
        if hasattr(obj, 'deposit') and hasattr(obj.deposit, 'region'):
            return obj.deposit.region in user_regions

        # Through ticket relationship
        if hasattr(obj, 'ticket') and hasattr(obj.ticket, 'region'):
            return obj.ticket.region in user_regions

        # Through voting poll relationship
        if hasattr(obj, 'voting_poll') and hasattr(obj.voting_poll.deposit, 'region'):
            return obj.voting_poll.deposit.region in user_regions

        # Through membership relationship
        if hasattr(obj, 'membership') and hasattr(obj.membership.deposit, 'region'):
            return obj.membership.deposit.region in user_regions

        # Through deposit membership relationship
        if hasattr(obj, 'deposit_membership') and hasattr(obj.deposit_membership.deposit, 'region'):
            return obj.deposit_membership.deposit.region in user_regions

        # Through loan relationship
        if hasattr(obj, 'loan') and hasattr(obj.loan.deposit, 'region'):
            return obj.loan.deposit.region in user_regions

        # Through due_date relationship
        if hasattr(obj, 'due_date') and hasattr(obj.due_date.deposit, 'region'):
            return obj.due_date.deposit.region in user_regions

        return False


class SuperAdminOnlyMixin:
    """
    Mixin for admin classes that should only be accessible to superusers.
    Completely hides these models from region owners.
    """

    def has_module_permission(self, request):
        """Only allow superusers to see the module"""
        return request.user.is_superuser

    def has_view_permission(self, request, obj=None):
        """Only allow superusers to view objects"""
        return request.user.is_superuser

    def has_change_permission(self, request, obj=None):
        """Only allow superusers to change objects"""
        return request.user.is_superuser

    def has_add_permission(self, request):
        """Only allow superusers to add objects"""
        return request.user.is_superuser

    def has_delete_permission(self, request, obj=None):
        """Only allow superusers to delete objects"""
        return request.user.is_superuser


class RegionFilteredAdmin(RegionOwnerPermissionMixin, ModelAdmin):
    """
    Base admin class for models that should be filtered by region.
    Automatically filters querysets and form choices based on user's owned regions.
    """

    def get_queryset(self, request):
        """Filter queryset based on user's owned regions"""
        qs = super().get_queryset(request)

        if request.user.is_superuser:
            return qs

        if not hasattr(request.user, 'owned_regions') or not request.user.owned_regions.exists():
            return qs.none()

        user_regions = request.user.owned_regions.all()
        return self._filter_queryset_by_regions(qs, user_regions)

    def _filter_queryset_by_regions(self, qs, user_regions):
        """Filter queryset based on region relationships"""
        model = qs.model

        # Direct region relationship
        if hasattr(model, 'region'):
            return qs.filter(region__in=user_regions)

        # Through deposit relationship
        if hasattr(model, 'deposit'):
            return qs.filter(deposit__region__in=user_regions)

        # Through ticket relationship
        if hasattr(model, 'ticket'):
            return qs.filter(ticket__region__in=user_regions)

        # Through voting poll relationship
        if hasattr(model, 'voting_poll'):
            return qs.filter(voting_poll__deposit__region__in=user_regions)

        # Through membership relationship
        if hasattr(model, 'membership'):
            return qs.filter(membership__deposit__region__in=user_regions)

        # Through deposit membership relationship
        if hasattr(model, 'deposit_membership'):
            return qs.filter(deposit_membership__deposit__region__in=user_regions)

        # Through loan relationship
        if hasattr(model, 'loan'):
            return qs.filter(loan__deposit__region__in=user_regions)

        # Through due_date relationship
        if hasattr(model, 'due_date'):
            return qs.filter(due_date__deposit__region__in=user_regions)

        return qs

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter foreign key choices based on user's owned regions"""
        if request.user.is_superuser:
            return super().formfield_for_foreignkey(db_field, request, **kwargs)

        if not hasattr(request.user, 'owned_regions') or not request.user.owned_regions.exists():
            kwargs["queryset"] = db_field.related_model.objects.none()
            return super().formfield_for_foreignkey(db_field, request, **kwargs)

        user_regions = request.user.owned_regions.all()

        # Filter based on the related model
        if db_field.name == 'region':
            kwargs["queryset"] = user_regions
        elif db_field.name == 'deposit':
            kwargs["queryset"] = db_field.related_model.objects.filter(region__in=user_regions)
        elif db_field.name == 'user':
            # Filter users to those who are members of the region owner's regions
            kwargs["queryset"] = db_field.related_model.objects.filter(
                region_memberships__region__in=user_regions
            ).distinct()
        elif db_field.name == 'deposit_membership':
            kwargs["queryset"] = db_field.related_model.objects.filter(
                deposit__region__in=user_regions
            )
        elif db_field.name == 'ticket':
            kwargs["queryset"] = db_field.related_model.objects.filter(
                region__in=user_regions
            )
        elif db_field.name == 'voting_poll':
            kwargs["queryset"] = db_field.related_model.objects.filter(
                deposit__region__in=user_regions
            )
        elif db_field.name == 'loan':
            kwargs["queryset"] = db_field.related_model.objects.filter(
                deposit__region__in=user_regions
            )
        elif db_field.name == 'due_date':
            kwargs["queryset"] = db_field.related_model.objects.filter(
                deposit__region__in=user_regions
            )

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def formfield_for_manytomany(self, db_field, request, **kwargs):
        """Filter many-to-many choices based on user's owned regions"""
        if request.user.is_superuser:
            return super().formfield_for_manytomany(db_field, request, **kwargs)

        if not hasattr(request.user, 'owned_regions') or not request.user.owned_regions.exists():
            kwargs["queryset"] = db_field.related_model.objects.none()
            return super().formfield_for_manytomany(db_field, request, **kwargs)

        user_regions = request.user.owned_regions.all()

        # Apply similar filtering logic as foreign keys
        if hasattr(db_field.related_model, 'region'):
            kwargs["queryset"] = db_field.related_model.objects.filter(region__in=user_regions)
        elif hasattr(db_field.related_model, 'deposit'):
            kwargs["queryset"] = db_field.related_model.objects.filter(deposit__region__in=user_regions)

        return super().formfield_for_manytomany(db_field, request, **kwargs)


class SuperAdminOnlyAdmin(SuperAdminOnlyMixin, ModelAdmin):
    """
    Base admin class for models that should only be accessible to superusers.
    """
    pass


class HomeView(RedirectView):
    pattern_name = "admin:index"


def variables(request):
    return {"plausible_domain": settings.PLAUSIBLE_DOMAIN}

# class MyClassBasedView(UnfoldModelAdminViewMixin, TemplateView):
#     title = "Custom Title"  # required: custom page header title
#     # required: tuple of permissions
#     permission_required = (
#         "formula.view_driver",
#         "formula.add_driver",
#         "formula.change_driver",
#         "formula.delete_driver",
#     )
#     template_name = "formula/driver_custom_page.html"


def dashboard_callback(request, context):
    context.update(random_data())
    return context


@lru_cache
def random_data():
    WEEKDAYS = [
        "Mon",
        "Tue",
        "Wed",
        "Thu",
        "Fri",
        "Sat",
        "Sun",
    ]

    positive = [[1, random.randrange(8, 28)] for i in range(1, 28)]
    negative = [[-1, -random.randrange(8, 28)] for i in range(1, 28)]
    average = [r[1] - random.randint(3, 5) for r in positive]
    performance_positive = [[1, random.randrange(8, 28)] for i in range(1, 28)]
    performance_negative = [[-1, -random.randrange(8, 28)] for i in range(1, 28)]

    return {
        "navigation": [
            {"title": _("Dashboard"), "link": "/", "active": True},
            {"title": _("Analytics"), "link": "#"},
            {"title": _("Settings"), "link": "#"},
        ],
        "filters": [
            {"title": _("All"), "link": "#", "active": True},
            {
                "title": _("New"),
                "link": "#",
            },
        ],
        "kpi": [
            {
                "title": "Product A Performance",
                "metric": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "footer": mark_safe(
                    f'<strong class="text-green-700 font-semibold dark:text-green-400">+{intcomma(f"{random.uniform(1, 9):.02f}")}%</strong>&nbsp;progress from last week'
                ),
                "chart": json.dumps(
                    {
                        "labels": [WEEKDAYS[day % 7] for day in range(1, 28)],
                        "datasets": [{"data": average, "borderColor": "#9333ea"}],
                    }
                ),
            },
            {
                "title": "Product B Performance",
                "metric": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "footer": mark_safe(
                    f'<strong class="text-green-700 font-semibold dark:text-green-400">+{intcomma(f"{random.uniform(1, 9):.02f}")}%</strong>&nbsp;progress from last week'
                ),
            },
            {
                "title": "Product C Performance",
                "metric": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "footer": mark_safe(
                    f'<strong class="text-green-700 font-semibold dark:text-green-400">+{intcomma(f"{random.uniform(1, 9):.02f}")}%</strong>&nbsp;progress from last week'
                ),
            },
        ],
        "progress": [
            {
                "title": "🦆 Social marketing e-book",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
            {
                "title": "🦍 Freelancing tasks",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
            {
                "title": "🐋 Development coaching",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
            {
                "title": "🦑 Product consulting",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
            {
                "title": "🐨 Other income",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
            {
                "title": "🐶 Course sales",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
            {
                "title": "🐻‍❄️ Ads revenue",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
            {
                "title": "🦩 Customer Retention Rate",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
            {
                "title": "🦊 Marketing ROI",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
            {
                "title": "🦁 Affiliate partnerships",
                "description": f"${intcomma(f'{random.uniform(1000, 9999):.02f}')}",
                "value": random.randint(10, 90),
            },
        ],
        "chart": json.dumps(
            {
                "labels": [WEEKDAYS[day % 7] for day in range(1, 28)],
                "datasets": [
                    {
                        "label": "Example 1",
                        "type": "line",
                        "data": average,
                        "borderColor": "var(--color-primary-500)",
                    },
                    {
                        "label": "Example 2",
                        "data": positive,
                        "backgroundColor": "var(--color-primary-700)",
                    },
                    {
                        "label": "Example 3",
                        "data": negative,
                        "backgroundColor": "var(--color-primary-300)",
                    },
                ],
            }
        ),
        "performance": [
            {
                "title": _("Last week revenue"),
                "metric": "$1,234.56",
                "footer": mark_safe(
                    '<strong class="text-green-600 font-medium">+3.14%</strong>&nbsp;progress from last week'
                ),
                "chart": json.dumps(
                    {
                        "labels": [WEEKDAYS[day % 7] for day in range(1, 28)],
                        "datasets": [
                            {
                                "data": performance_positive,
                                "borderColor": "var(--color-primary-700)",
                            }
                        ],
                    }
                ),
            },
            {
                "title": _("Last week expenses"),
                "metric": "$1,234.56",
                "footer": mark_safe(
                    '<strong class="text-green-600 font-medium">+3.14%</strong>&nbsp;progress from last week'
                ),
                "chart": json.dumps(
                    {
                        "labels": [WEEKDAYS[day % 7] for day in range(1, 28)],
                        "datasets": [
                            {
                                "data": performance_negative,
                                "borderColor": "var(--color-primary-300)",
                            }
                        ],
                    }
                ),
            },
        ],
    }


from djmoney.money import Money
from decimal import Decimal
from apps.tasrif.models import CurrencyRate

def convert_currency_pure(from_rate, to_rate, amount):
    
    converted_amount = (amount / from_rate.rate) * to_rate.rate

    if to_rate.code == "IRR":
        converted_amount = round(converted_amount)

    one_unit_conversion = to_rate.rate / from_rate.rate

    return converted_amount, one_unit_conversion
    
def convert_currency(amount, from_currency_code, to_currency_code):
    try:
        from_currency_rate = CurrencyRate.objects.get(code=from_currency_code)
        to_currency_rate = CurrencyRate.objects.get(code=to_currency_code)

        # مطمئن شدن از نوع داده Decimal برای نرخ‌ها
        from_rate = Decimal(to_currency_rate.rate)
        to_rate = Decimal(from_currency_rate.rate)
        
        # محاسبه نرخ تبدیل
        conversion_rate = from_rate / to_rate

        # تبدیل مبلغ
        converted_amount = Decimal(amount) * conversion_rate
            
        # ایجاد نمونه Money
        converted_money = Money(converted_amount, to_currency_code)
        
        return converted_money
    except CurrencyRate.DoesNotExist:
        return None  # یا می‌توانید خطا را مدیریت کنید
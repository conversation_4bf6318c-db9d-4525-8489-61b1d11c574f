#!/bin/sh

echo "Waiting for postgres..."
# sleep 20

# echo "Creating static directories..."
# mkdir -p /usr/src/app/static/staticfiles
# mkdir -p /usr/src/app/static/media
# chmod -R 755 /usr/src/app/static

# echo "Collecting static files..."
# python manage.py collectstatic --noinput 

# echo "Verifying static files..."
# ls -la /usr/src/app/static/staticfiles/ | head -10
# echo "Landing page files:"
# ls -la /usr/src/app/static/staticfiles/landing_page/ 2>/dev/null || echo "Landing page directory not found"

# echo "Running migrations..."
# python manage.py migrate --noinput

echo "Starting application..."
exec "$@"

# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
.venv
venv/
ENV/
env/
.env.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node modules (if any)
node_modules/

# Documentation
README.md
docs/

# Development files
*.md
.editorconfig

# Don't ignore static files - we need them for production
# static/
# media/

# Ignore collected static files (will be regenerated)
static/staticfiles/
static/media/

# Ignore development database
db.sqlite3

# Ignore test files
tests/
*_test.py
test_*.py

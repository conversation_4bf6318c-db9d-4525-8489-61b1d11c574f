---
description: Repository Information Overview
alwaysApply: true
---

# Qatreh Backend Information

## Summary
A Django-based backend application for a financial platform that manages deposits, loans, payments, and user accounts. The system includes features for voting, lottery, ticketing, and regional management.

## Structure
- **apps/**: Contains all Django applications (account, deposit, loan, payment, etc.)
- **config/**: Project configuration, settings, and URL routing
- **templates/**: HTML templates for admin and frontend views
- **static/**: Static files (CSS, JS, images)
- **tests/**: API test scripts and test data
- **utils/**: Utility functions and helper classes

## Language & Runtime
**Language**: Python
**Version**: 3.9 (based on Dockerfile)
**Framework**: Django 4.2+
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- Django 4.2+: Web framework
- Django REST Framework 3.16.0: API development
- Celery 5.2.1: Asynchronous task queue
- PostgreSQL: Database (via psycopg2-binary)
- Redis: Caching and Celery broker
- django-unfold 0.54.0: Admin interface theme
- drf-yasg 1.21.10: API documentation
- django-filer 3.3.1: File management
- django-dynamic-preferences 1.16.0: User preferences

**Frontend Dependencies**:
- TailwindCSS 3.3.1: CSS framework

## Build & Installation
```bash
# Create virtual environment
python -m venv .venv
source .venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Run development server
python manage.py runserver
```

## Docker
**Dockerfile**: Uses Python 3.9 base image
**Docker Compose**: Includes web and PostgreSQL services
**Configuration**:
```bash
# Build and start containers
docker-compose up -d --build

# Run with production settings
docker-compose -f docker-compose.prod.yml up -d
```

## Testing
**Framework**: Custom API test scripts using requests library
**Test Location**: tests/api/
**Naming Convention**: test_XX_feature_name.py (numbered for sequence)
**Run Command**:
```bash
# Run all tests
python tests/api/run_all_tests.py

# Run specific test
python tests/api/test_01_user_registration.py
```

## Main Components
- **Account Management**: User registration, authentication, profiles
- **Deposit System**: Creating and managing deposits
- **Loan Management**: Loan requests, approvals, installments
- **Payment Processing**: Transaction handling with Zarinpal integration
- **Voting System**: Creating polls, voting, reporting results
- **Lottery System**: Managing lottery draws for deposits
- **Ticketing System**: User support and issue tracking
- **Regional Management**: Geographic organization of users and deposits
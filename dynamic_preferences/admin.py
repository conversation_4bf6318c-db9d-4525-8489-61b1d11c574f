from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django import forms

from .settings import preferences_settings
from .registries import global_preferences_registry
from .models import GlobalPreferenceModel
from .forms import GlobalSinglePreferenceForm, SinglePerInstancePreferenceForm
from django.utils.translation import gettext_lazy as _

from unfold.admin import ModelAdmin, TabularInline
from utils.admin import project_admin_site

class SectionFilter(admin.AllValuesFieldListFilter):
    def __init__(self, field, request, params, model, model_admin, field_path):
        super(SectionFilter, self).__init__(
            field, request, params, model, model_admin, field_path
        )
        parent_model, reverse_path = admin.utils.reverse_field_path(model, field_path)
        if model == parent_model:
            queryset = model_admin.get_queryset
        else:
            queryset = parent_model._default_manager.all()
        self.registries = []
        registry_name_set = set()
        for preferenceModel in queryset.distinct():
            l = len(registry_name_set)
            registry_name_set.add(preferenceModel.registry.__class__.__name__)
            if len(registry_name_set) != l:
                self.registries.append(preferenceModel.registry)

    def choices(self, changelist):
        choices = super(SectionFilter, self).choices(changelist)
        for choice in choices:
            display = choice["display"]
            try:
                for registry in self.registries:
                    display = registry.section_objects[display].verbose_name
                choice["display"] = display
            except (KeyError):
                pass
            yield choice


# Change DynamicPreferenceAdmin to inherit from unfold's ModelAdmin
class DynamicPreferenceAdmin(ModelAdmin):
    list_display = (
        "verbose_name",
        "help_text",
    )
    fields = ("raw_value", "default_value",)
    readonly_fields = ("default_value",)
    change_form_template = "dynamic_preferences/dyna_change_form.html"
    
    # Unfold specific settings
    search_fields = ["name", "section"]
    list_filter = ["section"]
    
    @admin.display(description=_('Verbose name'))
    def verbose_name(self, obj):
        return obj.verbose_name

    @admin.display(description=_('Help text'))
    def help_text(self, obj):
        return obj.help_text

    def has_add_permission(self, request):
        # if "root@admin" in request.user.username:
        #     return True
        return False

    def has_delete_permission(self, request, obj=None):
        if "root@admin" in request.user.email:
            return True
        return False

    if preferences_settings.ADMIN_ENABLE_CHANGELIST_FORM:
        def get_changelist_form(self, request, **kwargs):
            return self.changelist_form

    def default_value(self, obj):
        return obj.preference.default

    default_value.short_description = _("Default Value")

    def section_name(self, obj):
        try:
            return obj.registry.section_objects[obj.section].verbose_name
        except KeyError:
            pass
        return obj.section

    section_name.short_description = _("Section Name")

    def save_model(self, request, obj, form, change):
        pref = form.instance
        manager = pref.registry.manager()
        manager.update_db_pref(pref.section, pref.name, form.cleaned_data["raw_value"])


class GlobalPreferenceAdmin(DynamicPreferenceAdmin):
    form = GlobalSinglePreferenceForm
    changelist_form = GlobalSinglePreferenceForm
    
    # Unfold specific customizations
    list_display_links = ["verbose_name"]
    
    # You can add unfold specific features like:
    show_facets = True  # Enable faceted filtering
    
    # Optional: Add custom actions
    actions = ["reset_to_default"]
    
    def reset_to_default(self, request, queryset):
        for pref in queryset:
            manager = pref.registry.manager()
            manager.update_db_pref(pref.section, pref.name, pref.preference.default)
    reset_to_default.short_description = _("Reset selected preferences to default values")

    def get_queryset(self, *args, **kwargs):
        # Instanciate default prefs
        manager = global_preferences_registry.manager()
        manager.all()
        return super(GlobalPreferenceAdmin, self).get_queryset(*args, **kwargs)





project_admin_site.register(GlobalPreferenceModel, GlobalPreferenceAdmin)


class PerInstancePreferenceAdmin(DynamicPreferenceAdmin):
    list_display = ("instance",) + DynamicPreferenceAdmin.list_display
    fields = ("instance",) + DynamicPreferenceAdmin.fields
    raw_id_fields = ("instance",)
    form = SinglePerInstancePreferenceForm
    changelist_form = SinglePerInstancePreferenceForm
    list_select_related = True


import json

from django import forms

# from limitless_dashboard.fields.summernote import <PERSON><PERSON><PERSON><PERSON>

from dynamic_preferences.preferences import Section
from dynamic_preferences.registries import global_preferences_registry
from dynamic_preferences.types import BasePreferenceType, BaseSerializer, LongStringPreference, StringPreference, \
    FilePreference
from utils.json_editor_field import JsonEditorWidget
from django.utils.translation import gettext as _

from unfold.contrib.forms.widgets import WysiwygWidget
from unfold.widgets import UnfoldAdminTextareaWidget

class EditorPreferences(LongStringPreference):
    widget = WysiwygWidget(attrs={'class': 'editor-field'})  

class EditorTextPreferences(LongStringPreference):
    widget = UnfoldAdminTextareaWidget(attrs={'class': 'editor-field', 'rows': 20})  

@global_preferences_registry.register
class AboutUsConfig(EditorPreferences):
    section = Section('aboutus', verbose_name='AboutUsConfig')
    name = 'aboutus'
    required = False
    verbose_name = 'About Us'
    default = ''


class JsonSerializer(BaseSerializer):

    @classmethod
    def serialize(cls, value, **kwargs):
        return json.dumps(value, ensure_ascii=False)
    
    @classmethod
    def to_python(cls, value, **kwargs):
        if isinstance(value, str) and len(value.strip()) > 0:
            # print(f"VALUE TO PARSE: {value}")
            try:
                return json.loads(value)
            except json.JSONDecodeError as e:
                # print(f"JSONDecodeError: {e}")
                try:
                    value_replaced = value.replace("'", '"')
                    # print(f"VALUE AFTER REPLACEMENT: {value_replaced}")
                    return json.loads(value_replaced)
                except json.JSONDecodeError as e2:
                    print(f"JSONDecodeError after replacement: {e2}")
                    return {}
        return value
    



report_fields = {
    "type": "object",
    "format": "table",
    "title": "",
    "required_by_default": 1,
    "required": ['phone_number', "whatsapp", 'telegram', 'eitaa', 'bale', 'soroush'],
    "properties": {
        "phone_number": {"type": "string", "title": "phone_number"},
        "whatsapp": {"type": "string", "title": "whatsapp"},
        "telegram": {"type": "string", "title": "telegram"},
        "eitaa": {"type": "string", "title": "eitaa"},
        "bale": {"type": "string", "title": "bale"},
        "soroush": {"type": "string", "title": "soroush"},
    }
}


class JsonFieldReport(BasePreferenceType):
    field_class = forms.JSONField
    serializer = JsonSerializer
    widget = JsonEditorWidget(attrs={'schema': report_fields})

@global_preferences_registry.register
class ReportConfig(JsonFieldReport):
    section = Section('contact_report', verbose_name='ProblemReport')
    name = 'ReportConfig'
    required = False
    verbose_name = 'ProblemReport'
    help_text = _("Configure contact information for problem reporting including phone number, WhatsApp, Telegram, Eitaa, Bale, and Soroush.")
    default = {}
    


policy_schema = {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str('Terms and Conditions'),
            'properties': {
                'title': {'type': 'string', "format": "textarea", 'title': str('Title')},
                'description': {
                    'type': "string",
                    "format": "textarea",
                    'title': str('Description')
                }
            }
        }
    }


@global_preferences_registry.register
class PolicyConfig(JsonFieldReport):
    widget = JsonEditorWidget(attrs={'schema': policy_schema})
    section = Section('Policy', verbose_name='Terms and Conditions')
    name = 'Policy'
    required = False
    verbose_name = 'Terms and Conditions'
    help_text = _("Configure the terms and conditions that users must agree to, including title and description for each term.")
    default = {}


deposit_info_schema = {
    "type": "object",
    "format": "table",
    "title": " About Deposit",
    "required": ["title", 'information_deposit'],
    "properties": {
        "title": {"type": "string", "title": "Title Deposit"},
        "information_deposit" : {
            'type': "array",
            'format': 'table',
            'title': ' About Deposit',
            'items': {
                'type': 'object',
                'title': 'About Deposit',
                'properties': {
                    'description': {
                        'type': "string",
                        "format": "textarea",
                        'title': str('Description')
                    }
                }                
            }
        }        
    }
}

class DepositInfoFieldSocial(BasePreferenceType):
    field_class = forms.JSONField
    serializer = JsonSerializer
    widget = JsonEditorWidget(attrs={'schema': deposit_info_schema})


@global_preferences_registry.register
class PollDepositInfoConfig(DepositInfoFieldSocial):
    section = Section('poll_deposit_info', verbose_name='Poll About Deposit')
    name = 'poll_deposit_info'
    required = False
    verbose_name = 'Poll About Deposit'
    help_text = _("Configure information about poll deposits including title and detailed descriptions.")
    default = {}


@global_preferences_registry.register
class SavingDepositInfoConfig(DepositInfoFieldSocial):
    section = Section('saving_deposit_info', verbose_name='Saving About Deposit')
    name = 'saving_deposit_info'
    required = False
    verbose_name = 'Saving About Deposit'
    help_text = _("Configure information about saving deposits including title and detailed descriptions.")
    default = {}


@global_preferences_registry.register
class ReportingDepositInfoConfig(DepositInfoFieldSocial):
    section = Section('reporting_deposit_info', verbose_name='Reporting About Deposit')
    name = 'saving_deposit_reporting_deposit_infoinfo'
    required = False
    verbose_name = 'Reporting About Deposit'
    help_text = _("Configure information about reporting deposits including title and detailed descriptions.")
    default = {}
    
    
rules_schema = {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str('General rules'),
            'properties': {
                'question': {'type': 'string', "format": "textarea", 'title': str('Question')},
                'answer': {
                    'type': "string",
                    "format": "textarea",
                    'title': str('Answer')
                }
            }
        }
    }




@global_preferences_registry.register
class RulesConfig(JsonFieldReport):
    widget = JsonEditorWidget(attrs={'schema': rules_schema})
    section = Section('Rules', verbose_name='Rules')
    name = 'Rules'
    required = False
    verbose_name = 'Rules'
    help_text = _("Configure general rules with questions and answers that users need to follow.")
    default = {}


get_fqa_schema = {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str('Frequently asked questions'),
            'properties': {
                'question': {'type': 'string', "format": "textarea", 'title': str('Question')},
                'answer': {
                    'type': "string",
                    "format": "textarea",
                    'title': str('Answer')
                }
            }
        }
    }




@global_preferences_registry.register
class FAQAccountConfig(JsonFieldReport):
    widget = JsonEditorWidget(attrs={'schema': get_fqa_schema})
    section = Section('FAQ', verbose_name='Frequently asked questions')
    name = 'FAQ'
    required = False
    verbose_name = 'FAQ'
    help_text = _("Configure frequently asked questions and their answers to help users find information quickly.")
    default = {}

version: '3.8'

services:
  web:
    container_name: qatreh_web
    restart: unless-stopped
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers=4 --timeout 560
    volumes:
      - static_volume:/usr/src/app/static
    ports:
      - "8019:8000"
    env_file:
      - .env.prod
    depends_on:
      - postgres
    links:
      - postgres
    networks:
      - qatreh

  postgres:
    container_name: qatreh_db
    ports:
      - "5577:5432"
    restart: unless-stopped
    image: postgres:14.0
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    env_file:
      - .env.prod
    networks:
      - qatreh


  qatreh_redis:
    container_name: qatreh_redis
    image: redis:alpine
    env_file: .env.prod
    volumes:
      - redis_data:/data
    networks:
      - qatreh

  qatreh_celery:
    container_name: qatreh_celery
    build:
      context: .
      dockerfile: Dockerfile.prod
    env_file: .env.prod
    command: celery -A config worker -l info
    volumes:
      - .:/usr/src/app/
      - static_volume:/usr/src/app/static

    depends_on:
      - qatreh_redis
    networks:
      - qatreh


  qatreh_celery-beat:
    container_name: qatreh_celery_beat
    build:
      context: .
      dockerfile: Dockerfile.prod
    env_file: .env.prod
    command: celery -A config beat -l info
    volumes:
      - .:/usr/src/app/
    depends_on:
      - qatreh_redis
    networks:
      - qatreh


volumes:
  postgres_data:
  static_volume:
  redis_data:

networks:
  qatreh:
    driver: bridge

# 📊 فرمول جدید محاسبه صندوق قرعه‌کشی

## 🔄 تغییرات اساسی

### **فرمول قدیم:**
```
تعداد کل سهم‌ها = مبلغ کل وام ÷ مبلغ هر سهم
مبلغ هر سهم = مبلغ کل وام ÷ تعداد ماه قرعه‌کشی
```

### **فرمول جدید:**
```
تعداد کل سهم‌ها = تعداد ماه‌های قرعه‌کشی × تعداد قرعه‌کشی در ماه
مبلغ هر سهم = مبلغ کل وام ÷ تعداد کل سهم‌ها
قسط ماهانه عضو = مبلغ هر سهم × تعداد سهم درخواستی عضو
```

## 🧮 مثال‌های عملی

### **مثال 1: صندوق دوهفته‌ای**
```
تنظیمات:
- مبلغ کل وام: 12,000,000 تومان
- تعداد ماه: 12 ماه
- تعداد قرعه‌کشی در ماه: 2 (دوهفته‌ای)

محاسبات:
- تعداد کل سهم‌ها = 12 × 2 = 24 سهم
- مبلغ هر سهم = 12,000,000 ÷ 24 = 500,000 تومان
- عضو با 2 سهم: 500,000 × 2 = 1,000,000 تومان ماهانه
```

### **مثال 2: صندوق هفتگی**
```
تنظیمات:
- مبلغ کل وام: 24,000,000 تومان
- تعداد ماه: 12 ماه
- تعداد قرعه‌کشی در ماه: 4 (هفتگی)

محاسبات:
- تعداد کل سهم‌ها = 12 × 4 = 48 سهم
- مبلغ هر سهم = 24,000,000 ÷ 48 = 500,000 تومان
- عضو با 1 سهم: 500,000 × 1 = 500,000 تومان ماهانه
```

### **مثال 3: صندوق ماهانه**
```
تنظیمات:
- مبلغ کل وام: 6,000,000 تومان
- تعداد ماه: 12 ماه
- تعداد قرعه‌کشی در ماه: 1 (ماهانه)

محاسبات:
- تعداد کل سهم‌ها = 12 × 1 = 12 سهم
- مبلغ هر سهم = 6,000,000 ÷ 12 = 500,000 تومان
- عضو با 3 سهم: 500,000 × 3 = 1,500,000 تومان ماهانه
```

## 📈 مزایای فرمول جدید

### **1️⃣ انعطاف‌پذیری بیشتر**
- امکان تنظیم فرکانس قرعه‌کشی (هفتگی، دوهفته‌ای، ماهانه)
- سازگاری با انواع مختلف صندوق‌ها

### **2️⃣ منطق ریاضی صحیح**
- هر قرعه‌کشی = یک برنده = یک سهم
- تطابق کامل بین تعداد قرعه‌کشی‌ها و تعداد سهم‌ها

### **3️⃣ عدالت بیشتر**
- مبلغ کمتر هر سهم = دسترسی آسان‌تر برای اعضا
- امکان مشارکت با سرمایه کمتر

### **4️⃣ مدیریت ریسک بهتر**
- توزیع بهتر مبلغ کل بین سهم‌های بیشتر
- کاهش ریسک تمرکز سرمایه در تعداد کم سهم

## 🔧 پیاده‌سازی در کد

### **مدل Deposit:**
```python
@property
def total_unit_amount(self):
    if self.deposit_type == 'Poll':
        if self.lottery_month_count and self.lottery_per_month_count:
            return self.lottery_month_count * self.lottery_per_month_count
    return 0

def calculate_unit_amount(self):
    total_shares = self.lottery_month_count * self.lottery_per_month_count
    return self.total_debt_amount / total_shares
```

### **محاسبه قسط ماهانه:**
```python
# در RequestJoinDepositCreateSerializer
data['monthly_installment_amount'] = deposit.unit_amount * data['requested_unit_count']
```

## 📊 جدول مقایسه

| **مشخصه** | **فرمول قدیم** | **فرمول جدید** |
|------------|-----------------|-----------------|
| **منطق** | بر اساس تعداد ماه | بر اساس تعداد قرعه‌کشی |
| **انعطاف** | محدود به ماهانه | هفتگی، دوهفته‌ای، ماهانه |
| **مبلغ سهم** | بالاتر | کمتر و مناسب‌تر |
| **دسترسی** | محدود | آسان‌تر برای همه |

## ✅ تست و اعتبارسنجی

### **تست 1: صحت ریاضی**
```
12M تومان ÷ 24 سهم = 500K تومان
24 سهم × 500K تومان = 12M تومان ✅
```

### **تست 2: منطق قرعه‌کشی**
```
12 ماه × 2 قرعه/ماه = 24 قرعه‌کشی
24 قرعه‌کشی = 24 برنده = 24 سهم ✅
```

### **تست 3: عدالت مالی**
```
عضو A: 2 سهم → 1M تومان ماهانه
عضو B: 1 سهم → 500K تومان ماهانه
نسبت: 2:1 = منطقی ✅
```

## 🔄 مقایسه عملی فرمول‌ها

### **سناریو: صندوق 12 میلیونی، 12 ماهه**

#### **با فرمول قدیم (ماهانه):**
```
تعداد کل سهم‌ها = 12,000,000 ÷ 1,000,000 = 12 سهم
مبلغ هر سهم = 12,000,000 ÷ 12 = 1,000,000 تومان
قرعه‌کشی: 12 بار در 12 ماه (ماهانه)
```

#### **با فرمول جدید (دوهفته‌ای):**
```
تعداد کل سهم‌ها = 12 × 2 = 24 سهم
مبلغ هر سهم = 12,000,000 ÷ 24 = 500,000 تومان
قرعه‌کشی: 24 بار در 12 ماه (دوهفته‌ای)
```

### **مزایای فرمول جدید:**
1. **دسترسی آسان‌تر:** 500K به جای 1M تومان
2. **انعطاف بیشتر:** امکان قرعه‌کشی هفتگی، دوهفته‌ای
3. **مشارکت بیشتر:** اعضا با سرمایه کمتر می‌توانند شرکت کنند
4. **ریسک کمتر:** توزیع بهتر سرمایه بین سهم‌های بیشتر

## 📋 چک‌لیست پیاده‌سازی

- ✅ مدل Deposit: اضافه شدن فیلد lottery_per_month_count
- ✅ محاسبه total_unit_amount: فرمول جدید
- ✅ محاسبه unit_amount: فرمول جدید
- ✅ Validation: بررسی فیلدهای جدید
- ✅ Admin forms: validation و محاسبه خودکار
- ✅ Serializers: اضافه شدن فیلدهای جدید
- ✅ API Documentation: به‌روزرسانی مثال‌ها
- ✅ HTML Documentation: به‌روزرسانی فرمول‌ها
- ✅ Migration: اضافه شدن فیلد جدید به دیتابیس

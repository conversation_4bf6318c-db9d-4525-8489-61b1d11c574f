# پیاده‌سازی سیستم عضویت چندگانه در ریجن‌ها

## خلاصه تغییرات

این پروژه برای پشتیبانی از عضویت کاربران در چندین ریجن به‌روزرسانی شده است. قبلاً هر کاربر فقط می‌توانست عضو یک ریجن باشد، اما اکنون کاربران می‌توانند عضو چندین ریجن باشند و یکی از آن‌ها را به عنوان ریجن فعلی انتخاب کنند.

## تغییرات اصلی

### 1. تغییرات مدل UserRegion

**قبل:**
```python
class UserRegion(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='region_membership')
    region = models.ForeignKey(Region, on_delete=models.CASCADE, related_name='members')
    # سایر فیلدها...
```

**بعد:**
```python
class UserRegion(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='region_memberships')
    region = models.ForeignKey(Region, on_delete=models.CASCADE, related_name='members')
    is_current = models.BooleanField(default=False, help_text="Indicates if this is the user's current active region")
    # سایر فیلدها...
    
    class Meta:
        unique_together = ('user', 'region')  # جلوگیری از عضویت تکراری
```

**تغییرات کلیدی:**
- تبدیل `OneToOneField` به `ForeignKey` برای امکان عضویت در چندین ریجن
- اضافه کردن فیلد `is_current` برای مشخص کردن ریجن فعلی کاربر
- اضافه کردن `unique_together` برای جلوگیری از عضویت تکراری در یک ریجن
- تغییر `related_name` از `region_membership` به `region_memberships`

### 2. تغییرات User Model

**متدهای جدید اضافه شده:**
```python
@property
def user_region(self):
    """Get the user's current active region"""
    current_membership = self.region_memberships.filter(is_current=True).first()
    return current_membership.region if current_membership else None

@property
def current_region_membership(self):
    """Get the user's current region membership object"""
    return self.region_memberships.filter(is_current=True).first()

def get_all_regions(self):
    """Get all regions the user is a member of"""
    return [membership.region for membership in self.region_memberships.all()]

def is_member_of_region(self, region):
    """Check if user is a member of a specific region"""
    return self.region_memberships.filter(region=region).exists()

def set_current_region(self, region):
    """Set a region as the user's current active region"""
    if not self.is_member_of_region(region):
        return False
    
    # Set all regions as non-current
    self.region_memberships.update(is_current=False)
    
    # Set the specified region as current
    membership = self.region_memberships.get(region=region)
    membership.is_current = True
    membership.save()
    return True
```

### 3. تغییرات API ها

#### RegionListAPIView
**قبل:** نمایش همه ریجن‌ها
**بعد:** نمایش فقط ریجن‌هایی که کاربر عضو آن‌هاست

```python
def get_queryset(self):
    """Get only regions that the current user is a member of"""
    user = self.request.user
    user_region_ids = user.region_memberships.values_list('region_id', flat=True)
    return Region.objects.filter(id__in=user_region_ids).order_by('name')
```

#### ChangeUserRegionAPIView
**قبل:** تغییر ریجن کاربر (ایجاد یا بروزرسانی)
**بعد:** تغییر ریجن فعلی از بین ریجن‌هایی که کاربر عضو آن‌هاست

```python
# Check if user is a member of this region
try:
    user_region = UserRegion.objects.get(user=user, region=region)
except UserRegion.DoesNotExist:
    return Response({
        'status': 'error',
        'message': 'You are not a member of this region'
    }, status=status.HTTP_400_BAD_REQUEST)

# Use the user model method to set current region
success = user.set_current_region(region)
```

#### RegisterWithInvitationAPIView
**قبل:** اگر کاربر قبلاً عضو ریجنی بود، عضویت جدید ایجاد نمی‌کرد
**بعد:** امکان عضویت در ریجن‌های جدید بدون حذف عضویت‌های قبلی

```python
# Check if user is already a member of this specific region
existing_membership = UserRegion.objects.filter(user=user, region=region).first()
if existing_membership:
    # User is already a member of this region
    return Response({"message": "User is already a member of this region."})

# Check if this is the user's first region membership
is_first_region = not user.region_memberships.exists()

# Create a new UserRegion for this user
user_region = UserRegion.objects.create(
    user=user,
    region=region,
    invited_by=invited_by,
    is_active=False,
    is_current=is_first_region  # Set as current if it's the first region
)
```

### 4. Migration برای حفظ داده‌های موجود

Migration سفارشی ایجاد شده که:
- فیلد `is_current` را اضافه می‌کند
- `unique_together` constraint اضافه می‌کند
- رابطه `OneToOneField` را به `ForeignKey` تبدیل می‌کند
- همه UserRegion های موجود را به عنوان `is_current=True` تنظیم می‌کند

### 5. تغییرات Admin Panel

- بروزرسانی فیلترها از `region_membership__region` به `region_memberships__region`
- بروزرسانی متدهای نمایش اطلاعات ریجن کاربر
- اضافه کردن `distinct()` به queryset ها برای جلوگیری از تکرار

## سناریو جدید

### 1. ثبت‌نام کاربر جدید
- کاربر کد دعوت را وارد می‌کند
- اگر کد معتبر باشد، کاربر به ریجن مربوطه اضافه می‌شود
- اگر این اولین ریجن کاربر باشد، به عنوان ریجن فعلی تنظیم می‌شود

### 2. عضویت در ریجن جدید
- کاربر می‌تواند کد دعوت ریجن جدید را وارد کند
- اگر قبلاً عضو آن ریجن نباشد، عضویت جدید ایجاد می‌شود
- عضویت‌های قبلی حفظ می‌مانند

### 3. تغییر ریجن فعلی
- کاربر می‌تواند از بین ریجن‌هایی که عضو آن‌هاست، یکی را به عنوان ریجن فعلی انتخاب کند
- فقط یک ریجن می‌تواند در هر زمان فعلی باشد

### 4. مشاهده ریجن‌ها
- در RegionListAPIView فقط ریجن‌هایی که کاربر عضو آن‌هاست نمایش داده می‌شوند
- ریجن فعلی با فیلد `is_current_user_region=true` مشخص می‌شود

## API Endpoints

### GET /api/regions/
**توضیح:** دریافت لیست ریجن‌های کاربر
**پاسخ:**
```json
[
    {
        "id": 1,
        "name": "ریجن تهران",
        "members_count": 25,
        "is_current_user_region": true,
        "created_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": 2,
        "name": "ریجن اصفهان", 
        "members_count": 15,
        "is_current_user_region": false,
        "created_at": "2024-01-02T00:00:00Z"
    }
]
```

### POST /api/regions/change/
**توضیح:** تغییر ریجن فعلی کاربر
**درخواست:**
```json
{
    "region_id": 2
}
```
**پاسخ:**
```json
{
    "status": "success",
    "message": "Current region changed successfully"
}
```

### POST /api/account/register-with-invitation/
**توضیح:** عضویت در ریجن جدید با کد دعوت
**درخواست:**
```json
{
    "invitation_code": "HAMJEEB-ABC1234"
}
```
**پاسخ:**
```json
{
    "message": "User added to the region successfully.",
    "is_active": true,
    "is_member": true,
    "invited_by_fullname": "علی احمدی"
}
```

## نکات مهم

1. **سازگاری با نسخه قبل:** همه کاربران موجود به طور خودکار به ریجن فعلی تبدیل شده‌اند
2. **جلوگیری از تکرار:** کاربر نمی‌تواند دوبار عضو یک ریجن شود
3. **ریجن فعلی:** همیشه فقط یک ریجن می‌تواند فعلی باشد
4. **حفظ عملکرد:** همه API های قبلی همچنان کار می‌کنند اما با منطق جدید

## تست‌ها

برای تست عملکرد صحیح:
1. کاربر جدید ایجاد کنید و با کد دعوت به ریجن اضافه کنید
2. کاربر را با کد دعوت دیگری به ریجن دوم اضافه کنید
3. ریجن فعلی را تغییر دهید
4. لیست ریجن‌ها را مشاهده کنید
5. تلاش کنید ریجنی را فعلی کنید که عضو آن نیستید (باید خطا دهد)

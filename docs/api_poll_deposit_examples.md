# 📚 مستندات API صندوق قرعه‌کشی - فرمول جدید

## 🚀 ایجاد صندوق قرعه‌کشی

### **Endpoint:**
```
POST /api/requests/deposit/create/poll-deposit/
```

### **Request Body:**
```json
{
    "title": "صندوق قرعه‌کشی دوهفته‌ای",
    "description": "صندوق 12 ماهه با قرعه‌کشی هر دو هفته یکبار",
    "total_debt_amount": 12000000.00,
    "lottery_month_count": 12,
    "lottery_per_month_count": 2,
    "payment_cycle": 15,
    "max_unit_per_request": 5,
    "max_members_count": 30,
    "initial_lottery_date": "2024-01-15",
    "rules": [
        {
            "subject": "قانون پرداخت",
            "description": "پرداخت تا 15 هر ماه الزامی است"
        },
        {
            "subject": "قانون قرعه‌کشی", 
            "description": "قرعه‌کشی هر دو هفته یکبار انجام می‌شود"
        }
    ]
}
```

### **Response:**
```json
{
    "id": 1,
    "title": "صندوق قرعه‌کشی دوهفته‌ای",
    "description": "صندوق 12 ماهه با قرعه‌کشی هر دو هفته یکبار",
    "deposit_type": "Poll",
    "total_debt_amount": 12000000.00,
    "lottery_month_count": 12,
    "lottery_per_month_count": 2,
    "unit_amount": 500000.00,
    "total_shares": 24,
    "status": "pending",
    "created_at": "2024-01-01T10:00:00Z"
}
```

## 📊 محاسبات خودکار

### **فرمول‌های اعمال شده:**
```javascript
// محاسبه تعداد کل سهم‌ها
total_shares = lottery_month_count × lottery_per_month_count
total_shares = 12 × 2 = 24

// محاسبه مبلغ هر سهم
unit_amount = total_debt_amount ÷ total_shares  
unit_amount = 12,000,000 ÷ 24 = 500,000
```

## 👥 درخواست عضویت

### **Endpoint:**
```
POST /api/deposits/{deposit_id}/join/
```

### **Request Body:**
```json
{
    "requested_unit_count": 2
}
```

### **Response:**
```json
{
    "id": 1,
    "user": 123,
    "deposit": 456,
    "requested_unit_count": 2,
    "monthly_installment_amount": 1000000.00,
    "status": "pending",
    "created_at": "2024-01-01T10:00:00Z"
}
```

### **محاسبه قسط ماهانه:**
```javascript
monthly_installment_amount = unit_amount × requested_unit_count
monthly_installment_amount = 500,000 × 2 = 1,000,000
```

## 🎲 اطلاعات قرعه‌کشی

### **Endpoint:**
```
GET /api/deposits/{deposit_id}/lottery/info/
```

### **Response:**
```json
{
    "due_date_number": 1,
    "due_date": "2024-01-15",
    "is_lottery_completed": false,
    "can_perform_lottery": true,
    "lottery_per_month_count": 2,
    "current_month_lotteries_count": 0,
    "winners": [],
    "members": [
        {
            "fullname": "علی احمدی",
            "share_number": 1
        },
        {
            "fullname": "علی احمدی", 
            "share_number": 2
        },
        {
            "fullname": "مریم محمدی",
            "share_number": 1
        }
    ]
}
```

## 📈 اطلاعات کامل صندوق

### **Endpoint:**
```
GET /api/deposits/{deposit_id}/
```

### **Response:**
```json
{
    "id": 1,
    "deposit_type": "Poll",
    "title": "صندوق قرعه‌کشی دوهفته‌ای",
    "description": "صندوق 12 ماهه با قرعه‌کشی هر دو هفته یکبار",
    "total_debt_amount": 12000000.00,
    "lottery_month_count": 12,
    "lottery_per_month_count": 2,
    "unit_amount": 500000.00,
    "total_shares": 24,
    "payment_cycle": 15,
    "max_unit_per_request": 5,
    "max_members_count": 30,
    "members_count": 8,
    "balance": 4000000.00,
    "completed_lotteries_count": 3,
    "btn_disable_lottery": false,
    "is_active": true,
    "created": "2024-01-01T10:00:00Z"
}
```

## 🔄 مقایسه فرمول‌ها

### **مثال: صندوق 12 میلیونی، 12 ماهه**

| **مشخصه** | **فرمول قدیم** | **فرمول جدید (دوهفته‌ای)** |
|------------|-----------------|---------------------------|
| **تعداد کل سهم‌ها** | 12 سهم | 24 سهم |
| **مبلغ هر سهم** | 1,000,000 تومان | 500,000 تومان |
| **قرعه‌کشی** | 12 بار (ماهانه) | 24 بار (دوهفته‌ای) |
| **حداقل مشارکت** | 1,000,000 تومان | 500,000 تومان |
| **انعطاف‌پذیری** | محدود | بالا |

### **مزایای فرمول جدید:**
1. **دسترسی آسان‌تر:** مبلغ کمتر برای شروع
2. **قرعه‌کشی بیشتر:** هیجان و تعامل بیشتر
3. **ریسک کمتر:** توزیع بهتر سرمایه
4. **انعطاف‌پذیری:** امکان تنظیم فرکانس قرعه‌کشی

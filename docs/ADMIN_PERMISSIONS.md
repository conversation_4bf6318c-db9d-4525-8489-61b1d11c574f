# Admin Permission System Documentation

## Overview

This document describes the comprehensive region-based admin permission system implemented for the Django admin panel. The system allows region owners to access the admin panel while restricting their access to only data belonging to their regions.

## Architecture

### Core Components

1. **RegionOwnerPermissionMixin** - Base permission logic for region-based access
2. **SuperAdminOnlyMixin** - Restricts access to superusers only
3. **RegionFilteredAdmin** - Admin class for region-related models
4. **SuperAdminOnlyAdmin** - Admin class for superuser-only models

### Permission Levels

#### Superusers
- Full access to all models and data
- Can manage regions, users, and global settings
- No restrictions applied

#### Region Owners
- Access to admin panel for region-related models only
- Can only see and manage data from their owned regions
- Cannot access superadmin-only models like User management or Region creation

#### Regular Users
- No access to admin panel
- All permissions denied

## Implementation Details

### Base Admin Classes

#### RegionFilteredAdmin
```python
class RegionFilteredAdmin(RegionOwnerPermissionMixin, ModelAdmin):
    """
    Base admin class for models that should be filtered by region.
    Automatically filters querysets and form choices based on user's owned regions.
    """
```

Features:
- Automatic queryset filtering based on region relationships
- Foreign key and many-to-many field filtering
- Comprehensive permission checks
- Support for complex model relationships

#### SuperAdminOnlyAdmin
```python
class SuperAdminOnlyAdmin(SuperAdminOnlyMixin, ModelAdmin):
    """
    Base admin class for models that should only be accessible to superusers.
    """
```

Features:
- Complete access restriction for non-superusers
- Hidden from region owners entirely
- Used for sensitive models like User and Region management

### Model Classifications

#### Region-Related Models (RegionFilteredAdmin)
- **Deposit** - Direct region relationship
- **DepositMembership** - Through deposit
- **DepositDueDate** - Through deposit
- **Ticket** - Direct region relationship
- **TicketMessage** - Through ticket
- **Loan** - Through deposit
- **Payment** - Through deposit
- **Transaction** - Through deposit
- **VotingPoll** - Through deposit
- **UserRegion** - Direct region relationship (own region only)

#### Superadmin-Only Models (SuperAdminOnlyAdmin)
- **User** - Global user management
- **Region** - Region creation and management
- **ReportSubject** - Global configuration
- **AppVersion** - System configuration
- **GlobalPreferenceModel** - System preferences

### Permission Logic

#### Module Access
```python
def has_module_permission(self, request):
    """Allow region owners to see the module"""
    if request.user.is_superuser:
        return True
    return hasattr(request.user, 'owned_regions') and request.user.owned_regions.exists()
```

#### Object Access
```python
def _user_can_access_object(self, user, obj):
    """Check if user can access the given object based on region ownership"""
    user_regions = user.owned_regions.all()
    
    # Direct region relationship
    if hasattr(obj, 'region'):
        return obj.region in user_regions
    
    # Through deposit relationship
    if hasattr(obj, 'deposit') and hasattr(obj.deposit, 'region'):
        return obj.deposit.region in user_regions
    
    # Additional relationship checks...
```

#### Queryset Filtering
```python
def _filter_queryset_by_regions(self, qs, user_regions):
    """Filter queryset based on region relationships"""
    model = qs.model
    
    # Direct region relationship
    if hasattr(model, 'region'):
        return qs.filter(region__in=user_regions)
    
    # Through deposit relationship
    if hasattr(model, 'deposit'):
        return qs.filter(deposit__region__in=user_regions)
    
    # Additional filtering logic...
```

## Security Features

### Data Isolation
- Region owners can only access data from their owned regions
- Querysets are automatically filtered at the database level
- Foreign key choices are restricted to region-appropriate options

### Permission Validation
- Comprehensive permission checks for view, add, change, and delete operations
- Object-level permission validation
- Module-level access control

### Form Field Filtering
- Foreign key fields show only region-appropriate choices
- Many-to-many fields are filtered by region
- User selections are limited to region members

## Usage Examples

### Creating a Region-Filtered Admin
```python
from utils.admin import RegionFilteredAdmin

class MyModelAdmin(RegionFilteredAdmin):
    list_display = ('name', 'region', 'created')
    list_filter = ('region',)
    
    # Additional customization...
```

### Creating a Superadmin-Only Admin
```python
from utils.admin import SuperAdminOnlyAdmin

class SensitiveModelAdmin(SuperAdminOnlyAdmin):
    list_display = ('name', 'created')
    
    # Only superusers can access this
```

## Testing

### Validation Script
Run the validation script to check implementation:
```bash
python check_admin_implementation.py
```

### Manual Testing
1. Create a region owner user
2. Log into admin panel as region owner
3. Verify access to region-related models only
4. Verify data filtering works correctly
5. Test foreign key field filtering

## Maintenance

### Adding New Models

#### For Region-Related Models:
1. Inherit from `RegionFilteredAdmin`
2. Ensure model has region relationship (direct or indirect)
3. Register with admin site

#### For Superadmin-Only Models:
1. Inherit from `SuperAdminOnlyAdmin`
2. Register with admin site

### Extending Relationships
If adding new relationship patterns, update:
1. `_user_can_access_object()` method
2. `_filter_queryset_by_regions()` method
3. `formfield_for_foreignkey()` method

## Troubleshooting

### Common Issues

1. **Region owner can't see any data**
   - Check if user has `owned_regions` relationship
   - Verify region ownership is properly set

2. **Foreign key fields show all options**
   - Ensure `formfield_for_foreignkey()` handles the field name
   - Check relationship mapping in filtering logic

3. **Permission denied errors**
   - Verify admin class inheritance
   - Check user's region ownership status

### Debug Tips
- Use Django shell to test permission methods directly
- Check queryset filtering with sample data
- Verify admin class registration

## Security Considerations

1. **Data Leakage Prevention**
   - All querysets are filtered at database level
   - No client-side filtering dependencies
   - Comprehensive permission validation

2. **Privilege Escalation Prevention**
   - Clear separation between superuser and region owner permissions
   - No way for region owners to access superadmin-only models
   - Object-level permission checks

3. **Audit Trail**
   - All admin actions are logged by Django's built-in logging
   - User actions are traceable through admin logs
   - Permission checks are comprehensive and logged

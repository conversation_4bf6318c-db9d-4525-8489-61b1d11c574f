# 📋 تغییرات فرمول محاسبه صندوق قرعه‌کشی

## 🗓️ تاریخ: 2024-01-24

## 🔄 خلاصه تغییرات

### **تغییر اساسی فرمول محاسبه:**

#### **قبل از تغییر:**
```
تعداد کل سهم‌ها = مبلغ کل وام ÷ مبلغ هر سهم
مبلغ هر سهم = مبلغ کل وام ÷ تعداد ماه قرعه‌کشی
```

#### **بعد از تغییر:**
```
تعداد کل سهم‌ها = تعداد ماه‌های قرعه‌کشی × تعداد قرعه‌کشی در ماه
مبلغ هر سهم = مبلغ کل وام ÷ تعداد کل سهم‌ها
```

## 🆕 فیلدهای جدید

### **مدل Deposit:**
- ✅ `lottery_per_month_count`: تعداد قرعه‌کشی در ماه (پیش‌فرض: 1)

### **Serializers:**
- ✅ `total_shares`: نمایش تعداد کل سهم‌ها محاسبه شده

## 📊 مثال‌های عملی

### **مثال 1: صندوق دوهفته‌ای**
```
ورودی:
- مبلغ کل: 12,000,000 تومان
- تعداد ماه: 12
- قرعه‌کشی در ماه: 2

محاسبه:
- تعداد کل سهم‌ها: 12 × 2 = 24
- مبلغ هر سهم: 12,000,000 ÷ 24 = 500,000 تومان
```

### **مثال 2: صندوق هفتگی**
```
ورودی:
- مبلغ کل: 24,000,000 تومان
- تعداد ماه: 12
- قرعه‌کشی در ماه: 4

محاسبه:
- تعداد کل سهم‌ها: 12 × 4 = 48
- مبلغ هر سهم: 24,000,000 ÷ 48 = 500,000 تومان
```

## 🔧 تغییرات فنی

### **فایل‌های تغییر یافته:**

#### **1. مدل‌ها:**
- `apps/deposit/models/deposit.py`
  - اضافه شدن فیلد `lottery_per_month_count`
  - اصلاح متد `calculate_unit_amount()`
  - اصلاح property `total_unit_amount`
  - اضافه شدن متد `get_total_shares_info()`

#### **2. Serializers:**
- `apps/deposit/serializers/deposit.py`
  - اضافه شدن فیلد `total_shares`
  - اضافه شدن متد `get_total_shares()`

- `apps/request/serializers/deposit_create.py`
  - اضافه شدن فیلد `lottery_per_month_count`

#### **3. Admin:**
- `apps/deposit/admin/deposit.py`
  - اضافه شدن فیلد به admin panel

- `apps/deposit/admin/forms.py`
  - اصلاح validation برای فرمول جدید

#### **4. مستندات:**
- `apps/request/doc.py`
  - به‌روزرسانی API documentation
  - اصلاح مثال‌های عملی

- `templates/payment/business_logic_documentation.html`
  - به‌روزرسانی فرمول‌ها و مثال‌ها

#### **5. Migration:**
- `apps/deposit/migrations/0014_add_lottery_per_month_count.py`
  - اضافه شدن فیلد جدید به دیتابیس

## 🎯 مزایای تغییر

### **1. انعطاف‌پذیری بیشتر:**
- امکان تنظیم فرکانس قرعه‌کشی (هفتگی، دوهفته‌ای، ماهانه)
- سازگاری با انواع مختلف صندوق‌ها

### **2. منطق ریاضی صحیح:**
- هر قرعه‌کشی = یک برنده = یک سهم
- تطابق کامل بین تعداد قرعه‌کشی‌ها و تعداد سهم‌ها

### **3. عدالت بیشتر:**
- مبلغ کمتر هر سهم = دسترسی آسان‌تر برای اعضا
- امکان مشارکت با سرمایه کمتر

### **4. مدیریت ریسک بهتر:**
- توزیع بهتر مبلغ کل بین سهم‌های بیشتر
- کاهش ریسک تمرکز سرمایه

## ⚠️ نکات مهم

### **1. سازگاری با گذشته:**
- صندوق‌های موجود با فرمول قدیم همچنان کار می‌کنند
- فیلد `lottery_per_month_count` برای صندوق‌های قدیمی null است

### **2. Validation جدید:**
- بررسی حداقل مبلغ هر سهم (1000 تومان)
- اعتبارسنجی منطقی بودن تعداد قرعه‌کشی در ماه

### **3. محاسبه خودکار:**
- `unit_amount` به صورت خودکار محاسبه می‌شود
- در صورت وارد کردن دستی، صحت آن بررسی می‌شود

## 🧪 تست‌ها

### **تست 1: صحت ریاضی**
```python
assert 12_000_000 / 24 == 500_000  # ✅
assert 24 * 500_000 == 12_000_000  # ✅
```

### **تست 2: منطق قرعه‌کشی**
```python
assert 12 * 2 == 24  # 12 ماه × 2 قرعه = 24 قرعه‌کشی ✅
```

### **تست 3: عدالت مالی**
```python
# عضو با 2 سهم: 2 × 500,000 = 1,000,000
# عضو با 1 سهم: 1 × 500,000 = 500,000
# نسبت: 2:1 = منطقی ✅
```

## 📚 مستندات اضافی

- `docs/poll_deposit_new_formula.md`: توضیح کامل فرمول جدید
- `docs/api_poll_deposit_examples.md`: مثال‌های API با فرمول جدید

## ✅ چک‌لیست تکمیل

- [x] اضافه شدن فیلد `lottery_per_month_count`
- [x] اصلاح فرمول محاسبه `unit_amount`
- [x] اصلاح فرمول محاسبه `total_unit_amount`
- [x] اضافه شدن validation جدید
- [x] به‌روزرسانی admin forms
- [x] اضافه شدن فیلد به serializers
- [x] به‌روزرسانی API documentation
- [x] به‌روزرسانی HTML documentation
- [x] ایجاد migration
- [x] تست عملکرد فرمول‌ها
- [x] ایجاد مستندات جامع

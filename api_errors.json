[{"api_help": "ثبت نام کاربران", "api_url": "/api/account/register/", "api_message_error": "This phone number is already registered.", "api_error": "شماره تماس از قبل ثبت شده", "api_error_status": 400}, {"api_help": "ثبت نام کاربران", "api_url": "/api/account/register/", "api_message_error": "Passwords do not match.", "api_error": "رمز عبور و تکرار آن مطابقت ندارند", "api_error_status": 400}, {"api_help": "ثبت نام کاربران", "api_url": "/api/account/register/", "api_message_error": "Password must be at least 8 characters long.", "api_error": "رمز عبور باید حداقل ۸ کاراکتر باشد", "api_error_status": 400}, {"api_help": "ت<PERSON><PERSON><PERSON><PERSON> کد ارسالی", "api_url": "/api/account/verify/", "api_message_error": "code notfound", "api_error": "کد تایید نامعتبر است", "api_error_status": 400}, {"api_help": "ت<PERSON><PERSON><PERSON><PERSON> کد ارسالی", "api_url": "/api/account/verify/", "api_message_error": "The verification code has expired.", "api_error": "کد تایید منقضی شده است", "api_error_status": 410}, {"api_help": "ورود کاربر", "api_url": "/api/account/login/", "api_message_error": "user notfound", "api_error": "کاربر یافت نشد", "api_error_status": 404}, {"api_help": "ورود کاربر", "api_url": "/api/account/login/", "api_message_error": "Service temporarily unavailable", "api_error": "سرویس موقتاً در دسترس نیست", "api_error_status": 500}, {"api_help": "بازیابی رمز عبور", "api_url": "/api/account/recover/", "api_message_error": "user notfound", "api_error": "کاربر یافت نشد", "api_error_status": 404}, {"api_help": "تنظیم مجدد رمز عبور", "api_url": "/api/account/reset/", "api_message_error": "Passwords do not match.", "api_error": "رمز عبور و تکرار آن مطابقت ندارند", "api_error_status": 400}, {"api_help": "تنظیم مجدد رمز عبور", "api_url": "/api/account/reset/", "api_message_error": "Password must be at least 8 characters long.", "api_error": "رمز عبور باید حداقل ۸ کاراکتر باشد", "api_error_status": 400}, {"api_help": "بروزرسانی پروفایل کاربر", "api_url": "/api/account/profile/update/", "api_message_error": "This email is already registered.", "api_error": "این ایمیل قبلاً ثبت شده است", "api_error_status": 400}, {"api_help": "بروزرسانی پروفایل کاربر", "api_url": "/api/account/profile/update/", "api_message_error": "User does not have any region membership.", "api_error": "کاربر عضویت منطقه‌ای ندارد", "api_error_status": 400}, {"api_help": "ثبت نام با کد دعوت", "api_url": "/api/account/invitaion/", "api_message_error": "Invitation code is required.", "api_error": "کد دعوت الزامی است", "api_error_status": 400}, {"api_help": "ثبت نام با کد دعوت", "api_url": "/api/account/invitaion/", "api_message_error": "Invalid invitation code.", "api_error": "کد دعوت نامعتبر است", "api_error_status": 400}, {"api_help": "ایجاد صندوق قرعه‌کشی", "api_url": "/api/requests/create/poll/", "api_message_error": "You do not have permission to make this request because your membership link is invalid.", "api_error": "شما مجوز ایجاد درخواست را ندارید زیرا لینک عضویت شما نامعتبر است", "api_error_status": 400}, {"api_help": "ایجاد صندوق قرعه‌کشی", "api_url": "/api/requests/create/poll/", "api_message_error": "Rules must be a list of dictionaries.", "api_error": "قوانین باید لیستی از دیکشنری‌ها باشد", "api_error_status": 400}, {"api_help": "ایجاد صندوق پس‌انداز", "api_url": "/api/requests/create/saving/", "api_message_error": "You do not have permission to make this request because your membership link is invalid.", "api_error": "شما مجوز ایجاد درخواست را ندارید زیرا لینک عضویت شما نامعتبر است", "api_error_status": 400}, {"api_help": "ایجاد صندوق پس‌انداز", "api_url": "/api/requests/create/saving/", "api_message_error": "Rules must be a list of dictionaries.", "api_error": "قوانین باید لیستی از دیکشنری‌ها باشد", "api_error_status": 400}, {"api_help": "ایجاد صندوق گزارش‌دهی", "api_url": "/api/requests/create/reporting/", "api_message_error": "You do not have permission to make this request because your membership link is invalid.", "api_error": "شما مجوز ایجاد درخواست را ندارید زیرا لینک عضویت شما نامعتبر است", "api_error_status": 400}, {"api_help": "ایجاد صندوق گزارش‌دهی", "api_url": "/api/requests/create/reporting/", "api_message_error": "Rules must be a list of dictionaries.", "api_error": "قوانین باید لیستی از دیکشنری‌ها باشد", "api_error_status": 400}, {"api_help": "درخواست عضویت در صندوق", "api_url": "/api/requests/join/<int:deposit_id>/", "api_message_error": "You have already joined this deposit.", "api_error": "شما قبلاً در این صندوق عضو شده‌اید", "api_error_status": 400}, {"api_help": "درخواست عضویت در صندوق", "api_url": "/api/requests/join/<int:deposit_id>/", "api_message_error": "You have already requested to join this deposit.", "api_error": "شما قبلاً درخواست عضویت در این صندوق را داده‌اید", "api_error_status": 400}, {"api_help": "بروزرسانی وضعیت درخواست عضویت", "api_url": "/api/requests/update/<int:id>/", "api_message_error": "Status must be either 'approved' or 'rejected'.", "api_error": "وضعیت باید 'تایید شده' یا 'رد شده' باشد", "api_error_status": 400}, {"api_help": "بروزرسانی وضعیت درخواست عضویت", "api_url": "/api/requests/update/<int:id>/", "api_message_error": "Rejection reason is required when status is 'rejected'.", "api_error": "دلیل رد کردن هنگام رد درخواست الزامی است", "api_error_status": 400}, {"api_help": "ایجاد درخواست وام", "api_url": "/api/requests/loan/create/", "api_message_error": "Loan amount must be greater than zero.", "api_error": "مبلغ وام باید بیشتر از صفر باشد", "api_error_status": 400}, {"api_help": "ایجاد درخواست وام", "api_url": "/api/requests/loan/create/", "api_message_error": "Installment count must be greater than zero.", "api_error": "تعداد اقساط باید بیشتر از صفر باشد", "api_error_status": 400}, {"api_help": "بروزرسانی وضعیت درخواست وام", "api_url": "/api/requests/loan/update/<int:id>/", "api_message_error": "Status must be either 'approved' or 'rejected'.", "api_error": "وضعیت باید 'تایید شده' یا 'رد شده' باشد", "api_error_status": 400}, {"api_help": "بروزرسانی وضعیت درخواست وام", "api_url": "/api/requests/loan/update/<int:id>/", "api_message_error": "Rejection reason is required when status is 'rejected'.", "api_error": "دلیل رد کردن هنگام رد درخواست الزامی است", "api_error_status": 400}, {"api_help": "ایجاد درخواست برداشت", "api_url": "/api/requests/withdrawal/create/", "api_message_error": "IBAN must be 26 characters long and start with 'IR'.", "api_error": "شماره شبا باید ۲۶ کاراکتر باشد و با 'IR' شروع شود", "api_error_status": 400}, {"api_help": "ایجاد درخواست برداشت", "api_url": "/api/requests/withdrawal/create/", "api_message_error": "You are not an active member of this deposit.", "api_error": "شما عضو فعال این صندوق نیستید", "api_error_status": 400}, {"api_help": "ایجاد درخواست برداشت", "api_url": "/api/requests/withdrawal/create/", "api_message_error": "Only deposit admins or owners can request withdrawals.", "api_error": "فقط مدیران یا مالکان صندوق می‌توانند درخواست برداشت دهند", "api_error_status": 400}, {"api_help": "تنظیم نقش عضو صندوق", "api_url": "/api/deposits/<int:deposit_id>/members/<int:membership_id>/set-role/", "api_message_error": "Invalid role.", "api_error": "نقش نامعتبر است", "api_error_status": 400}, {"api_help": "بروزرسانی رسانه صندوق", "api_url": "/api/deposits/<int:deposit_id>/media/<int:deposit_media_id>/update/", "api_message_error": "file does not exist, upload again", "api_error": "فایل وجود ندارد، مجدداً آپلود کنید", "api_error_status": 400}, {"api_help": "بروزرسانی رسانه صندوق", "api_url": "/api/deposits/<int:deposit_id>/media/<int:deposit_media_id>/update/", "api_message_error": "Error updating the deposit media", "api_error": "خطا در بروزرسانی رسانه صندوق", "api_error_status": 400}, {"api_help": "ایجاد رای‌گیری", "api_url": "/api/voting/create/", "api_message_error": "You must be an owner or admin of the deposit to create a voting poll.", "api_error": "برای ایجاد رای‌گیری باید مالک یا مدیر صندوق باشید", "api_error_status": 403}, {"api_help": "ایجاد رای‌گیری", "api_url": "/api/voting/create/", "api_message_error": "Voting polls can only be created for Reporting deposits.", "api_error": "رای‌گیری فقط برای صندوق‌های گزارش‌دهی قابل ایجاد است", "api_error_status": 400}, {"api_help": "شرکت در رای‌گیری", "api_url": "/api/voting/join/", "api_message_error": "You must be a member of the deposit to vote.", "api_error": "برای رای دادن باید عضو صندوق باشید", "api_error_status": 403}, {"api_help": "شرکت در رای‌گیری", "api_url": "/api/voting/join/", "api_message_error": "You have already voted in this poll.", "api_error": "شما قبلاً در این رای‌گیری شرکت کرده‌اید", "api_error_status": 400}, {"api_help": "ایجاد تیکت", "api_url": "/api/tickets/create/", "api_message_error": "You are not an active member of this deposit.", "api_error": "شما عضو فعال این صندوق نیستید", "api_error_status": 400}, {"api_help": "ایجاد پیام تیکت", "api_url": "/api/tickets/<int:ticket_id>/messages/create/", "api_message_error": "You do not have permission to send messages to this ticket.", "api_error": "شما مجوز ارسال پیام به این تیکت را ندارید", "api_error_status": 403}, {"api_help": "ایجاد گزارش مشکل", "api_url": "/api/issues/create/", "api_message_error": "Deposit with the given ID does not exist.", "api_error": "صندوق با شناسه داده شده وجود ندارد", "api_error_status": 400}, {"api_help": "ایجاد پرداخت", "api_url": "/api/payments/create/", "api_message_error": "Ensure this value is greater than or equal to 0.01.", "api_error": "مب<PERSON><PERSON> باید حداقل ۰.۰۱ باشد", "api_error_status": 400}, {"api_help": "ایجاد پرداخت", "api_url": "/api/payments/create/", "api_message_error": "Loan installment amount must be greater than zero when includes_loan_installment is True.", "api_error": "مب<PERSON>غ قسط وام باید بیشتر از صفر باشد وقتی شامل قسط وام است", "api_error_status": 400}, {"api_help": "ایجاد پرداخت", "api_url": "/api/payments/create/", "api_message_error": "You are not an active member of this deposit.", "api_error": "شما عضو فعال این صندوق نیستید", "api_error_status": 400}, {"api_help": "ایجاد پرداخت", "api_url": "/api/payments/create/", "api_message_error": "Invalid or missing security token", "api_error": "توکن امنیتی نامعتبر یا موجود نیست", "api_error_status": 403}, {"api_help": "هدا<PERSON>ت پرداخت", "api_url": "/api/payments/redirect/", "api_message_error": "Payment not found or does not belong to you", "api_error": "پرداخت یافت نشد یا متعلق به شما نیست", "api_error_status": 404}, {"api_help": "ایجاد قرعه‌کشی", "api_url": "/api/deposit/<int:deposit_id>/lottery/create/", "api_message_error": "You must be an owner or admin of the deposit to create a lottery.", "api_error": "برای ایجاد قرعه‌کشی باید مالک یا مدیر صندوق باشید", "api_error_status": 403}, {"api_help": "ایجاد قرعه‌کشی", "api_url": "/api/deposit/<int:deposit_id>/lottery/create/", "api_message_error": "No due dates found for this deposit", "api_error": "هیچ تاریخ سررسیدی برای این صندوق یافت نشد", "api_error_status": 400}, {"api_help": "حذف برنده قرعه‌کشی", "api_url": "/api/deposit/<int:deposit_id>/lottery/<int:winner_id>/delete/", "api_message_error": "You must be an owner or admin of the deposit to delete a lottery winner.", "api_error": "برای حذف برنده قرعه‌کشی باید مالک یا مدیر صندوق باشید", "api_error_status": 403}, {"api_help": "پایان قرعه‌کشی", "api_url": "/api/deposit/<int:deposit_id>/lottery/end/", "api_message_error": "You must be an owner or admin of the deposit to end a lottery.", "api_error": "برای پایان دادن به قرعه‌کشی باید مالک یا مدیر صندوق باشید", "api_error_status": 403}, {"api_help": "خطای عمومی سرور", "api_url": "همه API ها", "api_message_error": "An error occurred while processing the request.", "api_error": "خطایی در پردازش درخواست رخ داد", "api_error_status": 400}, {"api_help": "خطای عمومی سرور", "api_url": "همه API ها", "api_message_error": "Service temporarily unavailable", "api_error": "سرویس موقتاً در دسترس نیست", "api_error_status": 500}, {"api_help": "خطای عمومی سرور", "api_url": "همه API ها", "api_message_error": "The requested resource was not found.", "api_error": "منبع درخواستی یافت نشد", "api_error_status": 404}, {"api_help": "خطای اعتبارسنجی عمومی", "api_url": "همه API ها", "api_message_error": "There were validation errors.", "api_error": "خطاهای اعتبارسنجی وجود دارد", "api_error_status": 400}, {"api_help": "خطای فیلتر تراکنش", "api_url": "/api/transactions/", "api_message_error": "Invalid format. Use format like '10+' or '5-'", "api_error": "فرمت نامعتبر. از فرمت '10+' یا '5-' استفاده کنید", "api_error_status": 400}, {"api_help": "خطای احراز هویت", "api_url": "همه API ها", "api_message_error": "Authentication credentials were not provided.", "api_error": "اطلاعات احراز هویت ارائه نشده است", "api_error_status": 401}, {"api_help": "خطای دسترسی", "api_url": "همه API ها", "api_message_error": "You do not have permission to perform this action.", "api_error": "شما مجوز انجام این عمل را ندارید", "api_error_status": 403}, {"api_help": "خطا<PERSON> متد غیرمجاز", "api_url": "همه API ها", "api_message_error": "Method not allowed.", "api_error": "متد مجاز نیست", "api_error_status": 405}, {"api_help": "خطای فرمت داده", "api_url": "همه API ها", "api_message_error": "JSON parse error - Expecting value", "api_error": "خطای تجزیه JSON - مقدار مورد انتظار", "api_error_status": 400}, {"api_help": "خطای فی<PERSON>د الزامی", "api_url": "همه API ها", "api_message_error": "This field is required.", "api_error": "این فیلد الزامی است", "api_error_status": 400}, {"api_help": "خطای فی<PERSON>د خالی", "api_url": "همه API ها", "api_message_error": "This field may not be blank.", "api_error": "این فیلد نمی‌تواند خالی باشد", "api_error_status": 400}, {"api_help": "خطای فیلد null", "api_url": "همه API ها", "api_message_error": "This field may not be null.", "api_error": "این فیلد نمی‌تواند null باشد", "api_error_status": 400}, {"api_help": "خطای نوع داده نامعتبر", "api_url": "همه API ها", "api_message_error": "A valid integer is required.", "api_error": "ی<PERSON> عدد صحیح معتبر مورد نیاز است", "api_error_status": 400}, {"api_help": "خطای نوع داده نامعتبر", "api_url": "همه API ها", "api_message_error": "A valid number is required.", "api_error": "یک عدد معتبر مورد نیاز است", "api_error_status": 400}, {"api_help": "خطای نوع داده نامعتبر", "api_url": "همه API ها", "api_message_error": "Not a valid boolean.", "api_error": "مقدار بولین معتبر نیست", "api_error_status": 400}, {"api_help": "خطای طول رشته", "api_url": "همه API ها", "api_message_error": "Ensure this field has no more than X characters.", "api_error": "اطمینان حاصل کنید این فیلد بیش از X کاراکتر ندارد", "api_error_status": 400}, {"api_help": "خطای طول رشته", "api_url": "همه API ها", "api_message_error": "Ensure this field has at least X characters.", "api_error": "اطمینان حاصل کنید این فیلد حداقل X کاراکتر دارد", "api_error_status": 400}, {"api_help": "خطای انتخاب نامعتبر", "api_url": "همه API ها", "api_message_error": "Select a valid choice.", "api_error": "یک انتخاب معتبر انجام دهید", "api_error_status": 400}, {"api_help": "خطای فرمت ایمیل", "api_url": "همه API ها", "api_message_error": "Enter a valid email address.", "api_error": "یک آدرس ایمیل معتبر وارد کنید", "api_error_status": 400}, {"api_help": "خطای فرمت URL", "api_url": "همه API ها", "api_message_error": "Enter a valid URL.", "api_error": "یک URL معتبر وارد کنید", "api_error_status": 400}, {"api_help": "خطای فرمت تاریخ", "api_url": "همه API ها", "api_message_error": "Date has wrong format. Use one of these formats instead: YYYY-MM-DD.", "api_error": "فرمت تاریخ اشتباه است. از این فرمت استفاده کنید: YYYY-MM-DD", "api_error_status": 400}, {"api_help": "خطای فرمت زمان", "api_url": "همه API ها", "api_message_error": "Time has wrong format. Use one of these formats instead: HH:MM[:ss[.uuuuuu]].", "api_error": "فرمت زمان اشتباه است. از این فرمت استفاده کنید: HH:MM[:ss[.uuuuuu]]", "api_error_status": 400}, {"api_help": "خطای فرمت تاریخ و زمان", "api_url": "همه API ها", "api_message_error": "Datetime has wrong format. Use one of these formats instead: YYYY-MM-DD HH:MM[:ss[.uuuuuu]][TZ].", "api_error": "فرمت تاریخ و زمان اشتباه است. از این فرمت استفاده کنید: YYYY-MM-DD HH:MM[:ss[.uuuuuu]][TZ]", "api_error_status": 400}, {"api_help": "خطای محدودیت منحصر به فرد", "api_url": "همه API ها", "api_message_error": "This field must be unique.", "api_error": "این فیلد باید منحصر به فرد باشد", "api_error_status": 400}, {"api_help": "خ<PERSON><PERSON><PERSON> حد مجاز فایل", "api_url": "همه API ها", "api_message_error": "File too large. Size should not exceed X MB.", "api_error": "فایل خیلی بزرگ است. حجم نباید از X مگابایت بیشتر باشد", "api_error_status": 400}, {"api_help": "خطای نوع فایل", "api_url": "همه API ها", "api_message_error": "File extension not allowed.", "api_error": "پسوند فایل مجاز نیست", "api_error_status": 400}, {"api_help": "خطای محدودیت نرخ", "api_url": "همه API ها", "api_message_error": "Request was throttled. Expected available in X seconds.", "api_error": "درخواست محدود شد. انتظار می‌رود در X ثانیه در دسترس باشد", "api_error_status": 429}]
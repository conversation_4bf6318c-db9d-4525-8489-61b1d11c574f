#!/usr/bin/env python
"""
Test script for creating a loan request
"""
import sys
import os
import requests
import random
import string
from datetime import datetime

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import utility functions
from tests.api.utils import (
    BASE_URL, get_headers, get_deposit_data, save_loan_request_data,
    check_db_for_loan_request
)

def test_create_loan_request():
    """Test creating a loan request for deposit ID 14"""
    print("\n=== Testing Create Loan Request API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # For this test, we'll use deposit ID 14 as specified in the requirements
    deposit_id = 14
    
    # Prepare loan request data
    loan_request_data = {
        "deposit": deposit_id,
        "amount": 50000.00,  # Loan amount
        "installment_count": 10,  # Number of installments
        "installment_date": 15,  # Day of month for installments
        "description": "Test loan request created by API test script"
    }
    
    # API endpoint for creating a loan request
    url = f"{BASE_URL}/request/deposit/loan-request/"
    
    try:
        # Send create loan request
        print(f"Creating loan request for deposit ID: {deposit_id}")
        response = requests.post(url, json=loan_request_data, headers=headers)
        
        # Check if loan request creation was successful
        if response.status_code == 201:
            loan_request_response = response.json()
            loan_request_id = loan_request_response.get('id')
            print(f"Loan request creation successful. Loan Request ID: {loan_request_id}")
            
            # Check if the loan request exists in the database
            if check_db_for_loan_request(loan_request_id):
                print(f"Verified: Loan request ID {loan_request_id} exists in the database.")
            else:
                print(f"Warning: Could not verify loan request ID {loan_request_id} in the database.")
            
            # Save loan request data for future tests
            loan_request_info = {
                "id": loan_request_id,
                "deposit_id": deposit_id,
                "amount": 50000.00,
                "installment_count": 10,
                "installment_date": 15,
                "description": "Test loan request created by API test script"
            }
            save_loan_request_data(loan_request_info)
            return True
        else:
            print(f"Loan request creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during loan request creation: {str(e)}")
        return False
        
    return False

if __name__ == "__main__":
    success = test_create_loan_request()
    sys.exit(0 if success else 1)
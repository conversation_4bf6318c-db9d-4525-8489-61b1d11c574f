#!/usr/bin/env python
"""
Test script for ticket APIs
"""
import sys
import os
import requests
import random
from datetime import datetime

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import utility functions
from tests.api.utils import (
    BASE_URL, get_headers, generate_random_string,
    save_ticket_data, get_ticket_data, check_db_for_ticket
)

def test_create_ticket():
    """Test creating a ticket"""
    print("\n=== Testing Create Ticket API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Generate random data for ticket
    random_suffix = generate_random_string()
    subject = f"Test Ticket {random_suffix}"
    message = f"This is a test ticket created by the API test script {random_suffix}"
    
    # Prepare ticket data
    ticket_data = {
        "subject": subject,
        "message": message
    }
    
    # API endpoint for creating a ticket
    url = f"{BASE_URL}/tickets/create/"
    
    try:
        # Send create ticket request
        print(f"Creating ticket: {subject}")
        response = requests.post(url, json=ticket_data, headers=headers)
        
        # Check if ticket creation was successful
        if response.status_code == 201:
            ticket_response = response.json()
            ticket_id = ticket_response.get('id')
            print(f"Ticket creation successful. Ticket ID: {ticket_id}")
            
            # Check if the ticket exists in the database
            exists, ticket = check_db_for_ticket(ticket_id)
            if exists:
                print(f"Verified: Ticket ID {ticket_id} exists in the database.")
            else:
                print(f"Warning: Could not verify ticket ID {ticket_id} in the database.")
            
            # Save ticket data for future tests
            ticket_info = {
                "id": ticket_id,
                "subject": subject,
                "message": message
            }
            save_ticket_data(ticket_info)
            return True
        else:
            print(f"Ticket creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during ticket creation: {str(e)}")
        return False
        
    return False

def test_get_tickets():
    """Test getting tickets"""
    print("\n=== Testing Get Tickets API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # API endpoint for getting tickets
    url = f"{BASE_URL}/tickets/"
    
    try:
        # Send get tickets request
        print("Getting tickets...")
        response = requests.get(url, headers=headers)
        
        # Check if request was successful
        if response.status_code == 200:
            tickets = response.json()
            
            if isinstance(tickets, list):
                print(f"Successfully retrieved {len(tickets)} tickets.")
                
                # Print some details about the tickets
                for i, ticket in enumerate(tickets[:3]):  # Show first 3 tickets
                    print(f"Ticket {i+1}: ID={ticket.get('id')}, Subject={ticket.get('subject')}, Status={ticket.get('status')}")
                
                if len(tickets) > 3:
                    print(f"... and {len(tickets) - 3} more tickets.")
                
                return True
            else:
                print("Unexpected response format. Expected a list of tickets.")
                return False
        else:
            print(f"Get tickets failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during get tickets: {str(e)}")
        return False
        
    return False

def test_get_ticket_messages():
    """Test getting ticket messages"""
    print("\n=== Testing Get Ticket Messages API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get ticket data
    ticket_data = get_ticket_data()
    if not ticket_data or not ticket_data.get("id"):
        print("No ticket data found. Please run test_create_ticket first.")
        if not test_create_ticket():
            return False
        ticket_data = get_ticket_data()
    
    ticket_id = ticket_data.get("id")
    
    # API endpoint for getting ticket messages
    url = f"{BASE_URL}/tickets/{ticket_id}/messages/"
    
    try:
        # Send get ticket messages request
        print(f"Getting messages for ticket ID: {ticket_id}")
        response = requests.get(url, headers=headers)
        
        # Check if request was successful
        if response.status_code == 200:
            messages = response.json()
            
            if isinstance(messages, list):
                print(f"Successfully retrieved {len(messages)} messages for ticket ID: {ticket_id}")
                
                # Print some details about the messages
                for i, message in enumerate(messages):
                    print(f"Message {i+1}: ID={message.get('id')}, Content={message.get('content')[:30]}...")
                
                return True
            else:
                print("Unexpected response format. Expected a list of messages.")
                return False
        else:
            print(f"Get ticket messages failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during get ticket messages: {str(e)}")
        return False
        
    return False

def test_create_ticket_message():
    """Test creating a ticket message"""
    print("\n=== Testing Create Ticket Message API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get ticket data
    ticket_data = get_ticket_data()
    if not ticket_data or not ticket_data.get("id"):
        print("No ticket data found. Please run test_create_ticket first.")
        if not test_create_ticket():
            return False
        ticket_data = get_ticket_data()
    
    ticket_id = ticket_data.get("id")
    
    # Generate random data for message
    random_suffix = generate_random_string()
    content = f"This is a test message created by the API test script {random_suffix}"
    
    # Prepare message data
    message_data = {
        "content": content
    }
    
    # API endpoint for creating a ticket message
    url = f"{BASE_URL}/tickets/{ticket_id}/messages/create/"
    
    try:
        # Send create ticket message request
        print(f"Creating message for ticket ID: {ticket_id}")
        response = requests.post(url, json=message_data, headers=headers)
        
        # Check if message creation was successful
        if response.status_code == 201:
            message_response = response.json()
            message_id = message_response.get('id')
            print(f"Message creation successful. Message ID: {message_id}")
            return True
        else:
            print(f"Message creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during message creation: {str(e)}")
        return False
        
    return False

def run_all_tests():
    """Run all ticket tests"""
    tests = [
        test_create_ticket,
        test_get_tickets,
        test_get_ticket_messages,
        test_create_ticket_message
    ]
    
    results = []
    for test_func in tests:
        result = test_func()
        results.append(result)
        print(f"Test {test_func.__name__}: {'PASSED' if result else 'FAILED'}")
        print("-" * 50)
    
    return all(results)

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
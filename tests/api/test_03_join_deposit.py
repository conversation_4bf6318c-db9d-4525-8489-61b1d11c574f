#!/usr/bin/env python
"""
Test script for joining a deposit
"""
import sys
import os
import requests
import random
import string
from datetime import datetime

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import utility functions
from tests.api.utils import BASE_URL, get_headers, get_deposit_data, get_user_data

def test_join_deposit():
    """Test joining a deposit"""
    print("\n=== Testing Join Deposit API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get deposit data
    deposit_data = get_deposit_data()
    if not deposit_data or not deposit_data.get("id"):
        print("No deposit data found. Please run test_02_create_deposit.py first.")
        return False
    
    deposit_id = deposit_data.get("id")
    
    # Prepare join request data
    join_data = {
        "deposit": deposit_id,
        "requested_unit_count": 2,  # Request 2 units
        "monthly_installment_amount": deposit_data.get("unit_amount", 10000) * 2  # 2 units worth
    }
    
    # API endpoint for joining a deposit
    url = f"{BASE_URL}/request/deposit/join/{deposit_id}/"
    
    try:
        # Send join deposit request
        print(f"Joining deposit ID: {deposit_id}")
        response = requests.post(url, json=join_data, headers=headers)
        
        # Check if join request was successful
        if response.status_code == 201:
            join_response = response.json()
            join_request_id = join_response.get('id')
            print(f"Join request successful. Request ID: {join_request_id}")
            
            # Now approve the join request (this would normally be done by an admin)
            # For testing purposes, we'll use the Django ORM directly
            print("Approving join request...")
            
            # Use Django ORM to approve the request
            from apps.request.models import RequestJoinDeposit
            join_request = RequestJoinDeposit.objects.get(id=join_request_id)
            join_request.status = RequestJoinDeposit.StatusChoices.APPROVED
            join_request.approve()
            
            print(f"Join request approved. User is now a member of deposit ID: {deposit_id}")
            return True
        else:
            print(f"Join request failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during join request: {str(e)}")
        return False
        
    return False

if __name__ == "__main__":
    success = test_join_deposit()
    sys.exit(0 if success else 1)
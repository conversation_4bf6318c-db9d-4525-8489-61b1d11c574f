#!/usr/bin/env python3

# Test script to verify parameter extraction from Swagger JSON

import json
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/Desktop/main/habbib/qatreh/backend')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.base')
import django
django.setup()

from apps.api.views.swagger_documentation import SwaggerBasedDocumentationView

def test_parameter_extraction():
    """Test parameter extraction with the invitation endpoint example"""
    
    # Sample Swagger JSON for invitation endpoint
    sample_endpoint = {
        "post": {
            "operationId": "account_invitaion_create",
            "summary": "Register with Invitation Code",
            "description": "Join a region using invitation code. Returns is_active and is_member status.",
            "parameters": [
                {
                    "name": "data",
                    "in": "body",
                    "required": True,
                    "schema": {
                        "required": ["invitation_code"],
                        "type": "object",
                        "properties": {
                            "invitation_code": {
                                "description": "Unique invitation code",
                                "type": "string",
                                "example": "ABC123DEF456"
                            }
                        }
                    }
                }
            ],
            "responses": {
                "200": {
                    "description": "User added to region successfully",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "message": {
                                "type": "string",
                                "example": "User added to the region successfully."
                            },
                            "is_active": {
                                "description": "User active status",
                                "type": "boolean",
                                "example": True
                            },
                            "is_member": {
                                "description": "User membership status",
                                "type": "boolean",
                                "example": True
                            }
                        }
                    }
                }
            },
            "tags": ["Account"]
        }
    }
    
    # Create view instance
    view = SwaggerBasedDocumentationView()
    
    # Extract parameters
    parameters = view.extract_parameters(sample_endpoint["post"])
    
    print("🔍 Testing Parameter Extraction")
    print("=" * 50)
    print(f"📊 Found {len(parameters)} parameters:")
    
    for i, param in enumerate(parameters, 1):
        print(f"\n{i}. Parameter:")
        print(f"   Name: {param.get('name')}")
        print(f"   Type: {param.get('type')}")
        print(f"   Data Type: {param.get('data_type')}")
        print(f"   Required: {param.get('required')}")
        print(f"   Description: {param.get('description')}")
        print(f"   Example: {param.get('example')}")
    
    # Test expected results
    expected_param = {
        'name': 'invitation_code',
        'type': 'body',
        'data_type': 'string',
        'required': True,
        'description': 'Unique invitation code',
        'example': 'ABC123DEF456'
    }
    
    print("\n" + "=" * 50)
    print("✅ Expected vs Actual:")
    
    if parameters:
        actual_param = parameters[0]
        for key, expected_value in expected_param.items():
            actual_value = actual_param.get(key)
            status = "✅" if actual_value == expected_value else "❌"
            print(f"{status} {key}: Expected='{expected_value}', Actual='{actual_value}'")
    else:
        print("❌ No parameters extracted!")
    
    return parameters

if __name__ == "__main__":
    test_parameter_extraction()

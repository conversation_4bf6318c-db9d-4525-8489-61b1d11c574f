import requests
import os
import json
import django
from pathlib import Path
import random
import string
from datetime import datetime, timedelta

# Setup Django environment
BASE_DIR = Path(__file__).resolve().parent.parent.parent
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# API base URL (adjust as needed)
BASE_URL = "http://localhost:8000/api"

def generate_random_string(length=8):
    """Generate a random string of fixed length"""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def generate_random_phone():
    """Generate a random phone number"""
    return f"+9890{random.randint(10000000, 99999999)}"

def save_token_to_file(token, file_path="tests/api/token.json"):
    """Save authentication token to a file"""
    with open(file_path, 'w') as f:
        json.dump({"token": token}, f)

def get_token_from_file(file_path="tests/api/token.json"):
    """Get authentication token from a file"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
            return data.get("token")
    except (FileNotFoundError, json.JSONDecodeError):
        return None

def save_user_data(user_data, file_path="tests/api/user_data.json"):
    """Save user data to a file"""
    with open(file_path, 'w') as f:
        json.dump(user_data, f)

def get_user_data(file_path="tests/api/user_data.json"):
    """Get user data from a file"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_deposit_data(deposit_data, file_path="tests/api/deposit_data.json"):
    """Save deposit data to a file"""
    with open(file_path, 'w') as f:
        json.dump(deposit_data, f)

def get_deposit_data(file_path="tests/api/deposit_data.json"):
    """Get deposit data from a file"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_loan_request_data(loan_request_data, file_path="tests/api/loan_request_data.json"):
    """Save loan request data to a file"""
    with open(file_path, 'w') as f:
        json.dump(loan_request_data, f)

def get_loan_request_data(file_path="tests/api/loan_request_data.json"):
    """Get loan request data from a file"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_ticket_data(ticket_data, file_path="tests/api/ticket_data.json"):
    """Save ticket data to a file"""
    with open(file_path, 'w') as f:
        json.dump(ticket_data, f)

def get_ticket_data(file_path="tests/api/ticket_data.json"):
    """Get ticket data from a file"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_voting_data(voting_data, file_path="tests/api/voting_data.json"):
    """Save voting data to a file"""
    with open(file_path, 'w') as f:
        json.dump(voting_data, f)

def get_voting_data(file_path="tests/api/voting_data.json"):
    """Get voting data from a file"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def get_headers():
    """Get headers with authentication token"""
    token = get_token_from_file()
    if token:
        return {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
    return {"Content-Type": "application/json"}

def check_db_for_loan_request(loan_request_id):
    """Check if a loan request exists in the database"""
    from apps.request.models import LoanRequest
    try:
        loan_request = LoanRequest.objects.get(id=loan_request_id)
        return loan_request is not None
    except LoanRequest.DoesNotExist:
        return False

def check_db_for_loan(loan_request_id):
    """Check if a loan exists for a given loan request"""
    from apps.loan.models import Loan
    from apps.request.models import LoanRequest
    
    try:
        loan_request = LoanRequest.objects.get(id=loan_request_id)
        # Find loan by deposit and user
        loans = Loan.objects.filter(
            deposit=loan_request.deposit, 
            deposit_membership__user=loan_request.user
        )
        return loans.exists(), loans.first()
    except LoanRequest.DoesNotExist:
        return False, None

def check_db_for_installments(loan_id):
    """Check if installments exist for a given loan"""
    from apps.loan.models import LoanInstallment
    
    installments = LoanInstallment.objects.filter(loan_id=loan_id)
    return installments.exists(), installments.count()

def check_db_for_deposit_membership(user_id, deposit_id):
    """Check if a user is a member of a deposit"""
    from apps.deposit.models import DepositMembership
    
    membership = DepositMembership.objects.filter(
        user_id=user_id,
        deposit_id=deposit_id,
        is_active=True
    ).first()
    
    return membership is not None, membership

def check_db_for_ticket(ticket_id):
    """Check if a ticket exists in the database"""
    from apps.ticket.models import Ticket
    
    try:
        ticket = Ticket.objects.get(id=ticket_id)
        return ticket is not None, ticket
    except Ticket.DoesNotExist:
        return False, None

def check_db_for_voting(voting_id):
    """Check if a voting poll exists in the database"""
    from apps.voting.models import VotingPoll
    
    try:
        voting = VotingPoll.objects.get(id=voting_id)
        return voting is not None, voting
    except VotingPoll.DoesNotExist:
        return False, None
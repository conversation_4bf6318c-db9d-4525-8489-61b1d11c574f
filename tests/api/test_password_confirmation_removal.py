#!/usr/bin/env python
"""
Test script to verify password_confirmation has been removed correctly.
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/home/<USER>/Desktop/main/habbib/qatreh/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from apps.account.serializers.user import UserRegisterSerializer

def test_serializer_fields():
    """Test that password_confirmation has been removed from serializer"""
    print("Testing UserRegisterSerializer fields...")
    
    try:
        serializer = UserRegisterSerializer()
        fields = serializer.fields
        
        # Check that password_confirmation is NOT in fields
        assert 'password_confirmation' not in fields, "password_confirmation should be removed from fields"
        print("✅ password_confirmation removed from serializer fields")
        
        # Check that other required fields are still present
        required_fields = ['phone_number', 'fullname', 'password', 'name', 'family']
        for field in required_fields:
            assert field in fields, f"Required field '{field}' missing from serializer"
        
        print("✅ All other required fields are present")
        
        # Check Meta fields
        meta_fields = serializer.Meta.fields
        assert 'password_confirmation' not in meta_fields, "password_confirmation should be removed from Meta.fields"
        print("✅ password_confirmation removed from Meta.fields")
        
        # Check extra_kwargs
        extra_kwargs = serializer.Meta.extra_kwargs
        assert 'password_confirmation' not in extra_kwargs, "password_confirmation should be removed from extra_kwargs"
        print("✅ password_confirmation removed from extra_kwargs")
        
        return True
        
    except Exception as e:
        print(f"❌ Serializer fields test failed: {e}")
        return False

def test_validation():
    """Test that validation works without password_confirmation"""
    print("\nTesting validation without password_confirmation...")
    
    try:
        # Test valid data with unique phone number
        import time
        unique_phone = f'+9891234567{int(time.time()) % 100:02d}'
        valid_data = {
            'phone_number': unique_phone,
            'fullname': 'John Doe',
            'name': 'John',
            'family': 'Doe',
            'password': 'validpassword123'
        }
        
        serializer = UserRegisterSerializer(data=valid_data)
        assert serializer.is_valid(), f"Valid data should pass validation, errors: {serializer.errors}"
        print("✅ Valid data passes validation")
        
        # Test short password (should still fail)
        invalid_data = valid_data.copy()
        invalid_data['password'] = '123'
        
        serializer = UserRegisterSerializer(data=invalid_data)
        assert not serializer.is_valid(), "Short password should fail validation"
        assert 'non_field_errors' in serializer.errors, "Short password should produce non_field_errors"
        print("✅ Short password validation still works")
        
        # Test that fullname is auto-generated when name and family are not provided
        import time
        unique_phone2 = f'+9891234567{int(time.time()) % 100 + 1:02d}'
        incomplete_data = {
            'phone_number': unique_phone2,
            'password': 'validpassword123'
            # No name or family provided - fullname should be auto-generated from phone_number
        }

        serializer = UserRegisterSerializer(data=incomplete_data)
        assert serializer.is_valid(), f"Validation should pass without fullname: {serializer.errors}"
        validated_data = serializer.validated_data
        assert 'fullname' in validated_data, "fullname should be auto-generated"
        assert validated_data['fullname'] == unique_phone2, "fullname should be phone_number when name/family not provided"
        print("✅ Auto-generated fullname validation works")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation test failed: {e}")
        return False

def test_documentation_files():
    """Test that documentation files have been updated"""
    print("\nTesting documentation files...")
    
    try:
        # Test apps/account/doc.py
        doc_file_path = '/home/<USER>/Desktop/main/habbib/qatreh/backend/apps/account/doc.py'
        with open(doc_file_path, 'r', encoding='utf-8') as f:
            doc_content = f.read()
        
        # Check that password_confirmation is removed from registration examples
        # Find the registration section
        register_start = doc_content.find('def doc_register():')
        register_end = doc_content.find('def doc_verify():', register_start)
        if register_end == -1:
            register_section = doc_content[register_start:]
        else:
            register_section = doc_content[register_start:register_end]

        assert '"password_confirmation"' not in register_section, "password_confirmation found in registration section of doc.py"
        print("✅ password_confirmation removed from doc.py")
        
        # Test apps/api/views/documentation.py
        api_doc_file_path = '/home/<USER>/Desktop/main/habbib/qatreh/backend/apps/api/views/documentation.py'
        with open(api_doc_file_path, 'r', encoding='utf-8') as f:
            api_doc_content = f.read()
        
        # Check that password_confirmation is removed from parameters
        assert "'password_confirmation'" not in api_doc_content, "password_confirmation found in API documentation parameters"
        print("✅ password_confirmation removed from API documentation")
        
        # Test template
        template_file_path = '/home/<USER>/Desktop/main/habbib/qatreh/backend/templates/api/documentation.html'
        with open(template_file_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Check that password_confirmation is removed from template examples
        assert '"password_confirmation"' not in template_content, "password_confirmation found in template examples"
        print("✅ password_confirmation removed from template")
        
        return True
        
    except Exception as e:
        print(f"❌ Documentation files test failed: {e}")
        return False

def test_serializer_structure():
    """Test the overall structure of the serializer"""
    print("\nTesting serializer structure...")
    
    try:
        # Read the serializer file to check structure
        serializer_file_path = '/home/<USER>/Desktop/main/habbib/qatreh/backend/apps/account/serializers/user.py'
        with open(serializer_file_path, 'r', encoding='utf-8') as f:
            serializer_content = f.read()
        
        # Check that password_confirmation field definition is removed from UserRegisterSerializer
        # Find the UserRegisterSerializer class
        register_start = serializer_content.find('class UserRegisterSerializer(')
        register_end = serializer_content.find('class UserVerifySerializer(', register_start)
        if register_end == -1:
            # If UserVerifySerializer is not found, look for next class
            register_end = serializer_content.find('class ', register_start + 1)

        if register_end != -1:
            register_section = serializer_content[register_start:register_end]
        else:
            register_section = serializer_content[register_start:]

        assert 'password_confirmation = serializers.CharField(' not in register_section, "password_confirmation field definition still exists in UserRegisterSerializer"
        print("✅ password_confirmation field definition removed")
        
        # Check that validation method doesn't reference password_confirmation
        validate_method_start = serializer_content.find('def validate(self, data):')
        if validate_method_start != -1:
            # Find the end of the validate method
            next_method_start = serializer_content.find('def ', validate_method_start + 1)
            if next_method_start == -1:
                validate_method_content = serializer_content[validate_method_start:]
            else:
                validate_method_content = serializer_content[validate_method_start:next_method_start]
            
            # Check that password_confirmation is not referenced in validation
            assert 'password_confirmation' not in validate_method_content, "password_confirmation still referenced in validate method"
            print("✅ password_confirmation removed from validate method")
        
        return True
        
    except Exception as e:
        print(f"❌ Serializer structure test failed: {e}")
        return False

if __name__ == '__main__':
    print("Starting password_confirmation removal tests...\n")
    
    try:
        # Run tests
        test1 = test_serializer_fields()
        test2 = test_validation()
        test3 = test_documentation_files()
        test4 = test_serializer_structure()
        
        if test1 and test2 and test3 and test4:
            print("\n🎉 All password_confirmation removal tests passed successfully!")
            print("\n📋 Summary of changes:")
            print("✅ Removed password_confirmation field from UserRegisterSerializer")
            print("✅ Removed password_confirmation from Meta.fields")
            print("✅ Removed password_confirmation from extra_kwargs")
            print("✅ Updated validate method to remove password confirmation logic")
            print("✅ Updated doc.py examples to remove password_confirmation")
            print("✅ Updated API documentation parameters")
            print("✅ Updated template examples")
            print("✅ Password length validation still works (minimum 8 characters)")
            print("✅ Other field validations still work correctly")
            print("\n📖 New API usage:")
            print("   POST /api/account/register/")
            print("   {")
            print('     "phone_number": "+************",')
            print('     "fullname": "John Doe",')
            print('     "name": "John",')
            print('     "family": "Doe",')
            print('     "password": "securepassword123"')
            print("   }")
        else:
            print("\n❌ Some tests failed")
            sys.exit(1)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

#!/usr/bin/env python
"""
Test script for deposit creation and management APIs
"""
import sys
import os
import requests
import random
from datetime import datetime, timedelta

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import utility functions
from tests.api.utils import (
    BASE_URL, get_headers, save_deposit_data, get_deposit_data,
    generate_random_string, get_user_data, check_db_for_deposit_membership
)

def test_create_poll_deposit():
    """Test creating a poll deposit"""
    print("\n=== Testing Create Poll Deposit API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Generate random data for deposit
    random_suffix = generate_random_string()
    title = f"Test Poll Deposit {random_suffix}"
    description = f"This is a test poll deposit created by the API test script {random_suffix}"
    
    # Set the initial lottery date to 30 days from now
    initial_lottery_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
    
    # Prepare deposit data
    deposit_data = {
        "title": title,
        "description": description,
        "total_debt_amount": 100000.00,
        "lottery_month_count": 10,
        "unit_amount": 10000.00,
        "payment_cycle": 15,  # Day of month for payment
        "max_unit_per_request": 5,
        "max_members_count": 20,
        "initial_lottery_date": initial_lottery_date,
        "rules": [
            {"subject": "Rule 1", "description": "Description for Rule 1"},
            {"subject": "Rule 2", "description": "Description for Rule 2"}
        ]
    }
    
    # API endpoint for creating a poll deposit
    url = f"{BASE_URL}/requests/deposit/create/poll-deposit/"
    
    try:
        # Send create deposit request
        print(f"Creating poll deposit: {title}")
        response = requests.post(url, json=deposit_data, headers=headers)
        
        # Check if deposit creation was successful
        if response.status_code == 201:
            deposit_response = response.json()
            deposit_id = deposit_response.get('id')
            print(f"Poll deposit creation successful. Deposit ID: {deposit_id}")
            
            # Save deposit data for future tests
            deposit_info = {
                "id": deposit_id,
                "title": title,
                "description": description,
                "type": "Poll",
                "total_debt_amount": 100000.00,
                "unit_amount": 10000.00,
                "payment_cycle": 15,
                "max_unit_per_request": 5,
                "max_members_count": 20
            }
            save_deposit_data(deposit_info)
            return True
        else:
            print(f"Poll deposit creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during poll deposit creation: {str(e)}")
        return False
        
    return False

def test_create_saving_deposit():
    """Test creating a saving deposit"""
    print("\n=== Testing Create Saving Deposit API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Generate random data for deposit
    random_suffix = generate_random_string()
    title = f"Test Saving Deposit {random_suffix}"
    description = f"This is a test saving deposit created by the API test script {random_suffix}"
    
    # Set the start date to 15 days from now
    start_date = (datetime.now() + timedelta(days=15)).strftime("%Y-%m-%d")
    
    # Prepare deposit data
    deposit_data = {
        "title": title,
        "description": description,
        "total_debt_amount": 200000.00,
        "unit_amount": 20000.00,
        "payment_cycle": 10,  # Day of month for payment
        "max_unit_per_request": 3,
        "max_members_count": 15,
        "validity_duration": 24,  # 24 months
        "start_date": start_date,
        "rules": [
            {"subject": "Saving Rule 1", "description": "Description for Saving Rule 1"},
            {"subject": "Saving Rule 2", "description": "Description for Saving Rule 2"}
        ]
    }
    
    # API endpoint for creating a saving deposit
    url = f"{BASE_URL}/requests/deposit/create/saving-deposit/"
    
    try:
        # Send create deposit request
        print(f"Creating saving deposit: {title}")
        response = requests.post(url, json=deposit_data, headers=headers)
        
        # Check if deposit creation was successful
        if response.status_code == 201:
            deposit_response = response.json()
            deposit_id = deposit_response.get('id')
            print(f"Saving deposit creation successful. Deposit ID: {deposit_id}")
            
            # Save deposit data for future tests
            deposit_info = {
                "id": deposit_id,
                "title": title,
                "description": description,
                "type": "Saving",
                "total_debt_amount": 200000.00,
                "unit_amount": 20000.00,
                "payment_cycle": 10,
                "max_unit_per_request": 3,
                "max_members_count": 15,
                "validity_duration": 24,
                "start_date": start_date
            }
            save_deposit_data(deposit_info)
            return True
        else:
            print(f"Saving deposit creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during saving deposit creation: {str(e)}")
        return False
        
    return False

def test_create_reporting_deposit():
    """Test creating a reporting deposit"""
    print("\n=== Testing Create Reporting Deposit API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Generate random data for deposit
    random_suffix = generate_random_string()
    title = f"Test Reporting Deposit {random_suffix}"
    description = f"This is a test reporting deposit created by the API test script {random_suffix}"
    
    # Prepare deposit data
    deposit_data = {
        "title": title,
        "description": description,
        "total_debt_amount": 150000.00,
        "unit_amount": 15000.00,
        "payment_cycle": 5,  # Day of month for payment
        "max_unit_per_request": 4,
        "max_members_count": 10,
        "validity_duration": 12,  # 12 months
        "rules": [
            {"subject": "Reporting Rule 1", "description": "Description for Reporting Rule 1"},
            {"subject": "Reporting Rule 2", "description": "Description for Reporting Rule 2"}
        ]
    }
    
    # API endpoint for creating a reporting deposit
    url = f"{BASE_URL}/requests/deposit/create/reporting-deposit/"
    
    try:
        # Send create deposit request
        print(f"Creating reporting deposit: {title}")
        response = requests.post(url, json=deposit_data, headers=headers)
        
        # Check if deposit creation was successful
        if response.status_code == 201:
            deposit_response = response.json()
            deposit_id = deposit_response.get('id')
            print(f"Reporting deposit creation successful. Deposit ID: {deposit_id}")
            
            # Save deposit data for future tests
            deposit_info = {
                "id": deposit_id,
                "title": title,
                "description": description,
                "type": "Reporting",
                "total_debt_amount": 150000.00,
                "unit_amount": 15000.00,
                "payment_cycle": 5,
                "max_unit_per_request": 4,
                "max_members_count": 10,
                "validity_duration": 12
            }
            save_deposit_data(deposit_info)
            return True
        else:
            print(f"Reporting deposit creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during reporting deposit creation: {str(e)}")
        return False
        
    return False

def test_get_deposit_list():
    """Test getting the list of deposits"""
    print("\n=== Testing Get Deposit List API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # API endpoint for getting deposit list
    url = f"{BASE_URL}/deposits/"
    
    try:
        # Send get deposit list request
        print("Getting deposit list...")
        response = requests.get(url, headers=headers)
        
        # Check if request was successful
        if response.status_code == 200:
            deposits = response.json()
            
            if isinstance(deposits, list):
                print(f"Successfully retrieved {len(deposits)} deposits.")
                
                # Print some details about the deposits
                for i, deposit in enumerate(deposits[:3]):  # Show first 3 deposits
                    print(f"Deposit {i+1}: ID={deposit.get('id')}, Title={deposit.get('title')}")
                
                if len(deposits) > 3:
                    print(f"... and {len(deposits) - 3} more deposits.")
                
                return True
            else:
                print("Unexpected response format. Expected a list of deposits.")
                return False
        else:
            print(f"Get deposit list failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during get deposit list: {str(e)}")
        return False
        
    return False

def test_get_deposit_detail():
    """Test getting deposit details"""
    print("\n=== Testing Get Deposit Detail API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get deposit data
    deposit_data = get_deposit_data()
    if not deposit_data or not deposit_data.get("id"):
        print("No deposit data found. Please run test_create_poll_deposit first.")
        if not test_create_poll_deposit():
            return False
        deposit_data = get_deposit_data()
    
    deposit_id = deposit_data.get("id")
    
    # API endpoint for getting deposit details
    url = f"{BASE_URL}/deposits/{deposit_id}/"
    
    try:
        # Send get deposit detail request
        print(f"Getting details for deposit ID: {deposit_id}")
        response = requests.get(url, headers=headers)
        
        # Check if request was successful
        if response.status_code == 200:
            deposit_detail = response.json()
            
            print(f"Successfully retrieved details for deposit ID: {deposit_id}")
            print(f"Title: {deposit_detail.get('title')}")
            print(f"Description: {deposit_detail.get('description')}")
            print(f"Type: {deposit_detail.get('deposit_type')}")
            
            return True
        else:
            print(f"Get deposit detail failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during get deposit detail: {str(e)}")
        return False
        
    return False

def test_join_deposit():
    """Test joining a deposit"""
    print("\n=== Testing Join Deposit API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get deposit data
    deposit_data = get_deposit_data()
    if not deposit_data or not deposit_data.get("id"):
        print("No deposit data found. Please run test_create_poll_deposit first.")
        if not test_create_poll_deposit():
            return False
        deposit_data = get_deposit_data()
    
    deposit_id = deposit_data.get("id")
    
    # Prepare join request data
    join_data = {
        "deposit": deposit_id,
        "requested_unit_count": 2,  # Request 2 units
        "monthly_installment_amount": deposit_data.get("unit_amount", 10000) * 2  # 2 units worth
    }
    
    # API endpoint for joining a deposit
    url = f"{BASE_URL}/requests/deposit/join/{deposit_id}/"
    
    try:
        # Send join deposit request
        print(f"Joining deposit ID: {deposit_id}")
        response = requests.post(url, json=join_data, headers=headers)
        
        # Check if join request was successful
        if response.status_code == 201:
            join_response = response.json()
            join_request_id = join_response.get('id')
            print(f"Join request successful. Request ID: {join_request_id}")
            
            # Now approve the join request (this would normally be done by an admin)
            # For testing purposes, we'll use the Django ORM directly
            print("Approving join request...")
            
            # Use Django ORM to approve the request
            from apps.request.models import RequestJoinDeposit
            join_request = RequestJoinDeposit.objects.get(id=join_request_id)
            join_request.status = RequestJoinDeposit.StatusChoices.APPROVED
            join_request.approve()
            
            # Check if the user is now a member of the deposit
            user_data = get_user_data()
            user_id = user_data.get("id")
            
            is_member, membership = check_db_for_deposit_membership(user_id, deposit_id)
            
            if is_member:
                print(f"Join request approved. User is now a member of deposit ID: {deposit_id}")
                return True
            else:
                print(f"User is not a member of deposit ID: {deposit_id} after approval.")
                return False
        else:
            print(f"Join request failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during join request: {str(e)}")
        return False
        
    return False

def test_get_deposit_members():
    """Test getting deposit members"""
    print("\n=== Testing Get Deposit Members API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get deposit data
    deposit_data = get_deposit_data()
    if not deposit_data or not deposit_data.get("id"):
        print("No deposit data found. Please run test_create_poll_deposit first.")
        if not test_create_poll_deposit():
            return False
        deposit_data = get_deposit_data()
    
    deposit_id = deposit_data.get("id")
    
    # API endpoint for getting deposit members
    url = f"{BASE_URL}/deposits/{deposit_id}/members/"
    
    try:
        # Send get deposit members request
        print(f"Getting members for deposit ID: {deposit_id}")
        response = requests.get(url, headers=headers)
        
        # Check if request was successful
        if response.status_code == 200:
            members = response.json()
            
            if isinstance(members, list):
                print(f"Successfully retrieved {len(members)} members for deposit ID: {deposit_id}")
                
                # Print some details about the members
                for i, member in enumerate(members):
                    print(f"Member {i+1}: ID={member.get('id')}, User={member.get('user_fullname')}, Role={member.get('role')}")
                
                return True
            else:
                print("Unexpected response format. Expected a list of members.")
                return False
        else:
            print(f"Get deposit members failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during get deposit members: {str(e)}")
        return False
        
    return False

def run_all_tests():
    """Run all deposit management tests"""
    tests = [
        test_create_poll_deposit,
        test_create_saving_deposit,
        test_create_reporting_deposit,
        test_get_deposit_list,
        test_get_deposit_detail,
        test_join_deposit,
        test_get_deposit_members
    ]
    
    results = []
    for test_func in tests:
        result = test_func()
        results.append(result)
        print(f"Test {test_func.__name__}: {'PASSED' if result else 'FAILED'}")
        print("-" * 50)
    
    return all(results)

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
#!/usr/bin/env python
"""
Test script to verify the updated documentation and API functionality.
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/home/<USER>/Desktop/main/habbib/qatreh/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from apps.account.serializers.user import UserRegisterSerializer, UserVerifySerializer
from apps.api.views.documentation import DocumentationView

def test_serializer_fields():
    """Test that serializers have the correct fields and help_text"""
    print("Testing serializer fields and help_text...")
    
    # Test UserRegisterSerializer
    register_serializer = UserRegisterSerializer()
    register_fields = register_serializer.fields
    
    # Check that new fields exist
    assert 'name' in register_fields, "name field missing from UserRegisterSerializer"
    assert 'family' in register_fields, "family field missing from UserRegisterSerializer"
    assert 'password_confirmation' in register_fields, "password_confirmation field missing from UserRegisterSerializer"
    
    # Check help_text
    assert 'first_name' in register_fields['name'].help_text, "name field help_text should mention first_name"
    assert 'last_name' in register_fields['family'].help_text, "family field help_text should mention last_name"
    assert 'match' in register_fields['password_confirmation'].help_text, "password_confirmation help_text should mention match"
    
    print("✅ UserRegisterSerializer fields and help_text correct")
    
    # Test UserVerifySerializer
    verify_serializer = UserVerifySerializer()
    verify_fields = verify_serializer.fields
    
    # Check that new fields exist
    assert 'name' in verify_fields, "name field missing from UserVerifySerializer"
    assert 'family' in verify_fields, "family field missing from UserVerifySerializer"
    assert 'code' in verify_fields, "code field missing from UserVerifySerializer"
    
    # Check help_text
    assert 'first_name' in verify_fields['name'].help_text, "name field help_text should mention first_name"
    assert 'last_name' in verify_fields['family'].help_text, "family field help_text should mention last_name"
    assert '5-digit' in verify_fields['code'].help_text, "code field help_text should mention 5-digit"
    
    print("✅ UserVerifySerializer fields and help_text correct")
    
    return True

def test_documentation_structure():
    """Test that documentation view has updated structure"""
    print("\nTesting documentation structure...")
    
    doc_view = DocumentationView()
    api_structure = doc_view.get_api_structure()
    
    # Check account endpoints exist
    assert 'account' in api_structure, "account section missing from API structure"
    
    account_endpoints = api_structure['account']['endpoints']
    
    # Find registration endpoint
    register_endpoint = None
    verify_endpoint = None
    
    for endpoint in account_endpoints:
        if endpoint['name'] == 'User Registration':
            register_endpoint = endpoint
        elif endpoint['name'] == 'User Verification':
            verify_endpoint = endpoint
    
    assert register_endpoint is not None, "User Registration endpoint not found"
    assert verify_endpoint is not None, "User Verification endpoint not found"
    
    # Check registration endpoint has new parameters
    register_params = {param['name']: param for param in register_endpoint['parameters']}
    assert 'name' in register_params, "name parameter missing from registration endpoint"
    assert 'family' in register_params, "family parameter missing from registration endpoint"
    assert 'password_confirmation' in register_params, "password_confirmation parameter missing from registration endpoint"
    
    # Check verify endpoint has new parameters
    verify_params = {param['name']: param for param in verify_endpoint['parameters']}
    assert 'name' in verify_params, "name parameter missing from verification endpoint"
    assert 'family' in verify_params, "family parameter missing from verification endpoint"
    
    # Check response examples exist
    assert 'success' in register_endpoint['response_examples'], "success response missing from registration endpoint"
    assert 'validation_error' in register_endpoint['response_examples'], "validation_error response missing from registration endpoint"
    
    assert 'success' in verify_endpoint['response_examples'], "success response missing from verification endpoint"
    assert 'invalid_code' in verify_endpoint['response_examples'], "invalid_code response missing from verification endpoint"
    
    print("✅ Documentation structure correct")
    
    return True

def test_serializer_validation():
    """Test serializer validation with new fields"""
    print("\nTesting serializer validation...")
    
    # Test UserRegisterSerializer validation
    valid_data = {
        'phone_number': '+989123456789',
        'fullname': 'John Doe',
        'name': 'John',
        'family': 'Doe',
        'password': 'testpass123',
        'password_confirmation': 'testpass123'
    }
    
    serializer = UserRegisterSerializer(data=valid_data)
    assert serializer.is_valid(), f"Valid data should pass validation, errors: {serializer.errors}"
    
    # Test password mismatch
    invalid_data = valid_data.copy()
    invalid_data['password_confirmation'] = 'different_password'
    
    serializer = UserRegisterSerializer(data=invalid_data)
    assert not serializer.is_valid(), "Password mismatch should fail validation"
    assert 'non_field_errors' in serializer.errors, "Password mismatch should produce non_field_errors"
    
    # Test short password
    invalid_data = valid_data.copy()
    invalid_data['password'] = '123'
    invalid_data['password_confirmation'] = '123'
    
    serializer = UserRegisterSerializer(data=invalid_data)
    assert not serializer.is_valid(), "Short password should fail validation"
    
    print("✅ Serializer validation working correctly")
    
    # Test UserVerifySerializer validation
    verify_data = {
        'phone_number': '+989123456789',
        'code': '12345',
        'name': 'John',
        'family': 'Doe'
    }
    
    verify_serializer = UserVerifySerializer(data=verify_data)
    assert verify_serializer.is_valid(), f"Valid verify data should pass validation, errors: {verify_serializer.errors}"
    
    print("✅ UserVerifySerializer validation working correctly")
    
    return True

if __name__ == '__main__':
    print("Starting documentation and API tests...\n")
    
    try:
        # Run tests
        test_serializer_fields()
        test_documentation_structure()
        test_serializer_validation()
        
        print("\n🎉 All documentation and API tests passed successfully!")
        print("\n📋 Summary of updates:")
        print("✅ Added name and family fields to UserRegisterSerializer and UserVerifySerializer")
        print("✅ Added proper help_text to all fields for better Swagger documentation")
        print("✅ Updated API documentation structure with new parameters")
        print("✅ Added comprehensive response examples for success and error cases")
        print("✅ Updated docs.py with detailed field descriptions and examples")
        print("✅ Maintained template compatibility for documentation rendering")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

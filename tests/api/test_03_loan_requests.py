#!/usr/bin/env python
"""
Test script for loan request APIs
"""
import sys
import os
import requests
import random
from datetime import datetime

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import utility functions
from tests.api.utils import (
    BASE_URL, get_headers, get_deposit_data, save_loan_request_data, get_loan_request_data,
    check_db_for_loan_request, check_db_for_loan, check_db_for_installments
)

def test_create_loan_request():
    """Test creating a loan request"""
    print("\n=== Testing Create Loan Request API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get deposit data
    deposit_data = get_deposit_data()
    if not deposit_data or not deposit_data.get("id"):
        print("No deposit data found. Please run test_02_deposit_management.py first.")
        return False
    
    deposit_id = deposit_data.get("id")
    
    # Prepare loan request data
    loan_request_data = {
        "deposit": deposit_id,
        "amount": 50000.00,  # Loan amount
        "installment_count": 10,  # Number of installments
        "installment_date": 15,  # Day of month for installments
        "description": "Test loan request created by API test script"
    }
    
    # API endpoint for creating a loan request
    url = f"{BASE_URL}/requests/deposit/loan-request/"
    
    try:
        # Send create loan request
        print(f"Creating loan request for deposit ID: {deposit_id}")
        response = requests.post(url, json=loan_request_data, headers=headers)
        
        # Check if loan request creation was successful
        if response.status_code == 201:
            loan_request_response = response.json()
            loan_request_id = loan_request_response.get('id')
            print(f"Loan request creation successful. Loan Request ID: {loan_request_id}")
            
            # Check if the loan request exists in the database
            if check_db_for_loan_request(loan_request_id):
                print(f"Verified: Loan request ID {loan_request_id} exists in the database.")
            else:
                print(f"Warning: Could not verify loan request ID {loan_request_id} in the database.")
            
            # Save loan request data for future tests
            loan_request_info = {
                "id": loan_request_id,
                "deposit_id": deposit_id,
                "amount": 50000.00,
                "installment_count": 10,
                "installment_date": 15,
                "description": "Test loan request created by API test script"
            }
            save_loan_request_data(loan_request_info)
            return True
        else:
            print(f"Loan request creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during loan request creation: {str(e)}")
        return False
        
    return False

def test_get_loan_requests():
    """Test getting loan requests for a deposit"""
    print("\n=== Testing Get Loan Requests API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get deposit data
    deposit_data = get_deposit_data()
    if not deposit_data or not deposit_data.get("id"):
        print("No deposit data found. Please run test_02_deposit_management.py first.")
        return False
    
    deposit_id = deposit_data.get("id")
    
    # API endpoint for getting loan requests
    url = f"{BASE_URL}/requests/deposit/{deposit_id}/loan-list/"
    
    try:
        # Send get loan requests request
        print(f"Getting loan requests for deposit ID: {deposit_id}")
        response = requests.get(url, headers=headers)
        
        # Check if request was successful
        if response.status_code == 200:
            loan_requests = response.json()
            
            if isinstance(loan_requests, list):
                print(f"Successfully retrieved {len(loan_requests)} loan requests for deposit ID: {deposit_id}")
                
                # Print some details about the loan requests
                for i, loan_request in enumerate(loan_requests):
                    print(f"Loan Request {i+1}: ID={loan_request.get('id')}, Amount={loan_request.get('amount')}, Status={loan_request.get('status')}")
                
                return True
            else:
                print("Unexpected response format. Expected a list of loan requests.")
                return False
        else:
            print(f"Get loan requests failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during get loan requests: {str(e)}")
        return False
        
    return False

def test_approve_loan_request():
    """Test approving a loan request"""
    print("\n=== Testing Approve Loan Request API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get loan request data
    loan_request_data = get_loan_request_data()
    if not loan_request_data or not loan_request_data.get("id"):
        print("No loan request data found. Please run test_create_loan_request first.")
        if not test_create_loan_request():
            return False
        loan_request_data = get_loan_request_data()
    
    loan_request_id = loan_request_data.get("id")
    
    # Prepare approval data
    approval_data = {
        "status": "approved"
    }
    
    # API endpoint for updating loan request status
    url = f"{BASE_URL}/requests/deposit/loan-request/{loan_request_id}/update/"
    
    try:
        # Send approve loan request
        print(f"Approving loan request ID: {loan_request_id}")
        response = requests.patch(url, json=approval_data, headers=headers)
        
        # Check if approval was successful
        if response.status_code == 200:
            print(f"Loan request approval successful.")
            
            # Check if a loan was created in the database
            loan_exists, loan = check_db_for_loan(loan_request_id)
            
            if loan_exists and loan:
                print(f"Verified: Loan was created for loan request ID {loan_request_id}.")
                print(f"Loan ID: {loan.id}, Amount: {loan.amount}, Status: {loan.status}")
                
                # Check if installments were created
                installments_exist, installment_count = check_db_for_installments(loan.id)
                
                if installments_exist:
                    print(f"Verified: {installment_count} installments were created for loan ID {loan.id}.")
                    return True
                else:
                    print(f"Warning: No installments found for loan ID {loan.id}.")
            else:
                print(f"Warning: No loan found for loan request ID {loan_request_id}.")
                return False
        else:
            print(f"Loan request approval failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during loan request approval: {str(e)}")
        return False
        
    return True

def test_reject_loan_request():
    """Test rejecting a loan request"""
    print("\n=== Testing Reject Loan Request API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Create a new loan request to reject
    # Get deposit data
    deposit_data = get_deposit_data()
    if not deposit_data or not deposit_data.get("id"):
        print("No deposit data found. Please run test_02_deposit_management.py first.")
        return False
    
    deposit_id = deposit_data.get("id")
    
    # Prepare loan request data
    loan_request_data = {
        "deposit": deposit_id,
        "amount": 30000.00,  # Loan amount
        "installment_count": 6,  # Number of installments
        "installment_date": 20,  # Day of month for installments
        "description": "Test loan request to be rejected"
    }
    
    # API endpoint for creating a loan request
    create_url = f"{BASE_URL}/requests/deposit/loan-request/"
    
    try:
        # Send create loan request
        print(f"Creating loan request for deposit ID: {deposit_id} (to be rejected)")
        create_response = requests.post(create_url, json=loan_request_data, headers=headers)
        
        if create_response.status_code != 201:
            print(f"Failed to create loan request for rejection test: {create_response.status_code}")
            print(create_response.text)
            return False
            
        loan_request_response = create_response.json()
        loan_request_id = loan_request_response.get('id')
        print(f"Loan request created. ID: {loan_request_id}")
        
        # Prepare rejection data
        rejection_data = {
            "status": "rejected",
            "rejection_reason": "Test rejection reason"
        }
        
        # API endpoint for updating loan request status
        update_url = f"{BASE_URL}/requests/deposit/loan-request/{loan_request_id}/update/"
        
        # Send reject loan request
        print(f"Rejecting loan request ID: {loan_request_id}")
        reject_response = requests.patch(update_url, json=rejection_data, headers=headers)
        
        # Check if rejection was successful
        if reject_response.status_code == 200:
            print(f"Loan request rejection successful.")
            
            # Verify the loan request status in the database
            from apps.request.models import LoanRequest
            loan_request = LoanRequest.objects.get(id=loan_request_id)
            
            if loan_request.status == LoanRequest.LoanStatus.REJECTED:
                print(f"Verified: Loan request ID {loan_request_id} status is 'rejected'.")
                print(f"Rejection reason: {loan_request.rejection_reason}")
                return True
            else:
                print(f"Warning: Loan request ID {loan_request_id} status is not 'rejected'.")
                return False
        else:
            print(f"Loan request rejection failed with status code: {reject_response.status_code}")
            print(reject_response.text)
            return False
            
    except Exception as e:
        print(f"Error during loan request rejection: {str(e)}")
        return False
        
    return True

def test_create_loan_request_for_deposit_14():
    """Test creating a loan request specifically for deposit ID 14"""
    print("\n=== Testing Create Loan Request for Deposit ID 14 API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # For this test, we'll use deposit ID 14 as specified in the requirements
    deposit_id = 14
    
    # Prepare loan request data
    loan_request_data = {
        "deposit": deposit_id,
        "amount": 75000.00,  # Loan amount
        "installment_count": 12,  # Number of installments
        "installment_date": 25,  # Day of month for installments
        "description": "Test loan request for deposit ID 14 created by API test script"
    }
    
    # API endpoint for creating a loan request
    url = f"{BASE_URL}/requests/deposit/loan-request/"
    
    try:
        # Send create loan request
        print(f"Creating loan request for deposit ID: {deposit_id}")
        response = requests.post(url, json=loan_request_data, headers=headers)
        
        # Check if loan request creation was successful
        if response.status_code == 201:
            loan_request_response = response.json()
            loan_request_id = loan_request_response.get('id')
            print(f"Loan request creation successful for deposit ID 14. Loan Request ID: {loan_request_id}")
            
            # Check if the loan request exists in the database
            if check_db_for_loan_request(loan_request_id):
                print(f"Verified: Loan request ID {loan_request_id} exists in the database.")
            else:
                print(f"Warning: Could not verify loan request ID {loan_request_id} in the database.")
            
            # Save loan request data for future tests
            loan_request_info = {
                "id": loan_request_id,
                "deposit_id": deposit_id,
                "amount": 75000.00,
                "installment_count": 12,
                "installment_date": 25,
                "description": "Test loan request for deposit ID 14 created by API test script"
            }
            save_loan_request_data(loan_request_info)
            return True
        else:
            print(f"Loan request creation for deposit ID 14 failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during loan request creation for deposit ID 14: {str(e)}")
        return False
        
    return False

def run_all_tests():
    """Run all loan request tests"""
    tests = [
        test_create_loan_request,
        test_get_loan_requests,
        test_approve_loan_request,
        test_reject_loan_request,
        test_create_loan_request_for_deposit_14
    ]
    
    results = []
    for test_func in tests:
        result = test_func()
        results.append(result)
        print(f"Test {test_func.__name__}: {'PASSED' if result else 'FAILED'}")
        print("-" * 50)
    
    return all(results)

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
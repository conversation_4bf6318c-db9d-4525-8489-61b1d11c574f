#!/usr/bin/env python
"""
Test script for creating a deposit
"""
import sys
import os
import requests
import random
import string
from datetime import datetime, timedelta

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import utility functions
from tests.api.utils import BASE_URL, get_headers, save_deposit_data, get_user_data

def generate_random_string(length=8):
    """Generate a random string of fixed length"""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def test_create_deposit():
    """Test creating a poll deposit"""
    print("\n=== Testing Create Deposit API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Generate random data for deposit
    random_suffix = generate_random_string()
    title = f"Test Poll Deposit {random_suffix}"
    description = f"This is a test poll deposit created by the API test script {random_suffix}"
    
    # Set the initial lottery date to 30 days from now
    initial_lottery_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
    
    # Prepare deposit data
    deposit_data = {
        "title": title,
        "description": description,
        "total_debt_amount": 100000.00,
        "lottery_month_count": 10,
        "unit_amount": 10000.00,
        "payment_cycle": 15,  # Day of month for payment
        "max_unit_per_request": 5,
        "max_members_count": 20,
        "initial_lottery_date": initial_lottery_date,
        "rules": [
            {"subject": "Rule 1", "description": "Description for Rule 1"},
            {"subject": "Rule 2", "description": "Description for Rule 2"}
        ]
    }
    
    # API endpoint for creating a poll deposit
    url = f"{BASE_URL}/request/deposit/create/poll-deposit/"
    
    try:
        # Send create deposit request
        print(f"Creating deposit: {title}")
        response = requests.post(url, json=deposit_data, headers=headers)
        
        # Check if deposit creation was successful
        if response.status_code == 201:
            deposit_response = response.json()
            deposit_id = deposit_response.get('id')
            print(f"Deposit creation successful. Deposit ID: {deposit_id}")
            
            # Save deposit data for future tests
            deposit_info = {
                "id": deposit_id,
                "title": title,
                "description": description,
                "total_debt_amount": 100000.00,
                "unit_amount": 10000.00,
                "payment_cycle": 15,
                "max_unit_per_request": 5,
                "max_members_count": 20
            }
            save_deposit_data(deposit_info)
            return True
        else:
            print(f"Deposit creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during deposit creation: {str(e)}")
        return False
        
    return False

if __name__ == "__main__":
    success = test_create_deposit()
    sys.exit(0 if success else 1)#!/usr/bin/env python
"""
Test script for creating a deposit
"""
import sys
import os
import requests
import random
from datetime import datetime, timedelta

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import utility functions
from tests.api.utils import BASE_URL, get_headers, get_user_data, save_deposit_data

def test_create_deposit():
    """Test creating a poll deposit"""
    print("\n=== Testing Deposit Creation API ===")
    
    # Get user data from previous test
    user_data = get_user_data()
    if not user_data:
        print("User data not found. Please run the user registration test first.")
        return False
    
    # Generate random data for deposit
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    title = f"Test Poll Deposit {timestamp}"
    
    # Set initial lottery date to 30 days from now
    initial_lottery_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
    
    # Prepare deposit data for a Poll deposit
    deposit_data = {
        "deposit_type": "Poll",
        "title": title,
        "description": "This is a test poll deposit created by API test script",
        "region": 1,  # Assuming region ID 1 exists
        "total_debt_amount": 10000000,  # 10 million
        "lottery_month_count": 10,
        "unit_amount": 1000000,  # 1 million per unit
        "payment_cycle": 1,  # Monthly
        "max_unit_per_request": 5,
        "max_members_count": 20,
        "initial_lottery_date": initial_lottery_date,
        "rules": {
            "late_payment_penalty": 0.1,
            "early_payment_reward": 0.05
        }
    }
    
    # API endpoint for creating a poll deposit
    url = f"{BASE_URL}/deposit/poll-deposit/"
    
    try:
        # Send deposit creation request
        print(f"Creating deposit with title: {title}")
        response = requests.post(url, json=deposit_data, headers=get_headers())
        
        # Check if deposit creation was successful
        if response.status_code == 201:
            created_deposit = response.json()
            print(f"Deposit creation successful. Deposit ID: {created_deposit.get('id')}")
            
            # Save deposit data for future tests
            save_deposit_data(created_deposit)
            return True
        else:
            print(f"Deposit creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during deposit creation: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_create_deposit()
    sys.exit(0 if success else 1) 
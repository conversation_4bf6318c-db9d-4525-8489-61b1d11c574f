#!/usr/bin/env python
"""
Script to generate sample data files for API tests
"""
import os
import sys
import json
from pathlib import Path

# Setup Django environment
BASE_DIR = Path(__file__).resolve().parent.parent.parent
# Add the project root directory to the Python path
sys.path.insert(0, str(BASE_DIR))

# Set environment variables that might be needed by Django settings
os.environ.setdefault('DJANGO_ALLOWED_HOSTS', 'localhost,127.0.0.1')
os.environ.setdefault('DEBUG', 'True')
os.environ.setdefault('SECRET_KEY', 'django-insecure-test-key-for-development')
os.environ.setdefault('DATABASE_URL', 'sqlite:///db.sqlite3')

# Set the Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.base')
print(f'Using settings module: {os.environ["DJANGO_SETTINGS_MODULE"]}')

import django
django.setup()

# Import Django models
from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token
from apps.account.models import User
from apps.deposit.models import Deposit, DepositMembership
from apps.request.models import LoanRequest
from apps.loan.models import Loan
from apps.ticket.models import Ticket
from apps.voting.models import VotingPoll

def generate_token_file():
    """Generate token.json file with a valid token"""
    print("Generating token.json...")
    
    # Try to get an existing user
    user = User.objects.filter(is_active=True).first()
    
    if not user:
        # If no user exists, create one
        print("No active user found. Creating a new user...")
        user = User.objects.create(
            phone_number="+************",
            fullname="Test User",
            is_active=True
        )
        user.set_password("Test@123456")
        user.save()
    
    # Get or create token for the user
    token, created = Token.objects.get_or_create(user=user)
    
    # Save token to file
    with open("tests/api/token.json", 'w') as f:
        json.dump({"token": token.key}, f)
    
    print(f"Token saved for user: {user.fullname} (ID: {user.id})")
    return user, token.key

def generate_user_data_file(user, token):
    """Generate user_data.json file with user data"""
    print("Generating user_data.json...")
    
    user_data = {
        "id": user.id,
        "fullname": user.fullname,
        "phone_number": str(user.phone_number),
        "token": token,
        "password": "Test@123456"  # Default password for test user
    }
    
    # Save user data to file
    with open("tests/api/user_data.json", 'w') as f:
        json.dump(user_data, f)
    
    print(f"User data saved for user: {user.fullname} (ID: {user.id})")
    return user_data

def generate_deposit_data_file(user):
    """Generate deposit_data.json file with deposit data"""
    print("Generating deposit_data.json...")
    
    # Try to get an existing deposit
    deposit = Deposit.objects.filter(is_active=True).first()
    
    if not deposit:
        # If no deposit exists, try to find one where the user is a member
        memberships = DepositMembership.objects.filter(user=user, is_active=True)
        if memberships.exists():
            deposit = memberships.first().deposit
    
    if not deposit:
        # If still no deposit, create one
        print("No active deposit found. Creating a new deposit...")
        deposit = Deposit.objects.create(
            deposit_type=Deposit.DepositType.POLL,
            owner=user,
            title="Test Deposit",
            description="Test deposit for API tests",
            total_debt_amount=100000.00,
            unit_amount=10000.00,
            payment_cycle=15,
            max_unit_per_request=5,
            max_members_count=20,
            is_active=True
        )
    
    # Save deposit data to file
    deposit_data = {
        "id": deposit.id,
        "title": deposit.title,
        "description": deposit.description,
        "type": deposit.deposit_type,
        "total_debt_amount": float(deposit.total_debt_amount) if deposit.total_debt_amount else 0,
        "unit_amount": float(deposit.unit_amount),
        "payment_cycle": deposit.payment_cycle,
        "max_unit_per_request": deposit.max_unit_per_request,
        "max_members_count": deposit.max_members_count
    }
    
    with open("tests/api/deposit_data.json", 'w') as f:
        json.dump(deposit_data, f)
    
    print(f"Deposit data saved for deposit: {deposit.title} (ID: {deposit.id})")
    
    # Make sure the user is a member of this deposit
    membership, created = DepositMembership.objects.get_or_create(
        user=user,
        deposit=deposit,
        defaults={
            "role": DepositMembership.Role.MEMBER,
            "requested_unit_count": 2,
            "monthly_installment_amount": float(deposit.unit_amount) * 2,
            "is_active": True
        }
    )
    
    if created:
        print(f"Created membership for user in deposit ID: {deposit.id}")
    else:
        print(f"User is already a member of deposit ID: {deposit.id}")
    
    return deposit_data

def generate_loan_request_data_file(user, deposit_data):
    """Generate loan_request_data.json file with loan request data"""
    print("Generating loan_request_data.json...")
    
    # Try to get an existing loan request
    loan_request = LoanRequest.objects.filter(user=user).first()
    
    if not loan_request:
        # If no loan request exists, create one
        print("No loan request found. Creating a new loan request...")
        loan_request = LoanRequest.objects.create(
            deposit_id=deposit_data["id"],
            user=user,
            status=LoanRequest.LoanStatus.PENDING,
            amount=50000.00,
            installment_count=10,
            installment_date=15,
            description="Test loan request for API tests"
        )
    
    # Save loan request data to file
    loan_request_data = {
        "id": loan_request.id,
        "deposit_id": loan_request.deposit.id,
        "amount": float(loan_request.amount),
        "installment_count": loan_request.installment_count,
        "installment_date": loan_request.installment_date,
        "description": loan_request.description,
        "status": loan_request.status
    }
    
    with open("tests/api/loan_request_data.json", 'w') as f:
        json.dump(loan_request_data, f)
    
    print(f"Loan request data saved for loan request ID: {loan_request.id}")
    return loan_request_data

def generate_ticket_data_file(user):
    """Generate ticket_data.json file with ticket data"""
    print("Generating ticket_data.json...")
    
    # Try to get an existing ticket
    ticket = Ticket.objects.filter(user=user).first()
    
    if not ticket:
        # If no ticket exists, create one
        print("No ticket found. Creating a new ticket...")
        try:
            ticket = Ticket.objects.create(
                user=user,
                subject="Test Ticket",
                status="open"
            )
        except Exception as e:
            print(f"Error creating ticket: {str(e)}")
            # Create a minimal ticket data
            ticket_data = {
                "id": 1,
                "subject": "Test Ticket",
                "message": "Test ticket message"
            }
            with open("tests/api/ticket_data.json", 'w') as f:
                json.dump(ticket_data, f)
            print("Created minimal ticket data")
            return ticket_data
    
    # Save ticket data to file
    ticket_data = {
        "id": ticket.id,
        "subject": ticket.subject,
    }
    
    with open("tests/api/ticket_data.json", 'w') as f:
        json.dump(ticket_data, f)
    
    print(f"Ticket data saved for ticket ID: {ticket.id}")
    return ticket_data

def generate_voting_data_file(deposit_data):
    """Generate voting_data.json file with voting data"""
    print("Generating voting_data.json...")
    
    # Try to get an existing voting poll
    voting_poll = VotingPoll.objects.first()
    
    if not voting_poll:
        # If no voting poll exists, create minimal data
        print("No voting poll found. Creating minimal voting data...")
        voting_data = {
            "id": 1,
            "deposit_id": deposit_data["id"],
            "title": "Test Voting Poll",
            "description": "Test voting poll for API tests",
            "end_date": "2023-12-31"
        }
    else:
        # Save voting poll data to file
        voting_data = {
            "id": voting_poll.id,
            "deposit_id": voting_poll.deposit.id,
            "title": voting_poll.title,
            "description": voting_poll.description,
            "end_date": voting_poll.end_date.strftime("%Y-%m-%d") if hasattr(voting_poll, 'end_date') and voting_poll.end_date else "2023-12-31"
        }
    
    with open("tests/api/voting_data.json", 'w') as f:
        json.dump(voting_data, f)
    
    print(f"Voting data saved")
    return voting_data

def main():
    """Generate all sample data files"""
    try:
        # Create the directory if it doesn't exist
        os.makedirs("tests/api", exist_ok=True)
        
        # Generate token and user data
        user, token = generate_token_file()
        user_data = generate_user_data_file(user, token)
        
        # Generate deposit data
        deposit_data = generate_deposit_data_file(user)
        
        # Generate loan request data
        loan_request_data = generate_loan_request_data_file(user, deposit_data)
        
        # Generate ticket data
        ticket_data = generate_ticket_data_file(user)
        
        # Generate voting data
        voting_data = generate_voting_data_file(deposit_data)
        
        print("\nAll sample data files generated successfully!")
        
    except Exception as e:
        print(f"Error generating sample data: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
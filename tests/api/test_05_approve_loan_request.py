#!/usr/bin/env python
"""
Test script for approving a loan request and checking loan creation
"""
import sys
import os
import requests
import random
import string
from datetime import datetime

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import utility functions
from tests.api.utils import (
    BASE_URL, get_headers, get_loan_request_data,
    check_db_for_loan, check_db_for_installments
)

def test_approve_loan_request():
    """Test approving a loan request and checking loan creation"""
    print("\n=== Testing Approve Loan Request API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Get loan request data
    loan_request_data = get_loan_request_data()
    if not loan_request_data or not loan_request_data.get("id"):
        print("No loan request data found. Please run test_04_create_loan_request.py first.")
        return False
    
    loan_request_id = loan_request_data.get("id")
    
    # Prepare approval data
    approval_data = {
        "status": "approved"
    }
    
    # API endpoint for updating loan request status
    url = f"{BASE_URL}/request/deposit/loan-request/{loan_request_id}/update/"
    
    try:
        # Send approve loan request
        print(f"Approving loan request ID: {loan_request_id}")
        response = requests.patch(url, json=approval_data, headers=headers)
        
        # Check if approval was successful
        if response.status_code == 200:
            print(f"Loan request approval successful.")
            
            # Check if a loan was created in the database
            loan_exists, loan = check_db_for_loan(loan_request_id)
            
            if loan_exists and loan:
                print(f"Verified: Loan was created for loan request ID {loan_request_id}.")
                print(f"Loan ID: {loan.id}, Amount: {loan.amount}, Status: {loan.status}")
                
                # Check if installments were created
                installments_exist, installment_count = check_db_for_installments(loan.id)
                
                if installments_exist:
                    print(f"Verified: {installment_count} installments were created for loan ID {loan.id}.")
                    return True
                else:
                    print(f"Warning: No installments found for loan ID {loan.id}.")
            else:
                print(f"Warning: No loan found for loan request ID {loan_request_id}.")
                return False
        else:
            print(f"Loan request approval failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during loan request approval: {str(e)}")
        return False
        
    return True

def test_reject_loan_request():
    """Test rejecting a loan request"""
    print("\n=== Testing Reject Loan Request API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Create a new loan request to reject
    # Prepare loan request data
    deposit_id = 14  # Using deposit ID 14 as specified
    
    loan_request_data = {
        "deposit": deposit_id,
        "amount": 30000.00,  # Loan amount
        "installment_count": 6,  # Number of installments
        "installment_date": 20,  # Day of month for installments
        "description": "Test loan request to be rejected"
    }
    
    # API endpoint for creating a loan request
    create_url = f"{BASE_URL}/request/deposit/loan-request/"
    
    try:
        # Send create loan request
        print(f"Creating loan request for deposit ID: {deposit_id} (to be rejected)")
        create_response = requests.post(create_url, json=loan_request_data, headers=headers)
        
        if create_response.status_code != 201:
            print(f"Failed to create loan request for rejection test: {create_response.status_code}")
            print(create_response.text)
            return False
            
        loan_request_response = create_response.json()
        loan_request_id = loan_request_response.get('id')
        print(f"Loan request created. ID: {loan_request_id}")
        
        # Prepare rejection data
        rejection_data = {
            "status": "rejected",
            "rejection_reason": "Test rejection reason"
        }
        
        # API endpoint for updating loan request status
        update_url = f"{BASE_URL}/request/deposit/loan-request/{loan_request_id}/update/"
        
        # Send reject loan request
        print(f"Rejecting loan request ID: {loan_request_id}")
        reject_response = requests.patch(update_url, json=rejection_data, headers=headers)
        
        # Check if rejection was successful
        if reject_response.status_code == 200:
            print(f"Loan request rejection successful.")
            
            # Verify the loan request status in the database
            from apps.request.models import LoanRequest
            loan_request = LoanRequest.objects.get(id=loan_request_id)
            
            if loan_request.status == LoanRequest.LoanStatus.REJECTED:
                print(f"Verified: Loan request ID {loan_request_id} status is 'rejected'.")
                print(f"Rejection reason: {loan_request.rejection_reason}")
                return True
            else:
                print(f"Warning: Loan request ID {loan_request_id} status is not 'rejected'.")
                return False
        else:
            print(f"Loan request rejection failed with status code: {reject_response.status_code}")
            print(reject_response.text)
            return False
            
    except Exception as e:
        print(f"Error during loan request rejection: {str(e)}")
        return False
        
    return True

if __name__ == "__main__":
    approve_success = test_approve_loan_request()
    reject_success = test_reject_loan_request()
    sys.exit(0 if (approve_success and reject_success) else 1)
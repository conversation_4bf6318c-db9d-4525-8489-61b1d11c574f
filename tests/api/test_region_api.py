#!/usr/bin/env python
"""
Test script to verify the region API functionality.
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/home/<USER>/Desktop/main/habbib/qatreh/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from apps.region.models import Region, UserRegion
from apps.account.models import User
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token
from apps.region.serializers import RegionListSerializer, ChangeUserRegionSerializer

def test_region_serializers():
    """Test that region serializers work correctly"""
    print("Testing region serializers...")
    
    try:
        # Test RegionListSerializer
        region_serializer = RegionListSerializer()
        fields = region_serializer.fields
        
        # Check that required fields exist
        required_fields = ['id', 'name', 'description', 'invitation_code', 'members_count', 'is_current_user_region', 'created_at']
        for field in required_fields:
            assert field in fields, f"Required field '{field}' missing from RegionListSerializer"
        
        print("✅ RegionListSerializer has all required fields")
        
        # Test ChangeUserRegionSerializer
        change_serializer = ChangeUserRegionSerializer()
        change_fields = change_serializer.fields
        
        assert 'region_id' in change_fields, "region_id field missing from ChangeUserRegionSerializer"
        print("✅ ChangeUserRegionSerializer has required fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Serializer test failed: {e}")
        return False

def test_region_models():
    """Test that region models work correctly"""
    print("\nTesting region models...")
    
    try:
        # Create a test region
        region = Region.objects.create(
            name='تست ریجن',
            description='این یک ریجن تست است'
        )
        
        # Check that invitation code is generated
        assert region.invitation_code is not None, "Invitation code should be generated automatically"
        assert region.invitation_code.startswith('REGION-'), "Invitation code should start with 'REGION-'"
        
        print("✅ Region model works correctly")
        
        # Create a test user
        user = User.objects.create_user(
            phone_number='+989123456780',
            fullname='کاربر تست',
            password='testpass123'
        )
        
        # Create UserRegion
        user_region = UserRegion.objects.create(
            user=user,
            region=region,
            is_active=True
        )
        
        # Check that invitation code is generated for user
        assert user_region.invitation_code is not None, "User invitation code should be generated"
        
        print("✅ UserRegion model works correctly")
        
        # Test members_count property
        members_count = region.members_count
        assert members_count == 1, f"Expected 1 member, got {members_count}"
        
        print("✅ Region members_count property works correctly")
        
        # Clean up
        user_region.delete()
        user.delete()
        region.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

def test_api_structure():
    """Test that API structure includes region endpoints"""
    print("\nTesting API structure...")
    
    try:
        from apps.api.views.documentation import CustomAPIDocumentationView
        
        doc_view = CustomAPIDocumentationView()
        api_structure = doc_view._get_api_structure()
        
        # Check that region section exists
        assert 'region' in api_structure, "region section missing from API structure"
        
        region_section = api_structure['region']
        assert region_section['name'] == 'Region Management', "Wrong region section name"
        
        # Check endpoints
        endpoints = region_section['endpoints']
        endpoint_names = [ep['name'] for ep in endpoints]
        
        assert 'Region List' in endpoint_names, "Region List endpoint missing"
        assert 'Change User Region' in endpoint_names, "Change User Region endpoint missing"
        
        print("✅ API structure includes region endpoints")
        
        # Check endpoint details
        region_list_endpoint = next(ep for ep in endpoints if ep['name'] == 'Region List')
        assert region_list_endpoint['method'] == 'GET', "Region List should be GET method"
        assert region_list_endpoint['url'] == '/api/region/list/', "Wrong URL for Region List"
        
        change_region_endpoint = next(ep for ep in endpoints if ep['name'] == 'Change User Region')
        assert change_region_endpoint['method'] == 'POST', "Change User Region should be POST method"
        assert change_region_endpoint['url'] == '/api/region/change/', "Wrong URL for Change User Region"
        
        print("✅ Region endpoints have correct details")
        
        return True
        
    except Exception as e:
        print(f"❌ API structure test failed: {e}")
        return False

def test_urls_configuration():
    """Test that URLs are properly configured"""
    print("\nTesting URLs configuration...")
    
    try:
        from django.urls import reverse
        
        # Test that URLs can be resolved
        try:
            region_list_url = reverse('region:region-list')
            print(f"✅ Region list URL resolved: {region_list_url}")
        except Exception as e:
            print(f"❌ Region list URL resolution failed: {e}")
            return False
        
        try:
            change_region_url = reverse('region:change-user-region')
            print(f"✅ Change region URL resolved: {change_region_url}")
        except Exception as e:
            print(f"❌ Change region URL resolution failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ URLs configuration test failed: {e}")
        return False

def test_view_imports():
    """Test that views can be imported correctly"""
    print("\nTesting view imports...")
    
    try:
        from apps.region.views import RegionListAPIView, ChangeUserRegionAPIView
        from apps.region.serializers import RegionListSerializer, ChangeUserRegionSerializer
        from apps.region.docs import doc_region_list, doc_change_user_region
        
        # Check that views have correct attributes
        assert hasattr(RegionListAPIView, 'queryset'), "RegionListAPIView missing queryset"
        assert hasattr(RegionListAPIView, 'serializer_class'), "RegionListAPIView missing serializer_class"
        assert hasattr(RegionListAPIView, 'permission_classes'), "RegionListAPIView missing permission_classes"
        
        assert hasattr(ChangeUserRegionAPIView, 'serializer_class'), "ChangeUserRegionAPIView missing serializer_class"
        assert hasattr(ChangeUserRegionAPIView, 'permission_classes'), "ChangeUserRegionAPIView missing permission_classes"
        
        print("✅ All views and dependencies imported successfully")
        
        # Test documentation functions
        region_list_doc = doc_region_list()
        change_region_doc = doc_change_user_region()
        
        assert isinstance(region_list_doc, str), "doc_region_list should return string"
        assert isinstance(change_region_doc, str), "doc_change_user_region should return string"
        assert len(region_list_doc) > 100, "doc_region_list should have substantial content"
        assert len(change_region_doc) > 100, "doc_change_user_region should have substantial content"
        
        print("✅ Documentation functions work correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ View imports test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Starting region API tests...\n")
    
    try:
        # Run tests
        test1 = test_region_serializers()
        test2 = test_region_models()
        test3 = test_api_structure()
        test4 = test_urls_configuration()
        test5 = test_view_imports()
        
        if test1 and test2 and test3 and test4 and test5:
            print("\n🎉 All region API tests passed successfully!")
            print("\n📋 Summary of implemented features:")
            print("✅ RegionListAPIView - GET /api/region/list/")
            print("   - Lists all regions with user membership status")
            print("   - Shows is_current_user_region field")
            print("   - Includes members_count for each region")
            print("✅ ChangeUserRegionAPIView - POST /api/region/change/")
            print("   - Changes user's region membership")
            print("   - Creates new membership if user has none")
            print("   - Updates existing membership if user already has one")
            print("✅ Complete documentation in apps/region/docs.py")
            print("✅ API structure updated with region endpoints")
            print("✅ URLs properly configured and accessible")
            print("✅ Template automatically includes region endpoints in sidebar")
            print("\n📖 Usage:")
            print("   GET /api/region/list/ - List all regions")
            print("   POST /api/region/change/ - Change user region")
            print("   Body: {\"region_id\": 1}")
        else:
            print("\n❌ Some tests failed")
            sys.exit(1)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

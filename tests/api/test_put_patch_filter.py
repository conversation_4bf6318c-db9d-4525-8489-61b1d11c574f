#!/usr/bin/env python3

# Test script to verify PUT/PATCH filtering logic

import json
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/Desktop/main/habbib/qatreh/backend')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.base')
import django
django.setup()

from apps.api.views.swagger_documentation import SwaggerBasedDocumentationView

def test_put_patch_filtering():
    """Test that PUT is filtered out when both PUT and PATCH exist"""
    
    # Sample Swagger JSON with both PUT and PATCH
    sample_swagger_data = {
        "paths": {
            "/deposits/media/{deposit_media_id}/update/": {
                "put": {
                    "operationId": "deposits_media_update_update",
                    "description": "PUT method description",
                    "parameters": [],
                    "responses": {"200": {"description": ""}},
                    "tags": ["deposits"]
                },
                "patch": {
                    "operationId": "deposits_media_update_partial_update",
                    "summary": "Update Deposit Media",
                    "description": "PATCH method description",
                    "parameters": [
                        {
                            "name": "data",
                            "in": "body",
                            "required": True,
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "title": {
                                        "description": "Updated media title",
                                        "type": "string",
                                        "example": "Updated Deposit Photos"
                                    }
                                }
                            }
                        }
                    ],
                    "responses": {"200": {"description": "Media updated successfully"}},
                    "tags": ["Deposit"]
                },
                "parameters": [
                    {
                        "name": "deposit_media_id",
                        "in": "path",
                        "required": True,
                        "type": "string"
                    }
                ]
            }
        }
    }
    
    # Create view instance
    view = SwaggerBasedDocumentationView()
    
    # Parse the swagger data
    api_structure = view.parse_swagger_data(sample_swagger_data)
    
    print("🔍 Testing PUT/PATCH Filtering")
    print("=" * 50)
    
    # Check results
    deposit_endpoints = api_structure.get('Deposit', {}).get('endpoints', [])
    
    print(f"📊 Found {len(deposit_endpoints)} endpoints in Deposit app:")
    
    methods_found = []
    for endpoint in deposit_endpoints:
        method = endpoint.get('method')
        methods_found.append(method)
        print(f"   - {method}: {endpoint.get('name')}")
        print(f"     Description: {endpoint.get('description')}")
        print(f"     Parameters: {len(endpoint.get('parameters', []))}")
    
    print("\n" + "=" * 50)
    print("✅ Test Results:")
    
    # Verify that only PATCH is present, not PUT
    if 'PATCH' in methods_found and 'PUT' not in methods_found:
        print("✅ SUCCESS: Only PATCH method found (PUT was filtered out)")
    elif 'PUT' in methods_found and 'PATCH' in methods_found:
        print("❌ FAILED: Both PUT and PATCH found (PUT should be filtered)")
    elif 'PUT' in methods_found and 'PATCH' not in methods_found:
        print("⚠️  WARNING: Only PUT found (PATCH missing)")
    else:
        print("❌ FAILED: Neither PUT nor PATCH found")
    
    print(f"Methods found: {methods_found}")
    
    return api_structure

if __name__ == "__main__":
    test_put_patch_filtering()

#!/usr/bin/env python
"""
Test Swagger improvement after fixing decorators
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/home/<USER>/Desktop/main/habbib/qatreh/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

def test_swagger_improvement():
    """Test if Swagger decorators improvement worked"""
    print("Testing Swagger improvement...")
    
    try:
        from apps.api.views.swagger_documentation import SwaggerBasedDocumentationView
        from django.test import RequestFactory
        
        # Create mock request
        factory = RequestFactory()
        request = factory.get('/docs/')
        
        # Get view response
        view = SwaggerBasedDocumentationView()
        
        # Try to get real Swagger JSON (this might work now)
        print("Trying to get Swagger JSON...")
        swagger_data = view.get_swagger_json(request)
        
        # Check if we got real Swagger data
        if 'info' in swagger_data and swagger_data.get('paths'):
            print("✅ Got real Swagger JSON!")
            paths_count = len(swagger_data.get('paths', {}))
            print(f"   Paths count: {paths_count}")
            
            # Check invitation endpoint specifically
            paths = swagger_data.get('paths', {})
            invitation_path = None
            
            for path, methods in paths.items():
                if 'invitaion' in path:
                    invitation_path = path
                    break
            
            if invitation_path:
                print(f"✅ Found invitation path: {invitation_path}")
                
                # Check POST method
                post_method = paths[invitation_path].get('post', {})
                if post_method:
                    print("✅ POST method found")
                    
                    # Check request body
                    request_body = post_method.get('requestBody', {})
                    if request_body:
                        print("✅ Request body defined")
                        content = request_body.get('content', {})
                        if 'application/json' in content:
                            schema = content['application/json'].get('schema', {})
                            properties = schema.get('properties', {})
                            print(f"   Request properties: {list(properties.keys())}")
                            
                            if 'invitation_code' in properties:
                                print("✅ invitation_code parameter found in Swagger!")
                            else:
                                print("❌ invitation_code parameter missing")
                    
                    # Check responses
                    responses = post_method.get('responses', {})
                    print(f"   Response codes: {list(responses.keys())}")
                    
                    # Check 200 response
                    success_response = responses.get('200', {})
                    if success_response:
                        content = success_response.get('content', {})
                        if 'application/json' in content:
                            schema = content['application/json'].get('schema', {})
                            properties = schema.get('properties', {})
                            print(f"   Success response properties: {list(properties.keys())}")
                            
                            if 'is_active' in properties and 'is_member' in properties:
                                print("✅ is_active and is_member found in response!")
                            else:
                                print("❌ is_active or is_member missing from response")
                else:
                    print("❌ POST method not found")
            else:
                print("❌ Invitation path not found")
                print("Available paths:")
                for path in list(paths.keys())[:5]:
                    print(f"   - {path}")
        else:
            print("❌ Using fallback generated data")
        
        # Test our documentation view
        print("\nTesting documentation view...")
        api_structure = view.parse_swagger_data(swagger_data)
        
        # Find invitation endpoint
        account_app = api_structure.get('account', {})
        if account_app:
            endpoints = account_app.get('endpoints', [])
            
            for endpoint in endpoints:
                if 'invitaion' in endpoint.get('url', ''):
                    print(f"✅ Invitation endpoint found in documentation")
                    print(f"   Name: {endpoint.get('name')}")
                    print(f"   Description: {endpoint.get('description')}")
                    
                    parameters = endpoint.get('parameters', [])
                    print(f"   Parameters ({len(parameters)}):")
                    for param in parameters:
                        print(f"     - {param.get('name')} ({param.get('type')}): {param.get('description')}")
                    
                    response_examples = endpoint.get('response_examples', {})
                    print(f"   Response examples: {list(response_examples.keys())}")
                    
                    # Check success response
                    success_response = response_examples.get('success', '')
                    if 'is_active' in success_response and 'is_member' in success_response:
                        print("✅ Success response includes is_active and is_member")
                    else:
                        print("❌ Success response missing is_active or is_member")
                    
                    break
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Testing Swagger improvement after decorator fixes...")
    print("=" * 60)
    
    success = test_swagger_improvement()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SWAGGER IMPROVEMENT TEST COMPLETED!")
        print("\n💡 Next steps:")
        print("1. Start Django server: python manage.py runserver")
        print("2. Visit: http://127.0.0.1:8000/en/docs/")
        print("3. Check invitation endpoint parameters and responses")
        print("4. Should now show proper invitation_code parameter")
        print("5. Should show proper response with is_active and is_member")
    else:
        print("❌ SWAGGER IMPROVEMENT TEST FAILED!")
    print("=" * 60)

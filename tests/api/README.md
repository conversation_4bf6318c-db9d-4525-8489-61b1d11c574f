# API Test Scripts

This directory contains API test scripts for testing various functionalities of the backend system.

## Overview

The test scripts are designed to be run in sequence, as each test depends on the data created by previous tests. The scripts use the `requests` library to make HTTP requests to the API endpoints and verify the responses.

## Test Scripts

1. `test_01_user_registration.py` - Tests user registration, verification, login, profile, and password recovery
2. `test_02_deposit_management.py` - Tests creating different types of deposits, getting deposit lists and details, joining deposits, and getting deposit members
3. `test_03_loan_requests.py` - Tests creating loan requests, getting loan requests, approving and rejecting loan requests
4. `test_04_lottery.py` - Tests getting lottery information, winners, creating lotteries, and deleting lottery winners
5. `test_05_tickets.py` - Tests creating tickets, getting tickets, getting ticket messages, and creating ticket messages
6. `test_06_voting.py` - Tests creating voting polls, getting voting polls, getting voting poll details, voting on polls, and getting voting reports
7. `test_07_issues.py` - Tests creating issue reports

## Running the Tests

### Prerequisites

- Python 3.6+
- Django project dependencies installed
- Backend server running at `http://localhost:8000` (or update `BASE_URL` in `utils.py`)

### Running Individual Tests

You can run each test script individually:

```bash
cd /backend
python tests/api/test_01_user_registration.py
```

### Running All Tests

To run all tests in sequence:

```bash
cd /backend
python tests/api/run_all_tests.py
```

## Test Data

The test scripts store data in JSON files in the `tests/api` directory:

- `token.json` - Authentication token
- `user_data.json` - User data
- `deposit_data.json` - Deposit data
- `loan_request_data.json` - Loan request data
- `ticket_data.json` - Ticket data
- `voting_data.json` - Voting poll data

## Notes

- The tests are designed to be idempotent, meaning they can be run multiple times without causing issues.
- Some tests may fail if the data they depend on doesn't exist or has been modified.
- For the loan request test, we specifically include a test for deposit ID 14 as required.
- The tests use Django ORM directly for some operations that would normally require admin privileges.
- Some tests might fail due to business logic constraints (e.g., a user can't join a deposit twice), but the scripts handle these cases gracefully.

## Troubleshooting

If you encounter issues:

1. Make sure the backend server is running
2. Check that the `BASE_URL` in `utils.py` is correct
3. Delete the JSON data files to start fresh
4. Run the tests in sequence starting from `test_01_user_registration.py`# API Test Scripts

This directory contains API test scripts for testing various functionalities of the backend system.

## Overview

The test scripts are designed to be run in sequence, as each test depends on the data created by previous tests. The scripts use the `requests` library to make HTTP requests to the API endpoints and verify the responses.

## Test Scripts

1. `test_01_user_registration.py` - Tests user registration and login
2. `test_02_create_deposit.py` - Tests creating a deposit
3. `test_03_join_deposit.py` - Tests joining a deposit
4. `test_04_create_loan_request.py` - Tests creating a loan request for deposit ID 14
5. `test_05_approve_loan_request.py` - Tests approving and rejecting loan requests

## Running the Tests

### Prerequisites

- Python 3.6+
- Django project dependencies installed
- Backend server running at `http://localhost:8000` (or update `BASE_URL` in `utils.py`)

### Running Individual Tests

You can run each test script individually:

```bash
cd /backend
python tests/api/test_01_user_registration.py
```

### Running All Tests

To run all tests in sequence:

```bash
cd /backend
python tests/api/run_all_tests.py
```

## Test Data

The test scripts store data in JSON files in the `tests/api` directory:

- `token.json` - Authentication token
- `user_data.json` - User data
- `deposit_data.json` - Deposit data
- `loan_request_data.json` - Loan request data

## Notes

- The tests are designed to be idempotent, meaning they can be run multiple times without causing issues.
- Some tests may fail if the data they depend on doesn't exist or has been modified.
- For the loan request test, we specifically use deposit ID 14 as required.
- The tests use Django ORM directly for some operations that would normally require admin privileges.
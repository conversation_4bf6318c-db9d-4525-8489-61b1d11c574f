#!/usr/bin/env python
"""
Test script for issues APIs
"""
import sys
import os
import requests
import random
from datetime import datetime

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import utility functions
from tests.api.utils import (
    BASE_URL, get_headers, generate_random_string
)

def test_create_issue_report():
    """Test creating an issue report"""
    print("\n=== Testing Create Issue Report API ===")
    
    # Get authentication headers
    headers = get_headers()
    if not headers.get("Authorization"):
        print("No authentication token found. Please run test_01_user_registration.py first.")
        return False
    
    # Generate random data for issue report
    random_suffix = generate_random_string()
    title = f"Test Issue Report {random_suffix}"
    description = f"This is a test issue report created by the API test script {random_suffix}"
    
    # Prepare issue report data
    issue_data = {
        "title": title,
        "description": description,
        "app_version": "1.0.0",
        "device_info": "Test Device",
        "os_version": "Test OS 1.0"
    }
    
    # API endpoint for creating an issue report
    url = f"{BASE_URL}/issues/create/"
    
    try:
        # Send create issue report request
        print(f"Creating issue report: {title}")
        response = requests.post(url, json=issue_data, headers=headers)
        
        # Check if issue report creation was successful
        if response.status_code == 201:
            issue_response = response.json()
            issue_id = issue_response.get('id')
            print(f"Issue report creation successful. Issue ID: {issue_id}")
            
            # Verify the issue report in the database
            from apps.issues.models import IssueReport
            issue = IssueReport.objects.filter(id=issue_id).first()
            
            if issue:
                print(f"Verified: Issue report ID {issue_id} exists in the database.")
                print(f"Title: {issue.title}")
                print(f"Description: {issue.description}")
                return True
            else:
                print(f"Warning: Could not verify issue report ID {issue_id} in the database.")
                return False
        else:
            print(f"Issue report creation failed with status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Error during issue report creation: {str(e)}")
        return False
        
    return False

def run_all_tests():
    """Run all issue tests"""
    tests = [
        test_create_issue_report
    ]
    
    results = []
    for test_func in tests:
        result = test_func()
        results.append(result)
        print(f"Test {test_func.__name__}: {'PASSED' if result else 'FAILED'}")
        print("-" * 50)
    
    return all(results)

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
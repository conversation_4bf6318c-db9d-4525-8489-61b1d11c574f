#!/usr/bin/env python
"""
Test edge cases for region-deposit filtering
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/home/<USER>/Desktop/main/habbib/qatreh/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.region.models import Region, UserRegion
from apps.deposit.models import Deposit
from apps.account.models import User
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token
import random

def test_edge_cases():
    """Test edge cases for region-deposit filtering"""
    print("🔍 Testing edge cases...")
    
    try:
        # Case 1: User with no region membership
        print("\n📍 Case 1: User with no region membership...")
        phone_suffix = random.randint(1000, 9999)
        user_no_region = User.objects.create_user(
            phone_number=f'+********{phone_suffix}',
            fullname='کاربر بدون اقلیم',
            password='testpass123'
        )
        
        client = APIClient()
        token, created = Token.objects.get_or_create(user=user_no_region)
        client.credentials(HTTP_AUTHORIZATION='Token ' + token.key)
        
        # Test deposits API - should return empty list
        response = client.get('/api/deposits/')
        if response.status_code == 200:
            data = response.json()
            deposits = data.get('results', data) if 'results' in data else data
            assert len(deposits) == 0, f"Expected 0 deposits for user with no region, got {len(deposits)}"
            print("✅ User with no region gets empty deposit list")
        
        # Case 2: User with inactive region membership
        print("\n📍 Case 2: User with inactive region membership...")
        region = Region.objects.create(
            name='اقلیم غیرفعال',
            description='اقلیم تست غیرفعال'
        )
        
        phone_suffix = random.randint(1000, 9999)
        user_inactive = User.objects.create_user(
            phone_number=f'+98912301{phone_suffix}',
            fullname='کاربر غیرفعال',
            password='testpass123'
        )
        
        # Create inactive region membership
        UserRegion.objects.create(
            user=user_inactive,
            region=region,
            is_active=False,  # Inactive
            is_current=True
        )
        
        client2 = APIClient()
        token2, created = Token.objects.get_or_create(user=user_inactive)
        client2.credentials(HTTP_AUTHORIZATION='Token ' + token2.key)
        
        # Test deposits API
        response2 = client2.get('/api/deposits/')
        if response2.status_code == 200:
            data2 = response2.json()
            deposits2 = data2.get('results', data2) if 'results' in data2 else data2
            # Should still filter by region even if membership is inactive
            assert len(deposits2) == 0, f"Expected 0 deposits for inactive membership, got {len(deposits2)}"
            print("✅ User with inactive region membership handled correctly")
        
        # Case 3: Multiple deposits in same region
        print("\n📍 Case 3: Multiple deposits in same region...")
        region2 = Region.objects.create(
            name='اقلیم چندصندوق',
            description='اقلیم با چند صندوق'
        )
        
        phone_suffix = random.randint(1000, 9999)
        user_multi = User.objects.create_user(
            phone_number=f'+98912302{phone_suffix}',
            fullname='کاربر چندصندوق',
            password='testpass123'
        )
        
        UserRegion.objects.create(
            user=user_multi,
            region=region2,
            is_active=True,
            is_current=True
        )
        
        # Create multiple deposits in same region
        deposit1 = Deposit.objects.create(
            owner=user_multi,
            region=region2,
            title='صندوق اول',
            description='صندوق اول در اقلیم',
            deposit_type='Poll',
            unit_amount=100000,
            payment_cycle=1,
            max_unit_per_request=10,
            max_members_count=20,
            total_debt_amount=1000000,
            lottery_month_count=10
        )
        
        deposit2 = Deposit.objects.create(
            owner=user_multi,
            region=region2,
            title='صندوق دوم',
            description='صندوق دوم در اقلیم',
            deposit_type='Saving',
            unit_amount=200000,
            payment_cycle=1,
            max_unit_per_request=10,
            max_members_count=20
        )
        
        client3 = APIClient()
        token3, created = Token.objects.get_or_create(user=user_multi)
        client3.credentials(HTTP_AUTHORIZATION='Token ' + token3.key)
        
        response3 = client3.get('/api/deposits/')
        if response3.status_code == 200:
            data3 = response3.json()
            deposits3 = data3.get('results', data3) if 'results' in data3 else data3
            assert len(deposits3) == 2, f"Expected 2 deposits in same region, got {len(deposits3)}"
            deposit_titles = [d['title'] for d in deposits3]
            assert 'صندوق اول' in deposit_titles, "First deposit not found"
            assert 'صندوق دوم' in deposit_titles, "Second deposit not found"
            print("✅ Multiple deposits in same region shown correctly")
        
        # Case 4: Inactive deposits should not be shown
        print("\n📍 Case 4: Inactive deposits should not be shown...")
        deposit1.is_active = False
        deposit1.save()
        
        response4 = client3.get('/api/deposits/')
        if response4.status_code == 200:
            data4 = response4.json()
            deposits4 = data4.get('results', data4) if 'results' in data4 else data4
            assert len(deposits4) == 1, f"Expected 1 active deposit, got {len(deposits4)}"
            assert deposits4[0]['title'] == 'صندوق دوم', f"Expected 'صندوق دوم', got {deposits4[0]['title']}"
            print("✅ Inactive deposits are filtered out correctly")
        
        # Clean up
        print("\n🧹 Cleaning up...")
        deposit1.delete()
        deposit2.delete()
        user_no_region.delete()
        user_inactive.delete()
        user_multi.delete()
        region.delete()
        region2.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 Starting edge cases test...\n")
    
    success = test_edge_cases()
    
    if success:
        print("\n🎉 All edge cases test PASSED!")
        print("\n📋 Verified edge cases:")
        print("✅ User with no region membership gets empty deposit list")
        print("✅ User with inactive region membership handled correctly")
        print("✅ Multiple deposits in same region shown correctly")
        print("✅ Inactive deposits are filtered out")
        print("\n🔧 The deposit filtering system is robust and handles all edge cases!")
    else:
        print("\n❌ Edge cases test FAILED")
        sys.exit(1)
"""
Final comprehensive test for region owner invitation permissions
Run with: python manage.py shell < test_final_region_owner.py
"""

from django.contrib.auth import get_user_model
from apps.region.models import Region, UserRegion, InvitationLink
from apps.account.serializers.user import UserProfileSerializer
from apps.deposit.models import Deposit, DepositMembership
import random

User = get_user_model()

def test_scenario(scenario_name, test_func):
    print(f"\n🧪 {scenario_name}")
    print("=" * 50)
    try:
        result = test_func()
        if result:
            print(f"✅ {scenario_name}: PASSED")
        else:
            print(f"❌ {scenario_name}: FAILED")
        return result
    except Exception as e:
        print(f"💥 {scenario_name}: ERROR - {str(e)}")
        return False

def test_regular_user():
    """Test regular user without any special permissions"""
    phone_number = f'+**********{random.randint(10, 99)}'
    user = User.objects.create_user(
        phone_number=phone_number,
        fullname='Regular User',
        password='testpass123'
    )
    
    serializer = UserProfileSerializer(user)
    can_create = serializer.get_can_create_invitation_link(user)
    
    print(f"Regular user can create invitation: {can_create}")
    
    user.delete()
    return not can_create  # Should be False

def test_region_owner_only():
    """Test user who only owns a region (no deposits)"""
    phone_number = f'+**********{random.randint(10, 99)}'
    user = User.objects.create_user(
        phone_number=phone_number,
        fullname='Region Owner Only',
        password='testpass123'
    )
    
    region = Region.objects.create(
        name=f'Test Region {random.randint(100, 999)}',
        description='Test region for owner-only test',
        owner=user
    )
    
    serializer = UserProfileSerializer(user)
    can_create = serializer.get_can_create_invitation_link(user)
    
    print(f"Region owner can create invitation: {can_create}")
    
    user.delete()
    region.delete()
    return can_create  # Should be True

def test_deposit_owner_only():
    """Test user who only owns a deposit (no regions)"""
    phone_number = f'+**********{random.randint(10, 99)}'
    user = User.objects.create_user(
        phone_number=phone_number,
        fullname='Deposit Owner Only',
        password='testpass123'
    )
    
    deposit = Deposit.objects.create(
        title=f'Test Deposit {random.randint(100, 999)}',
        description='Test deposit for owner-only test',
        owner=user,
        unit_amount=100000,
        max_members_count=10,
        lottery_month_count=10,
        lottery_per_month_count=1,
        is_active=True
    )
    
    serializer = UserProfileSerializer(user)
    can_create = serializer.get_can_create_invitation_link(user)
    
    print(f"Deposit owner can create invitation: {can_create}")
    
    user.delete()
    deposit.delete()
    return can_create  # Should be True

def test_region_and_deposit_owner():
    """Test user who owns both region and deposit"""
    phone_number = f'+**********{random.randint(10, 99)}'
    user = User.objects.create_user(
        phone_number=phone_number,
        fullname='Region and Deposit Owner',
        password='testpass123'
    )
    
    region = Region.objects.create(
        name=f'Test Region {random.randint(100, 999)}',
        description='Test region for combined test',
        owner=user
    )
    
    deposit = Deposit.objects.create(
        title=f'Test Deposit {random.randint(100, 999)}',
        description='Test deposit for combined test',
        owner=user,
        unit_amount=100000,
        max_members_count=10,
        lottery_month_count=10,
        lottery_per_month_count=1,
        is_active=True
    )
    
    serializer = UserProfileSerializer(user)
    can_create = serializer.get_can_create_invitation_link(user)
    
    print(f"Region and deposit owner can create invitation: {can_create}")
    
    user.delete()
    region.delete()
    deposit.delete()
    return can_create  # Should be True

def test_deposit_admin():
    """Test user who is admin of a deposit"""
    # Create deposit owner
    owner_phone = f'+**********{random.randint(10, 99)}'
    owner = User.objects.create_user(
        phone_number=owner_phone,
        fullname='Deposit Owner',
        password='testpass123'
    )
    
    # Create admin user
    admin_phone = f'+**********{random.randint(10, 99)}'
    admin_user = User.objects.create_user(
        phone_number=admin_phone,
        fullname='Deposit Admin',
        password='testpass123'
    )
    
    deposit = Deposit.objects.create(
        title=f'Test Deposit {random.randint(100, 999)}',
        description='Test deposit for admin test',
        owner=owner,
        unit_amount=100000,
        max_members_count=10,
        lottery_month_count=10,
        lottery_per_month_count=1,
        is_active=True
    )
    
    # Create admin membership
    DepositMembership.objects.create(
        user=admin_user,
        deposit=deposit,
        role=DepositMembership.Role.ADMIN,
        is_active=True,
        requested_unit_count=1
    )
    
    serializer = UserProfileSerializer(admin_user)
    can_create = serializer.get_can_create_invitation_link(admin_user)
    
    print(f"Deposit admin can create invitation: {can_create}")
    
    owner.delete()
    admin_user.delete()
    deposit.delete()
    return can_create  # Should be True

# Run all tests
print("🚀 Starting comprehensive region owner invitation tests...")

results = []
results.append(test_scenario("Regular User (No Permissions)", test_regular_user))
results.append(test_scenario("Region Owner Only", test_region_owner_only))
results.append(test_scenario("Deposit Owner Only", test_deposit_owner_only))
results.append(test_scenario("Region and Deposit Owner", test_region_and_deposit_owner))
results.append(test_scenario("Deposit Admin", test_deposit_admin))

print(f"\n📊 Final Results:")
print("=" * 50)
passed = sum(results)
total = len(results)
print(f"Passed: {passed}/{total}")

if passed == total:
    print("🎉 All tests passed! Region owners can create invitation links.")
else:
    print("💥 Some tests failed. Please check the implementation.")

print("\n✨ Test Summary:")
print("- Regular users: Cannot create invitation links")
print("- Region owners: Can create invitation links")
print("- Deposit owners: Can create invitation links")
print("- Deposit admins: Can create invitation links")
print("- Combined owners: Can create invitation links")
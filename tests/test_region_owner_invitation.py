#!/usr/bin/env python3
"""
Test script to verify that region owners can create invitation links
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.append('/home/<USER>/Desktop/main/habbib/qatreh/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.region.models import Region, UserRegion, InvitationLink
from apps.account.serializers.user import UserProfileSerializer

User = get_user_model()

def test_region_owner_invitation_permission():
    """Test that region owners can create invitation links"""
    
    print("🧪 Testing region owner invitation link permissions...")
    
    # Create a test user
    test_user = User.objects.create_user(
        phone_number='+************',
        fullname='Test Region Owner',
        password='testpass123'
    )
    
    # Create a region with this user as owner
    test_region = Region.objects.create(
        name='Test Region',
        description='A test region for testing owner permissions',
        owner=test_user
    )
    
    print(f"✅ Created test user: {test_user.fullname}")
    print(f"✅ Created test region: {test_region.name} with owner: {test_user.fullname}")
    
    # Test the serializer method
    serializer = UserProfileSerializer(test_user)
    can_create_invitation = serializer.get_can_create_invitation_link(test_user)
    
    print(f"🔍 Can create invitation link (serializer): {can_create_invitation}")
    
    if can_create_invitation:
        print("✅ SUCCESS: Region owner can create invitation links according to serializer")
    else:
        print("❌ FAILED: Region owner cannot create invitation links according to serializer")
    
    # Check if user has any region memberships
    memberships = test_user.region_memberships.all()
    print(f"🔍 User has {memberships.count()} region memberships")
    
    for membership in memberships:
        print(f"   - Region: {membership.region.name}, Active: {membership.is_active}, Current: {membership.is_current}")
    
    # Check owned regions
    owned_regions = test_user.owned_regions.all()
    print(f"🔍 User owns {owned_regions.count()} regions")
    
    for region in owned_regions:
        print(f"   - Owned region: {region.name}")
    
    # Clean up
    test_user.delete()
    test_region.delete()
    
    return can_create_invitation

def test_region_owner_with_membership():
    """Test region owner who also has a UserRegion membership"""
    
    print("\n🧪 Testing region owner with UserRegion membership...")
    
    # Create a test user
    test_user = User.objects.create_user(
        phone_number='+989123456788',
        fullname='Test Region Owner with Membership',
        password='testpass123'
    )
    
    # Create a region with this user as owner
    test_region = Region.objects.create(
        name='Test Region 2',
        description='A test region for testing owner with membership',
        owner=test_user
    )
    
    # Create UserRegion membership for the owner
    user_region = UserRegion.objects.create(
        user=test_user,
        region=test_region,
        is_active=True,
        is_current=True
    )
    
    print(f"✅ Created test user: {test_user.fullname}")
    print(f"✅ Created test region: {test_region.name} with owner: {test_user.fullname}")
    print(f"✅ Created UserRegion membership: Active={user_region.is_active}, Current={user_region.is_current}")
    
    # Test the serializer method
    serializer = UserProfileSerializer(test_user)
    can_create_invitation = serializer.get_can_create_invitation_link(test_user)
    
    print(f"🔍 Can create invitation link (serializer): {can_create_invitation}")
    
    if can_create_invitation:
        print("✅ SUCCESS: Region owner with membership can create invitation links")
    else:
        print("❌ FAILED: Region owner with membership cannot create invitation links")
    
    # Clean up
    test_user.delete()
    test_region.delete()
    
    return can_create_invitation

if __name__ == '__main__':
    print("🚀 Starting region owner invitation link tests...\n")
    
    # Test 1: Region owner without UserRegion membership
    result1 = test_region_owner_invitation_permission()
    
    # Test 2: Region owner with UserRegion membership
    result2 = test_region_owner_with_membership()
    
    print(f"\n📊 Test Results:")
    print(f"   - Region owner without membership: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"   - Region owner with membership: {'✅ PASS' if result2 else '❌ FAIL'}")
    
    if result1 and result2:
        print("\n🎉 All tests passed! Region owners can create invitation links.")
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
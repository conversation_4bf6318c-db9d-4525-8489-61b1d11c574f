"""
Test API endpoint for region owner invitation creation
Run with: python manage.py shell < test_region_owner_api.py
"""

from django.contrib.auth import get_user_model
from django.test import RequestFactory
from apps.region.models import Region, UserRegion, InvitationLink
from apps.region.views import CreateInvitationLinkAPIView
from rest_framework.test import force_authenticate
import random

User = get_user_model()

print("🧪 Testing region owner invitation link API...")

# Create a test user with unique phone number
phone_number = f'+9891234567{random.randint(10, 99)}'
test_user = User.objects.create_user(
    phone_number=phone_number,
    fullname='Test Region Owner API',
    password='testpass123'
)

# Create a region with this user as owner
test_region = Region.objects.create(
    name='Test Region API',
    description='A test region for testing owner API permissions',
    owner=test_user
)

print(f"✅ Created test user: {test_user.fullname}")
print(f"✅ Created test region: {test_region.name} with owner: {test_user.fullname}")

# Check if user has current region membership before API call
current_membership = test_user.current_region_membership
print(f"🔍 User has current region membership: {current_membership is not None}")

# Test the API endpoint
factory = RequestFactory()
request = factory.post('/api/region/create-invitation-link/')
request.user = test_user  # Set user directly

view = CreateInvitationLinkAPIView()
response = view.post(request)

print(f"🔍 API Response Status: {response.status_code}")
print(f"🔍 API Response Data: {response.data}")

if response.status_code == 201:
    print("✅ SUCCESS: Region owner can create invitation links via API")
    
    # Check if UserRegion was created
    user_regions = test_user.region_memberships.all()
    print(f"🔍 User now has {user_regions.count()} region memberships")
    
    for membership in user_regions:
        print(f"   - Region: {membership.region.name}, Active: {membership.is_active}, Current: {membership.is_current}")
    
    # Check if invitation link was created
    invitation_links = InvitationLink.objects.filter(user_region__user=test_user)
    print(f"🔍 User has {invitation_links.count()} invitation links")
    
    for link in invitation_links:
        print(f"   - Invitation code: {link.invitation_code}, Used: {link.is_used}")
        
else:
    print("❌ FAILED: Region owner cannot create invitation links via API")

# Clean up
test_user.delete()
test_region.delete()

print("🧹 Cleaned up test data")
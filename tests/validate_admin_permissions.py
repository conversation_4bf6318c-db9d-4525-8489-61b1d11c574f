#!/usr/bin/env python
"""
Validation script for admin permission system.
Tests the region-based access control for admin panel.
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.admin.sites import AdminSite
from apps.region.models import Region
from apps.deposit.models import Deposit
from utils.admin import RegionFilteredAdmin, SuperAdminOnlyAdmin

User = get_user_model()


class MockRequest:
    """Mock request object for testing admin permissions"""
    def __init__(self, user):
        self.user = user


def test_admin_permissions():
    """Test admin permission system"""
    print("🔧 Testing Admin Permission System")
    print("=" * 50)
    
    # Test admin class instantiation
    print("1. Testing admin class instantiation...")
    try:
        region_admin = RegionFilteredAdmin(Deposit, AdminSite())
        superadmin_admin = SuperAdminOnlyAdmin(Region, AdminSite())
        print("   ✅ Admin classes instantiated successfully")
    except Exception as e:
        print(f"   ❌ Failed to instantiate admin classes: {e}")
        return False
    
    # Test superuser permissions
    print("\n2. Testing superuser permissions...")
    superuser = User(is_superuser=True)
    request = MockRequest(superuser)
    
    try:
        # Test SuperAdminOnlyAdmin
        assert superadmin_admin.has_module_permission(request) == True
        assert superadmin_admin.has_view_permission(request) == True
        assert superadmin_admin.has_change_permission(request) == True
        assert superadmin_admin.has_add_permission(request) == True
        assert superadmin_admin.has_delete_permission(request) == True
        
        # Test RegionFilteredAdmin
        assert region_admin.has_module_permission(request) == True
        assert region_admin.has_view_permission(request) == True
        assert region_admin.has_change_permission(request) == True
        assert region_admin.has_add_permission(request) == True
        assert region_admin.has_delete_permission(request) == True
        
        print("   ✅ Superuser has all permissions")
    except AssertionError:
        print("   ❌ Superuser permission test failed")
        return False
    
    # Test regular user permissions
    print("\n3. Testing regular user permissions...")
    regular_user = User(is_superuser=False)
    request = MockRequest(regular_user)
    
    try:
        # Test SuperAdminOnlyAdmin - should deny all access
        assert superadmin_admin.has_module_permission(request) == False
        assert superadmin_admin.has_view_permission(request) == False
        assert superadmin_admin.has_change_permission(request) == False
        assert superadmin_admin.has_add_permission(request) == False
        assert superadmin_admin.has_delete_permission(request) == False
        
        # Test RegionFilteredAdmin - should deny access for users without regions
        assert region_admin.has_module_permission(request) == False
        
        print("   ✅ Regular user permissions correctly restricted")
    except AssertionError:
        print("   ❌ Regular user permission test failed")
        return False
    
    # Test region owner permissions (mock)
    print("\n4. Testing region owner permissions...")
    region_owner = User(is_superuser=False)
    # Mock the owned_regions relationship
    class MockRegions:
        def exists(self):
            return True
        def all(self):
            return [Region(id=1, name="Test Region")]
    
    region_owner.owned_regions = MockRegions()
    request = MockRequest(region_owner)
    
    try:
        # Test RegionFilteredAdmin - should allow access for region owners
        assert region_admin.has_module_permission(request) == True
        assert region_admin.has_view_permission(request) == True
        assert region_admin.has_change_permission(request) == True
        assert region_admin.has_add_permission(request) == True
        assert region_admin.has_delete_permission(request) == True
        
        # Test SuperAdminOnlyAdmin - should still deny access
        assert superadmin_admin.has_module_permission(request) == False
        
        print("   ✅ Region owner permissions work correctly")
    except AssertionError:
        print("   ❌ Region owner permission test failed")
        return False
    
    print("\n🎉 All admin permission tests passed!")
    print("=" * 50)
    print("✅ Region-based admin permission system is working correctly")
    print("✅ Superusers have full access to all models")
    print("✅ Region owners can access region-related models")
    print("✅ Regular users are properly restricted")
    print("✅ Superadmin-only models are hidden from region owners")
    
    return True


def test_admin_registrations():
    """Test that admin classes are properly registered"""
    print("\n🔧 Testing Admin Registrations")
    print("=" * 50)
    
    from utils.admin import project_admin_site
    
    # Check if models are registered
    registered_models = project_admin_site._registry.keys()
    model_names = [model.__name__ for model in registered_models]
    
    print(f"Registered models: {len(model_names)}")
    for name in sorted(model_names):
        print(f"  - {name}")
    
    # Check specific models
    expected_models = ['Deposit', 'Region', 'Ticket', 'User']
    found_models = []
    
    for model in registered_models:
        if model.__name__ in expected_models:
            found_models.append(model.__name__)
    
    print(f"\nFound expected models: {found_models}")
    
    if len(found_models) >= 3:  # At least 3 of the 4 expected models
        print("✅ Admin registrations look good")
        return True
    else:
        print("❌ Some expected models are not registered")
        return False


if __name__ == "__main__":
    success = True
    
    try:
        success &= test_admin_permissions()
        success &= test_admin_registrations()
    except Exception as e:
        print(f"❌ Validation failed with error: {e}")
        success = False
    
    if success:
        print("\n🎉 All validation tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some validation tests failed!")
        sys.exit(1)

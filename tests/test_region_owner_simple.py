"""
Simple test for region owner invitation permissions
Run with: python manage.py shell < test_region_owner_simple.py
"""

from django.contrib.auth import get_user_model
from apps.region.models import Region, UserRegion, InvitationLink
from apps.account.serializers.user import UserProfileSerializer

User = get_user_model()

print("🧪 Testing region owner invitation link permissions...")

# Create a test user with unique phone number
import random
phone_number = f'+**********{random.randint(10, 99)}'
test_user = User.objects.create_user(
    phone_number=phone_number,
    fullname='Test Region Owner',
    password='testpass123'
)

# Create a region with this user as owner
test_region = Region.objects.create(
    name='Test Region',
    description='A test region for testing owner permissions',
    owner=test_user
)

print(f"✅ Created test user: {test_user.fullname}")
print(f"✅ Created test region: {test_region.name} with owner: {test_user.fullname}")

# Test the serializer method
serializer = UserProfileSerializer(test_user)
can_create_invitation = serializer.get_can_create_invitation_link(test_user)

print(f"🔍 Can create invitation link (serializer): {can_create_invitation}")

if can_create_invitation:
    print("✅ SUCCESS: Region owner can create invitation links according to serializer")
else:
    print("❌ FAILED: Region owner cannot create invitation links according to serializer")

# Check owned regions
owned_regions = test_user.owned_regions.all()
print(f"🔍 User owns {owned_regions.count()} regions")

for region in owned_regions:
    print(f"   - Owned region: {region.name}")

# Clean up
test_user.delete()
test_region.delete()

print("🧹 Cleaned up test data")
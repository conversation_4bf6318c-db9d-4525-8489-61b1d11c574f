"""
Test cases for admin permission system.
Tests region-based access control for admin panel.
"""
import pytest
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.contrib.admin.sites import AdminSite
from django.contrib.messages.storage.fallback import FallbackStorage

from apps.region.models import Region, UserRegion
from apps.deposit.models import Deposit
from apps.ticket.models import Ticket
from apps.loan.models import Loan
from apps.payment.models import Payment
from apps.transaction.models import Transaction
from apps.voting.models import VotingPoll
from apps.lottery.models import DepositLottery
from apps.issues.models import IssueReport

from utils.admin import RegionFilteredAdmin, SuperAdminOnlyAdmin

User = get_user_model()


class MockRequest:
    """Mock request object for testing admin permissions"""
    def __init__(self, user):
        self.user = user
        self.META = {}
        self.session = {}
        self._messages = FallbackStorage(self)


class AdminPermissionTestCase(TestCase):
    """Base test case for admin permissions"""
    
    def setUp(self):
        """Set up test data"""
        # Create superuser
        self.superuser = User.objects.create_superuser(
            email='<EMAIL>',
            fullname='Super Admin',
            phone_number='+989123456789',
            password='testpass123'
        )
        
        # Create region owners
        self.region_owner1 = User.objects.create_user(
            email='<EMAIL>',
            fullname='Region Owner 1',
            phone_number='+989123456788',
            password='testpass123'
        )
        self.region_owner1.is_staff = True
        self.region_owner1.save()
        
        self.region_owner2 = User.objects.create_user(
            email='<EMAIL>',
            fullname='Region Owner 2',
            phone_number='+989123456787',
            password='testpass123'
        )
        self.region_owner2.is_staff = True
        self.region_owner2.save()
        
        # Create regular user
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Regular User',
            phone_number='+989123456786',
            password='testpass123'
        )
        
        # Create regions
        self.region1 = Region.objects.create(
            name='Region 1',
            description='Test Region 1',
            owner=self.region_owner1
        )
        
        self.region2 = Region.objects.create(
            name='Region 2',
            description='Test Region 2',
            owner=self.region_owner2
        )
        
        # Create deposits
        self.deposit1 = Deposit.objects.create(
            title='Deposit 1',
            description='Test Deposit 1',
            owner=self.region_owner1,
            region=self.region1,
            deposit_type='Poll',
            unit_amount=100000,
            payment_cycle='Monthly',
            max_members_count=10
        )
        
        self.deposit2 = Deposit.objects.create(
            title='Deposit 2',
            description='Test Deposit 2',
            owner=self.region_owner2,
            region=self.region2,
            deposit_type='Poll',
            unit_amount=200000,
            payment_cycle='Monthly',
            max_members_count=15
        )
        
        # Create tickets
        self.ticket1 = Ticket.objects.create(
            subject='Ticket 1',
            description='Test Ticket 1',
            user=self.region_owner1,
            region=self.region1,
            deposit=self.deposit1
        )
        
        self.ticket2 = Ticket.objects.create(
            subject='Ticket 2',
            description='Test Ticket 2',
            user=self.region_owner2,
            region=self.region2,
            deposit=self.deposit2
        )
        
        # Mock admin classes
        self.region_filtered_admin = RegionFilteredAdmin(Deposit, AdminSite())
        self.superadmin_only_admin = SuperAdminOnlyAdmin(Region, AdminSite())


class TestRegionOwnerPermissions(AdminPermissionTestCase):
    """Test permissions for region owners"""
    
    def test_region_owner_has_module_permission(self):
        """Test that region owners can see modules"""
        request = MockRequest(self.region_owner1)
        self.assertTrue(self.region_filtered_admin.has_module_permission(request))
    
    def test_regular_user_no_module_permission(self):
        """Test that regular users cannot see modules"""
        request = MockRequest(self.regular_user)
        self.assertFalse(self.region_filtered_admin.has_module_permission(request))
    
    def test_superuser_has_all_permissions(self):
        """Test that superusers have all permissions"""
        request = MockRequest(self.superuser)
        self.assertTrue(self.region_filtered_admin.has_module_permission(request))
        self.assertTrue(self.region_filtered_admin.has_view_permission(request))
        self.assertTrue(self.region_filtered_admin.has_change_permission(request))
        self.assertTrue(self.region_filtered_admin.has_add_permission(request))
        self.assertTrue(self.region_filtered_admin.has_delete_permission(request))
    
    def test_region_owner_can_access_own_objects(self):
        """Test that region owners can access objects in their region"""
        request = MockRequest(self.region_owner1)
        self.assertTrue(self.region_filtered_admin.has_view_permission(request, self.deposit1))
        self.assertTrue(self.region_filtered_admin.has_change_permission(request, self.deposit1))
        self.assertTrue(self.region_filtered_admin.has_delete_permission(request, self.deposit1))
    
    def test_region_owner_cannot_access_other_objects(self):
        """Test that region owners cannot access objects from other regions"""
        request = MockRequest(self.region_owner1)
        self.assertFalse(self.region_filtered_admin.has_view_permission(request, self.deposit2))
        self.assertFalse(self.region_filtered_admin.has_change_permission(request, self.deposit2))
        self.assertFalse(self.region_filtered_admin.has_delete_permission(request, self.deposit2))
    
    def test_queryset_filtering_for_region_owner(self):
        """Test that querysets are properly filtered for region owners"""
        request = MockRequest(self.region_owner1)
        qs = self.region_filtered_admin.get_queryset(request)
        
        # Should only see deposits from their region
        self.assertIn(self.deposit1, qs)
        self.assertNotIn(self.deposit2, qs)
    
    def test_queryset_empty_for_regular_user(self):
        """Test that regular users get empty querysets"""
        request = MockRequest(self.regular_user)
        qs = self.region_filtered_admin.get_queryset(request)
        self.assertEqual(qs.count(), 0)


class TestSuperAdminOnlyPermissions(AdminPermissionTestCase):
    """Test permissions for superadmin-only models"""
    
    def test_superuser_has_access(self):
        """Test that superusers can access superadmin-only models"""
        request = MockRequest(self.superuser)
        self.assertTrue(self.superadmin_only_admin.has_module_permission(request))
        self.assertTrue(self.superadmin_only_admin.has_view_permission(request))
        self.assertTrue(self.superadmin_only_admin.has_change_permission(request))
        self.assertTrue(self.superadmin_only_admin.has_add_permission(request))
        self.assertTrue(self.superadmin_only_admin.has_delete_permission(request))
    
    def test_region_owner_no_access(self):
        """Test that region owners cannot access superadmin-only models"""
        request = MockRequest(self.region_owner1)
        self.assertFalse(self.superadmin_only_admin.has_module_permission(request))
        self.assertFalse(self.superadmin_only_admin.has_view_permission(request))
        self.assertFalse(self.superadmin_only_admin.has_change_permission(request))
        self.assertFalse(self.superadmin_only_admin.has_add_permission(request))
        self.assertFalse(self.superadmin_only_admin.has_delete_permission(request))
    
    def test_regular_user_no_access(self):
        """Test that regular users cannot access superadmin-only models"""
        request = MockRequest(self.regular_user)
        self.assertFalse(self.superadmin_only_admin.has_module_permission(request))
        self.assertFalse(self.superadmin_only_admin.has_view_permission(request))
        self.assertFalse(self.superadmin_only_admin.has_change_permission(request))
        self.assertFalse(self.superadmin_only_admin.has_add_permission(request))
        self.assertFalse(self.superadmin_only_admin.has_delete_permission(request))


class TestForeignKeyFiltering(AdminPermissionTestCase):
    """Test foreign key filtering in admin forms"""
    
    def test_region_filtering_for_region_owner(self):
        """Test that region choices are filtered for region owners"""
        request = MockRequest(self.region_owner1)
        
        # Mock a foreign key field for region
        class MockField:
            name = 'region'
            related_model = Region
        
        field = MockField()
        formfield = self.region_filtered_admin.formfield_for_foreignkey(field, request)
        
        # Should only see their own region
        available_regions = list(formfield.queryset.all())
        self.assertIn(self.region1, available_regions)
        self.assertNotIn(self.region2, available_regions)
    
    def test_deposit_filtering_for_region_owner(self):
        """Test that deposit choices are filtered for region owners"""
        request = MockRequest(self.region_owner1)
        
        # Mock a foreign key field for deposit
        class MockField:
            name = 'deposit'
            related_model = Deposit
        
        field = MockField()
        formfield = self.region_filtered_admin.formfield_for_foreignkey(field, request)
        
        # Should only see deposits from their region
        available_deposits = list(formfield.queryset.all())
        self.assertIn(self.deposit1, available_deposits)
        self.assertNotIn(self.deposit2, available_deposits)


if __name__ == '__main__':
    pytest.main([__file__])

#!/usr/bin/env python
"""
Test script to verify that deposit filtering by region works correctly.
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/home/<USER>/Desktop/main/habbib/qatreh/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.region.models import Region, UserRegion
from apps.deposit.models import Deposit
from apps.account.models import User
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token

def test_deposit_region_filtering():
    """Test that deposits are filtered by user's current region"""
    print("Testing deposit region filtering...")
    
    try:
        # Create test regions
        region1 = Region.objects.create(
            name='اقلیم ۱',
            description='اقلیم تست ۱'
        )
        
        region2 = Region.objects.create(
            name='اقلیم ۲', 
            description='اقلیم تست ۲'
        )
        
        # Create test user
        user = User.objects.create_user(
            phone_number='+************',
            fullname='کاربر تست',
            password='testpass123'
        )
        
        # Create user regions
        user_region1 = UserRegion.objects.create(
            user=user,
            region=region1,
            is_active=True,
            is_current=True  # Set region1 as current
        )
        
        user_region2 = UserRegion.objects.create(
            user=user,
            region=region2,
            is_active=True,
            is_current=False
        )
        
        # Create deposits in different regions
        deposit1 = Deposit.objects.create(
            owner=user,
            region=region1,
            title='صندوق تست ۱',
            description='صندوق در اقلیم ۱',
            deposit_type='Poll',
            unit_amount=100000,
            payment_cycle=1,
            max_unit_per_request=10,
            max_members_count=20,
            total_debt_amount=1000000,
            lottery_month_count=10
        )
        
        deposit2 = Deposit.objects.create(
            owner=user,
            region=region2,
            title='صندوق تست ۲',
            description='صندوق در اقلیم ۲',
            deposit_type='Poll',
            unit_amount=200000,
            payment_cycle=1,
            max_unit_per_request=10,
            max_members_count=20,
            total_debt_amount=2000000,
            lottery_month_count=10
        )
        
        # Test user's current region
        current_region = user.user_region
        print(f"User's current region: {current_region.name if current_region else 'None'}")
        assert current_region == region1, f"Expected region1, got {current_region}"
        
        # Test API client
        client = APIClient()
        token, created = Token.objects.get_or_create(user=user)
        client.credentials(HTTP_AUTHORIZATION='Token ' + token.key)
        
        # Test deposit list API
        response = client.get('/api/deposits/')
        print(f"API Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            deposits = data.get('results', data) if 'results' in data else data
            print(f"Number of deposits returned: {len(deposits)}")
            
            # Should only return deposits from region1
            assert len(deposits) == 1, f"Expected 1 deposit, got {len(deposits)}"
            assert deposits[0]['title'] == 'صندوق تست ۱', f"Expected 'صندوق تست ۱', got {deposits[0]['title']}"
            
            print("✅ Deposit filtering by region works correctly")
            
            # Now change user's region to region2
            user.set_current_region(region2)
            
            # Test again
            response = client.get('/api/deposits/')
            if response.status_code == 200:
                data = response.json()
                deposits = data.get('results', data) if 'results' in data else data
                print(f"After region change - Number of deposits returned: {len(deposits)}")
                
                # Should now return deposits from region2
                assert len(deposits) == 1, f"Expected 1 deposit after region change, got {len(deposits)}"
                assert deposits[0]['title'] == 'صندوق تست ۲', f"Expected 'صندوق تست ۲', got {deposits[0]['title']}"
                
                print("✅ Deposit filtering after region change works correctly")
            else:
                print(f"❌ API call failed after region change: {response.status_code}")
                print(f"Response: {response.content}")
                return False
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"Response: {response.content}")
            return False
        
        # Clean up
        deposit1.delete()
        deposit2.delete()
        user_region1.delete()
        user_region2.delete()
        user.delete()
        region1.delete()
        region2.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Starting deposit region filtering test...\n")
    
    success = test_deposit_region_filtering()
    
    if success:
        print("\n🎉 Deposit region filtering test passed!")
        print("\n📋 Summary:")
        print("✅ Deposits are correctly filtered by user's current region")
        print("✅ When user changes region, deposit list updates accordingly")
        print("✅ Only deposits from current region are shown")
    else:
        print("\n❌ Test failed")
        sys.exit(1)
#!/bin/bash

# Script to check static files in production container
# Usage: ./check_static_files.sh

echo "🔍 Checking static files in production container..."

# Check if container is running
if ! docker ps | grep -q "qatreh_web"; then
    echo "❌ qatreh_web container is not running!"
    echo "Please start the container first with: docker-compose -f docker-compose.prod.yml up -d"
    exit 1
fi

echo "✅ Container is running"

# Check static files in container
echo "📁 Checking static files directory structure:"
docker exec qatreh_web ls -la /usr/src/app/static/

echo ""
echo "📁 Checking staticfiles directory:"
docker exec qatreh_web ls -la /usr/src/app/static/staticfiles/ | head -10

echo ""
echo "🎨 Checking landing page files:"
docker exec qatreh_web ls -la /usr/src/app/static/staticfiles/landing_page/ 2>/dev/null || echo "❌ Landing page directory not found!"

echo ""
echo "📊 Landing page subdirectories:"
docker exec qatreh_web find /usr/src/app/static/staticfiles/landing_page/ -type d 2>/dev/null || echo "❌ No landing page subdirectories found!"

echo ""
echo "🖼️ Sample landing page files:"
docker exec qatreh_web find /usr/src/app/static/staticfiles/landing_page/ -name "*.css" -o -name "*.js" -o -name "*.png" | head -10 2>/dev/null || echo "❌ No landing page assets found!"

echo ""
echo "📈 Static files summary:"
echo "Total static files: $(docker exec qatreh_web find /usr/src/app/static/staticfiles/ -type f | wc -l 2>/dev/null || echo 'Unknown')"
echo "Landing page files: $(docker exec qatreh_web find /usr/src/app/static/staticfiles/landing_page/ -type f | wc -l 2>/dev/null || echo '0')"

echo ""
echo "🌐 Testing homepage access:"
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:8019/ || echo "❌ Could not access homepage"

echo ""
echo "🎉 Static files check completed!"

#!/bin/bash

# Script for collecting static files in production environment
# Usage: ./collect_static.sh

echo "🚀 Starting static files collection for production..."

# Set production environment
export DJANGO_SETTINGS_MODULE=config.settings.production

# Create necessary directories
echo "📁 Creating static directories..."
mkdir -p static/staticfiles
mkdir -p static/media

# Collect static files
echo "📦 Collecting static files..."
python manage.py collectstatic --noinput --clear

# Check if collection was successful
if [ $? -eq 0 ]; then
    echo "✅ Static files collected successfully!"
    echo "📊 Static files summary:"
    echo "   - Static files location: static/staticfiles/"
    echo "   - Media files location: static/media/"
    echo "   - Landing page assets: static/landing_page/"
    
    # Show directory sizes
    echo "📈 Directory sizes:"
    du -sh static/staticfiles/ 2>/dev/null || echo "   - staticfiles: Not found"
    du -sh static/landing_page/ 2>/dev/null || echo "   - landing_page: Not found"
    du -sh static/media/ 2>/dev/null || echo "   - media: Not found"
else
    echo "❌ Error collecting static files!"
    exit 1
fi

echo "🎉 Static files collection completed!"
